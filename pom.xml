<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.haoys.edc</groupId>
    <artifactId>edc-research-master</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <!--EDC Project-->
        <module>edc-research-center</module>
        <!--数据中心-->
        <!--<module>edc-rdr-center</module>-->
        <!--数据分析-->
        <!--<module>edc-analysis-center</module>-->
        <!--专病数据库-->
        <!--<module>edc-disease-ningxia</module>-->
        <!--新疆结核病数据库-->
        <!--<module>edc-disease-xinjiang</module>-->
        <!--健康管理-->
        <!--<module>edc-health-center</module>-->
        <!--用户中心-->
        <module>edc-user-center</module>

    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <!-- ==================== 基础配置 ==================== -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <!--<skipTests>true</skipTests>-->

        <!-- ==================== 安全漏洞修复 ==================== -->
        <!-- 修复CVE-2024-38827: Spring Security Authorization Bypass -->
        <!-- 受影响版本: 5.7.0-5.7.13, 修复版本: 5.7.14+ -->
        <spring-security.version>5.7.14</spring-security.version>
        <!-- Spring Boot 版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <!-- Spring Framework 版本 -->
        <spring-framework.version>5.3.30</spring-framework.version>

        <!-- ==================== Docker配置 ==================== -->
        <docker.host>http://*************:2375</docker.host>
        <docker.maven.plugin.version>1.2.2</docker.maven.plugin.version>

        <!-- ==================== 数据库相关 ==================== -->
        <!-- MyBatis分页插件 -->
        <pagehelper-starter.version>1.3.0</pagehelper-starter.version>
        <pagehelper.version>5.2.0</pagehelper.version>
        <!-- 数据库连接池 -->
        <druid.version>1.2.6</druid.version>
        <!-- 动态数据源 -->
        <dynamic.datasource.version>3.5.1</dynamic.datasource.version>
        <!-- MyBatis相关 -->
        <mybatis-generator.version>1.4.0</mybatis-generator.version>
        <mybatis.version>3.5.6</mybatis.version>
        <!-- 数据库驱动 -->
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <postgresql.version>42.6.2</postgresql.version>

        <!-- ==================== 工具类库 ==================== -->
        <!-- Hutool工具包 -->
        <hutool.version>5.8.39</hutool.version>
        <!-- Google Guava -->
        <guava.version>29.0-jre</guava.version>
        <!-- Apache Commons -->
        <commons.lang3.version>3.12.0</commons.lang3.version>
        <commons.io.version>2.9.0</commons.io.version>
        <commons.configuration.version>1.10</commons.configuration.version>
        <commons.fileupload.version>1.5</commons.fileupload.version>
        <!-- Lombok -->
        <lombok.version>1.18.30</lombok.version>

        <!-- ==================== 安全认证 ==================== -->
        <!-- JWT Token -->
        <jjwt.version>0.9.0</jjwt.version>

        <!-- ==================== 日志相关 ==================== -->
        <!-- Logstash -->
        <logstash-logback.version>5.3</logstash-logback.version>

        <!-- ==================== API文档 ==================== -->
        <!-- Swagger2 -->
        <swagger2.version>2.10.5</swagger2.version>
        <swagger-models.version>1.6.0</swagger-models.version>
        <swagger-annotations.version>1.6.0</swagger-annotations.version>
        <!-- Knife4j -->
        <knife4j-spring-boot-starter.version>2.0.9</knife4j-spring-boot-starter.version>
        <knife4j-spring-ui.version>2.0.9</knife4j-spring-ui.version>

        <!-- ==================== 文件存储 ==================== -->
        <!-- 阿里云OSS -->
        <aliyun-oss.version>3.12.0</aliyun-oss.version>
        <!-- 又拍云 -->
        <upyun.version>4.2.2</upyun.version>
        <!-- 七牛云 -->
        <qiniu.version>[7.2.0, 7.2.99]</qiniu.version>
        <!-- MinIO -->
        <minio.version>7.1.0</minio.version>

        <!-- ==================== Excel处理 ==================== -->
        <!-- EasyPOI -->
        <easypoi.version>4.3.0</easypoi.version>
        <!-- EasyExcel -->
        <easyExcel.version>3.0.5</easyExcel.version>

        <!-- ==================== 搜索引擎 ==================== -->
        <!-- ElasticSearch -->
        <elasticsearch.version>8.12.2</elasticsearch.version>

        <!-- ==================== 其他工具 ==================== -->
        <!-- 用户代理解析 -->
        <bitwalker.version>1.21</bitwalker.version>
        <!-- 腾讯云API -->
        <com.tencentcloudapi.version>3.1.1246</com.tencentcloudapi.version>

        <!-- ==================== 加密相关 ==================== -->
        <!-- BouncyCastle 加密库统一版本管理 -->
        <bouncycastle.version>1.68</bouncycastle.version>
        <!-- Jasypt 配置加密 -->
        <jasypt.version>3.0.4</jasypt.version>
        <!-- AOP相关 -->
        <aspectj.version>1.9.7</aspectj.version>

        <!-- ==================== 测试相关 ==================== -->
        <!-- TestContainers 版本 -->
        <testcontainers.version>1.17.6</testcontainers.version>
        <!-- Embedded Redis 版本 -->
        <embedded-redis.version>0.7.3</embedded-redis.version>
        <!-- WireMock 版本 -->
        <wiremock.version>2.27.2</wiremock.version>
    </properties>

    <dependencies>
        <!--springboot项目集成quartz依赖-->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- ==================== 内部模块版本管理 ==================== -->
            <!-- 用户中心模块 -->
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-user-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-user-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-user-security</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-user-api</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 科研中心模块 -->
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-research-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-research-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-research-quartz</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- RDR中心模块 -->
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-rdr-center-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-rdr-center-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-rdr-center-business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 宁夏疾病数据库模块 -->
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-ningxia-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-ningxia-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-ningxia-business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 新疆疾病数据库模块 -->
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-xinjiang-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-xinjiang-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.haoys.edc</groupId>
                <artifactId>edc-disease-xinjiang-business</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- ==================== AI服务相关 ==================== -->
            <!-- 百度AI SDK -->
            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>4.16.6</version>
            </dependency>
            <!-- 百度云API Explorer SDK -->
            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>api-explorer-sdk</artifactId>
                <version>*******</version>
            </dependency>

            <!-- ==================== 数据库相关 ==================== -->
            <!-- MyBatis分页插件starter -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper-starter.version}</version>
            </dependency>
            <!-- MyBatis分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <!-- ==================== 工具类库 ==================== -->
            <!-- Hutool Java工具包 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-parent</artifactId>
                <version>5.8.39</version>
            </dependency>


            <!-- ==================== API文档相关 ==================== -->
            <!-- Swagger Models - 解决NumberFormatException问题 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger-models.version}</version>
            </dependency>
            <!-- Swagger Annotations -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <!-- Springfox Swagger2 核心 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger2.version}</version>
            </dependency>
            <!-- Springfox Swagger UI -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger2.version}</version>
            </dependency>
            <!-- Springfox Spring WebMVC -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-spring-webmvc</artifactId>
                <version>${swagger2.version}</version>
            </dependency>
            <!-- Springfox 核心 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-core</artifactId>
                <version>${swagger2.version}</version>
            </dependency>
            <!-- Springfox SPI -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-spi</artifactId>
                <version>${swagger2.version}</version>
            </dependency>
            <!-- Knife4j - 增强版Swagger UI -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j-spring-boot-starter.version}</version>
            </dependency>

            <!-- MyBatis 生成器 -->
            <dependency>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-core</artifactId>
                <version>${mybatis-generator.version}</version>
            </dependency>

            <!-- MyBatis-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <!--解决myabatis版本冲突问题-->
            <!--<dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis.plus.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>-->

            <!--Mysql数据库驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!--postgresql数据库驱动-->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>



            <!--JWT(Json Web Token)登录支持-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!--集成logstash-->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback.version}</version>
            </dependency>

            <!--guava-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons.lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-configuration</groupId>
                <artifactId>commons-configuration</artifactId>
                <version>${commons.configuration.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <optional>true</optional>
            </dependency>

            <!-- 阿里云OSS -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-oss.version}</version>
            </dependency>
            <!--七牛云 SDK-->
            <dependency>
                <groupId>com.qiniu</groupId>
                <artifactId>qiniu-java-sdk</artifactId>
                <version>${qiniu.version}</version>
            </dependency>
            <!--MinIO JAVA SDK-->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <!--又拍云 SDK-->
            <dependency>
                <groupId>com.upyun</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${upyun.version}</version>
            </dependency>

            <!-- easypoi -->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-spring-boot-starter</artifactId>
                <version>${easypoi.version}</version>
            </dependency>

            <!--alibaba easyexcel-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyExcel.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>
            <!-- 文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>

            <!-- elasticsearch8.X依赖 -->
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>



        <!-- ==================== BouncyCastle 加密库版本统一管理 ==================== -->
        <!-- 解决 BouncyCastle 签名冲突问题，统一使用 1.68 版本 -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk14</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcmail-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcmail-jdk14</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk14</artifactId>
            <version>${bouncycastle.version}</version>
        </dependency>

        <!-- ==================== 配置加密库 ==================== -->
        <!-- Jasypt 配置文件加密 -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>

        <!-- ==================== PDF处理库版本管理 ==================== -->
        <!-- 修复JCE认证错误：排除iText老版本中有签名问题的BouncyCastle依赖 -->
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
            <exclusions>
                <!-- 排除老版本BouncyCastle依赖，避免JCE签名认证失败 -->
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcmail-jdk14</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>bouncycastle</groupId>
                    <artifactId>bcprov-jdk14</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- bctsp-jdk14 在新版本中已被移除，完全排除避免签名冲突 -->
        <!--<dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bctsp-jdk14</artifactId>
            <version>1.46</version>
        </dependency>-->

        <!-- ==================== 测试依赖统一管理 ==================== -->
        <!-- Spring Boot Test Starter - 包含JUnit 5, Mockito, AssertJ等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Spring Security Test - 安全测试支持 -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <version>${spring-security.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- TestContainers - 集成测试容器支持 -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- TestContainers MySQL -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mysql</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- TestContainers Redis -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>redis</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- TestContainers PostgreSQL -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- Embedded Redis - Redis测试支持 -->
        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>${embedded-redis.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- ==================== AOP相关 ==================== -->
        <!-- Spring AOP -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>

        <!-- AspectJ Weaver -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectj.version}</version>
        </dependency>

        <!-- AspectJ Runtime -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>${aspectj.version}</version>
        </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>edc-research-dev</id>
            <properties>
                <environment>dev</environment>
                <package-name>edc-research-project-dev</package-name>
            </properties>
        </profile>

        <!-- 测试环境 -->
        <profile>
            <id>edc-research-test</id>
            <properties>
                <environment>test</environment>
                <package-name>edc-research-project-test</package-name>
            </properties>
        </profile>

        <!-- remote发布环境 -->
        <profile>
            <id>edc-research-remote</id>
            <properties>
                <environment>remote</environment>
                <package-name>edc-research-project-remote</package-name>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>


        <!--客户定制功能环境-->
        <profile>
            <id>edc-research-feature</id>
            <properties>
                <environment>feature</environment>
                <package-name>edc-research-project-feature</package-name>
            </properties>
        </profile>
    </profiles>

    <build>
        <!-- 打包名称 -->
        <!--suppress UnresolvedMavenProperty -->
        <finalName>${package-name}</finalName>
        <plugins>
            <!-- 该插件的作用是用于复制指定的文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <parameters>true</parameters>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
                <executions>
                    <execution>
                        <!-- 复制配置文件 -->
                        <id>copy-resources</id>
                        <phase>package</phase>
                        <!--<goals>
                            <goal>copy-resources</goal>
                        </goals>-->
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>target/classes</directory>
                                    <includes>
                                        <!-- <include>application.yml</include>-->
                                        <include>application-prod.yml</include>
                                    </includes>
                                    <excludes>
                                        <exclude>banner.txt</exclude>
                                        <exclude>bootstrap.properties</exclude>
                                        <exclude>log4j.properties</exclude>
                                        <exclude>logback-spring.xml</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                            <outputDirectory>
                                ${project.build.directory}
                            </outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- ==================== Maven仓库配置 ==================== -->
    <repositories>
        <!-- 阿里云Maven仓库 - 优先使用 -->
        <repository>
            <id>aliyun-central</id>
            <name>Aliyun Central Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- 阿里云公共仓库 -->
        <repository>
            <id>aliyun-public</id>
            <name>Aliyun Public Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- 阿里云Spring仓库 -->
        <repository>
            <id>aliyun-spring</id>
            <name>Aliyun Spring Repository</name>
            <url>https://maven.aliyun.com/repository/spring</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Maven中央仓库 - 备用 -->
        <repository>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo1.maven.org/maven2</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <!-- 阿里云Maven插件仓库 -->
        <pluginRepository>
            <id>aliyun-plugin</id>
            <name>Aliyun Plugin Repository</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
