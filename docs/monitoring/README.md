# EDC医疗科研数据管理系统 - 监控文档

本目录包含EDC医疗科研数据管理系统的监控相关文档，涵盖异常告警、链路追踪、性能监控等内容。

## 📁 文件说明

### 🚨 异常告警系统
- **[exception-alert-implementation-summary.md](exception-alert-implementation-summary.md)** - 异常告警实施总结
  - 告警系统架构设计
  - 告警规则配置
  - 通知渠道集成
  - 实施效果评估

### 🔍 分布式追踪
- **[trace-id-implementation-summary.md](trace-id-implementation-summary.md)** - TraceId实施总结
  - 分布式追踪架构
  - TraceId生成和传递
  - 链路追踪集成
  - 问题排查优化
- **[trace-id-logging-configuration.md](trace-id-logging-configuration.md)** - TraceId日志配置
  - 日志格式配置
  - TraceId集成方案
  - 日志收集配置
  - 查询和分析方法

### 📊 性能监控
- **[performance-monitoring-dashboard.mmd](performance-monitoring-dashboard.mmd)** - 性能监控仪表板
  - 关键指标监控
  - 实时性能图表
  - 告警阈值设置
  - 趋势分析展示

## 🎯 监控体系概览

### 监控架构层次
```
EDC监控体系架构:
┌─────────────────────────────────────┐
│           监控展示层                 │
│    (Grafana仪表板、告警面板)        │
├─────────────────────────────────────┤
│           数据处理层                 │
│   (Prometheus、ElasticSearch)       │
├─────────────────────────────────────┤
│           数据收集层                 │
│  (Micrometer、Logback、Zipkin)      │
├─────────────────────────────────────┤
│           应用服务层                 │
│    (Spring Boot应用实例)            │
└─────────────────────────────────────┘
```

### 监控维度
| 监控类型 | 监控对象 | 关键指标 | 告警阈值 |
|---------|----------|----------|----------|
| 应用监控 | 业务应用 | 响应时间、吞吐量、错误率 | RT>1s, 错误率>5% |
| 系统监控 | 服务器 | CPU、内存、磁盘、网络 | CPU>80%, 内存>85% |
| 数据库监控 | MySQL/Redis | 连接数、慢查询、缓存命中率 | 连接数>80%, 慢查询>100ms |
| 业务监控 | 核心业务 | 用户活跃度、数据处理量 | 自定义业务阈值 |

## 🔧 监控工具栈

### 核心监控组件
- **Prometheus**: 指标收集和存储
- **Grafana**: 可视化仪表板
- **AlertManager**: 告警管理
- **Zipkin**: 分布式链路追踪
- **ELK Stack**: 日志收集和分析

### 应用集成组件
- **Micrometer**: 应用指标收集
- **SLF4J + Logback**: 日志框架
- **Spring Boot Actuator**: 健康检查端点
- **Custom Metrics**: 自定义业务指标

## 📊 关键监控指标

### 应用性能指标
```
核心性能指标:
├── 响应时间 (Response Time)
│   ├── 平均响应时间: < 500ms
│   ├── 95%分位响应时间: < 1s
│   └── 99%分位响应时间: < 2s
├── 吞吐量 (Throughput)
│   ├── QPS (每秒请求数): > 100
│   └── TPS (每秒事务数): > 50
├── 错误率 (Error Rate)
│   ├── HTTP 4xx错误率: < 2%
│   ├── HTTP 5xx错误率: < 1%
│   └── 业务异常率: < 0.5%
└── 可用性 (Availability)
    └── 系统可用性: > 99.9%
```

### 系统资源指标
- **CPU使用率**: 正常 < 70%, 告警 > 80%
- **内存使用率**: 正常 < 75%, 告警 > 85%
- **磁盘使用率**: 正常 < 80%, 告警 > 90%
- **网络带宽**: 监控入站和出站流量

### 业务指标
- **用户活跃度**: 在线用户数、登录成功率
- **数据处理**: 数据录入量、处理成功率
- **功能使用**: 各功能模块使用频率

## 🚨 告警机制

### 告警级别
| 级别 | 描述 | 响应时间 | 通知方式 |
|------|------|----------|----------|
| P0-紧急 | 系统不可用 | 5分钟内 | 电话+短信+邮件 |
| P1-严重 | 核心功能异常 | 15分钟内 | 短信+邮件 |
| P2-重要 | 性能下降 | 30分钟内 | 邮件+企业微信 |
| P3-一般 | 资源使用异常 | 1小时内 | 邮件 |

### 告警规则示例
```yaml
# 应用响应时间告警
- alert: HighResponseTime
  expr: http_request_duration_seconds{quantile="0.95"} > 1
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "应用响应时间过高"

# 错误率告警
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "应用错误率过高"
```

## 🔍 链路追踪

### TraceId实施方案
1. **TraceId生成**: 请求入口生成唯一TraceId
2. **上下文传递**: 通过MDC在线程间传递
3. **跨服务传递**: HTTP Header传递TraceId
4. **日志集成**: 所有日志包含TraceId
5. **链路可视化**: Zipkin展示完整调用链

### 追踪覆盖范围
- **HTTP请求**: 所有REST API调用
- **数据库操作**: MyBatis SQL执行
- **缓存操作**: Redis读写操作
- **异步任务**: 异步方法执行
- **外部调用**: 第三方服务调用

## 📈 性能监控仪表板

### 仪表板布局
```
Grafana仪表板布局:
┌─────────────────────────────────────┐
│           系统概览面板               │
│    (整体健康状态、关键指标)          │
├─────────────────────────────────────┤
│           应用性能面板               │
│  (响应时间、吞吐量、错误率趋势)      │
├─────────────────────────────────────┤
│           系统资源面板               │
│   (CPU、内存、磁盘、网络使用)        │
├─────────────────────────────────────┤
│           业务监控面板               │
│    (用户活跃、数据处理、功能使用)    │
└─────────────────────────────────────┘
```

### 关键图表
- **实时指标**: 当前系统状态
- **趋势图表**: 历史数据趋势
- **热力图**: 性能分布情况
- **TOP N**: 最慢接口、最多错误等

## 🔧 监控配置

### Prometheus配置
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'edc-research'
    static_configs:
      - targets: ['localhost:8089']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
```

### Logback配置
```xml
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
</configuration>
```

## 📋 监控最佳实践

### 指标设计原则
1. **关键指标**: 专注于业务关键指标
2. **可操作性**: 指标应该可以指导行动
3. **及时性**: 指标应该及时反映系统状态
4. **准确性**: 确保指标的准确性和一致性

### 告警设计原则
1. **避免告警疲劳**: 减少误报和无效告警
2. **分级处理**: 根据严重程度分级处理
3. **自动恢复**: 问题解决后自动恢复告警
4. **上下文信息**: 提供足够的上下文信息

## 📞 技术支持

### 监控相关问题联系
- **监控平台**: <EMAIL>
- **告警处理**: <EMAIL>
- **性能优化**: <EMAIL>
- **技术支持**: <EMAIL>

### 紧急联系
- **P0级别故障**: 24小时值班电话
- **监控平台故障**: 运维团队直线
- **数据异常**: 数据团队热线

---

**监控文档目录版本**: v1.0  
**最后更新**: 2025-06-27  
**下次审查**: 2025-07-27
