<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.rdr.mapper.PatientsMapper">
  <resultMap id="BaseResultMap" type="com.haoys.rdr.model.Patients">
    <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="patient_class" jdbcType="VARCHAR" property="patientClass" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="VARCHAR" property="gender" />
    <result column="date_of_birth" jdbcType="TIMESTAMP" property="dateOfBirth" />
    <result column="id_no" jdbcType="VARCHAR" property="idNo" />
    <result column="citizenship" jdbcType="VARCHAR" property="citizenship" />
    <result column="nation" jdbcType="VARCHAR" property="nation" />
    <result column="marriage" jdbcType="VARCHAR" property="marriage" />
    <result column="occupation" jdbcType="VARCHAR" property="occupation" />
    <result column="education_info" jdbcType="VARCHAR" property="educationInfo" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="native_place" jdbcType="VARCHAR" property="nativePlace" />
    <result column="native_place_province" jdbcType="VARCHAR" property="nativePlaceProvince" />
    <result column="native_place_city" jdbcType="VARCHAR" property="nativePlaceCity" />
    <result column="home_adress" jdbcType="VARCHAR" property="homeAdress" />
    <result column="home_adress_province" jdbcType="VARCHAR" property="homeAdressProvince" />
    <result column="home_adress_city" jdbcType="VARCHAR" property="homeAdressCity" />
    <result column="home_adress_country" jdbcType="VARCHAR" property="homeAdressCountry" />
    <result column="birth_place" jdbcType="VARCHAR" property="birthPlace" />
    <result column="birth_place_province" jdbcType="VARCHAR" property="birthPlaceProvince" />
    <result column="birth_place_city" jdbcType="VARCHAR" property="birthPlaceCity" />
    <result column="birth_place_country" jdbcType="VARCHAR" property="birthPlaceCountry" />
    <result column="registered_residence" jdbcType="VARCHAR" property="registeredResidence" />
    <result column="registered_residence_province" jdbcType="VARCHAR" property="registeredResidenceProvince" />
    <result column="registered_residence_city" jdbcType="VARCHAR" property="registeredResidenceCity" />
    <result column="registered_residence_country" jdbcType="VARCHAR" property="registeredResidenceCountry" />
    <result column="work_unit" jdbcType="VARCHAR" property="workUnit" />
    <result column="work_unit_phone" jdbcType="VARCHAR" property="workUnitPhone" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_telephone" jdbcType="VARCHAR" property="contactTelephone" />
    <result column="source_path" jdbcType="VARCHAR" property="sourcePath" />
    <result column="pk_id" jdbcType="VARCHAR" property="pkId" />
    <result column="data_state" jdbcType="VARCHAR" property="dataState" />
    <result column="patient_sn_org" jdbcType="VARCHAR" property="patientSnOrg" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "hospital_code", "patient_sn", "patient_class", "name", "gender", "date_of_birth",
    "id_no", "citizenship", "nation", "marriage", "occupation", "education_info", "mobile_phone",
    "native_place", "native_place_province", "native_place_city", "home_adress", "home_adress_province",
    "home_adress_city", "home_adress_country", "birth_place", "birth_place_province",
    "birth_place_city", "birth_place_country", "registered_residence", "registered_residence_province",
    "registered_residence_city", "registered_residence_country", "work_unit", "work_unit_phone",
    "contact_name", "contact_telephone", "source_path", "pk_id", "data_state", "patient_sn_org"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.rdr.model.PatientsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."patients"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.rdr.model.PatientsExample">
    delete from "public"."patients"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.rdr.model.Patients">
    insert into "public"."patients" ("hospital_code", "patient_sn", "patient_class",
      "name", "gender", "date_of_birth",
      "id_no", "citizenship", "nation",
      "marriage", "occupation", "education_info",
      "mobile_phone", "native_place", "native_place_province",
      "native_place_city", "home_adress", "home_adress_province",
      "home_adress_city", "home_adress_country", "birth_place",
      "birth_place_province", "birth_place_city", "birth_place_country",
      "registered_residence", "registered_residence_province",
      "registered_residence_city", "registered_residence_country",
      "work_unit", "work_unit_phone", "contact_name",
      "contact_telephone", "source_path", "pk_id",
      "data_state", "patient_sn_org")
    values (#{hospitalCode,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{patientClass,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, #{dateOfBirth,jdbcType=TIMESTAMP},
      #{idNo,jdbcType=VARCHAR}, #{citizenship,jdbcType=VARCHAR}, #{nation,jdbcType=VARCHAR},
      #{marriage,jdbcType=VARCHAR}, #{occupation,jdbcType=VARCHAR}, #{educationInfo,jdbcType=VARCHAR},
      #{mobilePhone,jdbcType=VARCHAR}, #{nativePlace,jdbcType=VARCHAR}, #{nativePlaceProvince,jdbcType=VARCHAR},
      #{nativePlaceCity,jdbcType=VARCHAR}, #{homeAdress,jdbcType=VARCHAR}, #{homeAdressProvince,jdbcType=VARCHAR},
      #{homeAdressCity,jdbcType=VARCHAR}, #{homeAdressCountry,jdbcType=VARCHAR}, #{birthPlace,jdbcType=VARCHAR},
      #{birthPlaceProvince,jdbcType=VARCHAR}, #{birthPlaceCity,jdbcType=VARCHAR}, #{birthPlaceCountry,jdbcType=VARCHAR},
      #{registeredResidence,jdbcType=VARCHAR}, #{registeredResidenceProvince,jdbcType=VARCHAR},
      #{registeredResidenceCity,jdbcType=VARCHAR}, #{registeredResidenceCountry,jdbcType=VARCHAR},
      #{workUnit,jdbcType=VARCHAR}, #{workUnitPhone,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR},
      #{contactTelephone,jdbcType=VARCHAR}, #{sourcePath,jdbcType=VARCHAR}, #{pkId,jdbcType=VARCHAR},
      #{dataState,jdbcType=VARCHAR}, #{patientSnOrg,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.rdr.model.Patients">
    insert into "public"."patients"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        "hospital_code",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="patientClass != null">
        "patient_class",
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="gender != null">
        "gender",
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth",
      </if>
      <if test="idNo != null">
        "id_no",
      </if>
      <if test="citizenship != null">
        "citizenship",
      </if>
      <if test="nation != null">
        "nation",
      </if>
      <if test="marriage != null">
        "marriage",
      </if>
      <if test="occupation != null">
        "occupation",
      </if>
      <if test="educationInfo != null">
        "education_info",
      </if>
      <if test="mobilePhone != null">
        "mobile_phone",
      </if>
      <if test="nativePlace != null">
        "native_place",
      </if>
      <if test="nativePlaceProvince != null">
        "native_place_province",
      </if>
      <if test="nativePlaceCity != null">
        "native_place_city",
      </if>
      <if test="homeAdress != null">
        "home_adress",
      </if>
      <if test="homeAdressProvince != null">
        "home_adress_province",
      </if>
      <if test="homeAdressCity != null">
        "home_adress_city",
      </if>
      <if test="homeAdressCountry != null">
        "home_adress_country",
      </if>
      <if test="birthPlace != null">
        "birth_place",
      </if>
      <if test="birthPlaceProvince != null">
        "birth_place_province",
      </if>
      <if test="birthPlaceCity != null">
        "birth_place_city",
      </if>
      <if test="birthPlaceCountry != null">
        "birth_place_country",
      </if>
      <if test="registeredResidence != null">
        "registered_residence",
      </if>
      <if test="registeredResidenceProvince != null">
        "registered_residence_province",
      </if>
      <if test="registeredResidenceCity != null">
        "registered_residence_city",
      </if>
      <if test="registeredResidenceCountry != null">
        "registered_residence_country",
      </if>
      <if test="workUnit != null">
        "work_unit",
      </if>
      <if test="workUnitPhone != null">
        "work_unit_phone",
      </if>
      <if test="contactName != null">
        "contact_name",
      </if>
      <if test="contactTelephone != null">
        "contact_telephone",
      </if>
      <if test="sourcePath != null">
        "source_path",
      </if>
      <if test="pkId != null">
        "pk_id",
      </if>
      <if test="dataState != null">
        "data_state",
      </if>
      <if test="patientSnOrg != null">
        "patient_sn_org",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hospitalCode != null">
        #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="patientClass != null">
        #{patientClass,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="idNo != null">
        #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="citizenship != null">
        #{citizenship,jdbcType=VARCHAR},
      </if>
      <if test="nation != null">
        #{nation,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        #{marriage,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="educationInfo != null">
        #{educationInfo,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="nativePlace != null">
        #{nativePlace,jdbcType=VARCHAR},
      </if>
      <if test="nativePlaceProvince != null">
        #{nativePlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="nativePlaceCity != null">
        #{nativePlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="homeAdress != null">
        #{homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressProvince != null">
        #{homeAdressProvince,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressCity != null">
        #{homeAdressCity,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressCountry != null">
        #{homeAdressCountry,jdbcType=VARCHAR},
      </if>
      <if test="birthPlace != null">
        #{birthPlace,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceProvince != null">
        #{birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCity != null">
        #{birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCountry != null">
        #{birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidence != null">
        #{registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceProvince != null">
        #{registeredResidenceProvince,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceCity != null">
        #{registeredResidenceCity,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceCountry != null">
        #{registeredResidenceCountry,jdbcType=VARCHAR},
      </if>
      <if test="workUnit != null">
        #{workUnit,jdbcType=VARCHAR},
      </if>
      <if test="workUnitPhone != null">
        #{workUnitPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null">
        #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="pkId != null">
        #{pkId,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        #{dataState,jdbcType=VARCHAR},
      </if>
      <if test="patientSnOrg != null">
        #{patientSnOrg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.rdr.model.PatientsExample" resultType="java.lang.Long">
    select count(*) from "public"."patients"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."patients"
    <set>
      <if test="record.hospitalCode != null">
        "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.patientClass != null">
        "patient_class" = #{record.patientClass,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        "name" = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gender != null">
        "gender" = #{record.gender,jdbcType=VARCHAR},
      </if>
      <if test="record.dateOfBirth != null">
        "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="record.idNo != null">
        "id_no" = #{record.idNo,jdbcType=VARCHAR},
      </if>
      <if test="record.citizenship != null">
        "citizenship" = #{record.citizenship,jdbcType=VARCHAR},
      </if>
      <if test="record.nation != null">
        "nation" = #{record.nation,jdbcType=VARCHAR},
      </if>
      <if test="record.marriage != null">
        "marriage" = #{record.marriage,jdbcType=VARCHAR},
      </if>
      <if test="record.occupation != null">
        "occupation" = #{record.occupation,jdbcType=VARCHAR},
      </if>
      <if test="record.educationInfo != null">
        "education_info" = #{record.educationInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.mobilePhone != null">
        "mobile_phone" = #{record.mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.nativePlace != null">
        "native_place" = #{record.nativePlace,jdbcType=VARCHAR},
      </if>
      <if test="record.nativePlaceProvince != null">
        "native_place_province" = #{record.nativePlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.nativePlaceCity != null">
        "native_place_city" = #{record.nativePlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdress != null">
        "home_adress" = #{record.homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdressProvince != null">
        "home_adress_province" = #{record.homeAdressProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdressCity != null">
        "home_adress_city" = #{record.homeAdressCity,jdbcType=VARCHAR},
      </if>
      <if test="record.homeAdressCountry != null">
        "home_adress_country" = #{record.homeAdressCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlace != null">
        "birth_place" = #{record.birthPlace,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceProvince != null">
        "birth_place_province" = #{record.birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceCity != null">
        "birth_place_city" = #{record.birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="record.birthPlaceCountry != null">
        "birth_place_country" = #{record.birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidence != null">
        "registered_residence" = #{record.registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidenceProvince != null">
        "registered_residence_province" = #{record.registeredResidenceProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidenceCity != null">
        "registered_residence_city" = #{record.registeredResidenceCity,jdbcType=VARCHAR},
      </if>
      <if test="record.registeredResidenceCountry != null">
        "registered_residence_country" = #{record.registeredResidenceCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.workUnit != null">
        "work_unit" = #{record.workUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.workUnitPhone != null">
        "work_unit_phone" = #{record.workUnitPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.contactName != null">
        "contact_name" = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactTelephone != null">
        "contact_telephone" = #{record.contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.sourcePath != null">
        "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="record.pkId != null">
        "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataState != null">
        "data_state" = #{record.dataState,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSnOrg != null">
        "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."patients"
    set "hospital_code" = #{record.hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "patient_class" = #{record.patientClass,jdbcType=VARCHAR},
      "name" = #{record.name,jdbcType=VARCHAR},
      "gender" = #{record.gender,jdbcType=VARCHAR},
      "date_of_birth" = #{record.dateOfBirth,jdbcType=TIMESTAMP},
      "id_no" = #{record.idNo,jdbcType=VARCHAR},
      "citizenship" = #{record.citizenship,jdbcType=VARCHAR},
      "nation" = #{record.nation,jdbcType=VARCHAR},
      "marriage" = #{record.marriage,jdbcType=VARCHAR},
      "occupation" = #{record.occupation,jdbcType=VARCHAR},
      "education_info" = #{record.educationInfo,jdbcType=VARCHAR},
      "mobile_phone" = #{record.mobilePhone,jdbcType=VARCHAR},
      "native_place" = #{record.nativePlace,jdbcType=VARCHAR},
      "native_place_province" = #{record.nativePlaceProvince,jdbcType=VARCHAR},
      "native_place_city" = #{record.nativePlaceCity,jdbcType=VARCHAR},
      "home_adress" = #{record.homeAdress,jdbcType=VARCHAR},
      "home_adress_province" = #{record.homeAdressProvince,jdbcType=VARCHAR},
      "home_adress_city" = #{record.homeAdressCity,jdbcType=VARCHAR},
      "home_adress_country" = #{record.homeAdressCountry,jdbcType=VARCHAR},
      "birth_place" = #{record.birthPlace,jdbcType=VARCHAR},
      "birth_place_province" = #{record.birthPlaceProvince,jdbcType=VARCHAR},
      "birth_place_city" = #{record.birthPlaceCity,jdbcType=VARCHAR},
      "birth_place_country" = #{record.birthPlaceCountry,jdbcType=VARCHAR},
      "registered_residence" = #{record.registeredResidence,jdbcType=VARCHAR},
      "registered_residence_province" = #{record.registeredResidenceProvince,jdbcType=VARCHAR},
      "registered_residence_city" = #{record.registeredResidenceCity,jdbcType=VARCHAR},
      "registered_residence_country" = #{record.registeredResidenceCountry,jdbcType=VARCHAR},
      "work_unit" = #{record.workUnit,jdbcType=VARCHAR},
      "work_unit_phone" = #{record.workUnitPhone,jdbcType=VARCHAR},
      "contact_name" = #{record.contactName,jdbcType=VARCHAR},
      "contact_telephone" = #{record.contactTelephone,jdbcType=VARCHAR},
      "source_path" = #{record.sourcePath,jdbcType=VARCHAR},
      "pk_id" = #{record.pkId,jdbcType=VARCHAR},
      "data_state" = #{record.dataState,jdbcType=VARCHAR},
      "patient_sn_org" = #{record.patientSnOrg,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.rdr.model.Patients">
    update "public"."patients"
    <set>
      <if test="hospitalCode != null">
        "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="patientClass != null">
        "patient_class" = #{patientClass,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        "gender" = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="dateOfBirth != null">
        "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      </if>
      <if test="idNo != null">
        "id_no" = #{idNo,jdbcType=VARCHAR},
      </if>
      <if test="citizenship != null">
        "citizenship" = #{citizenship,jdbcType=VARCHAR},
      </if>
      <if test="nation != null">
        "nation" = #{nation,jdbcType=VARCHAR},
      </if>
      <if test="marriage != null">
        "marriage" = #{marriage,jdbcType=VARCHAR},
      </if>
      <if test="occupation != null">
        "occupation" = #{occupation,jdbcType=VARCHAR},
      </if>
      <if test="nativePlace != null">
        "native_place" = #{nativePlace,jdbcType=VARCHAR},
      </if>
      <if test="nativePlaceProvince != null">
        "native_place_province" = #{nativePlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="nativePlaceCity != null">
        "native_place_city" = #{nativePlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="homeAdress != null">
        "home_adress" = #{homeAdress,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressProvince != null">
        "home_adress_province" = #{homeAdressProvince,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressCity != null">
        "home_adress_city" = #{homeAdressCity,jdbcType=VARCHAR},
      </if>
      <if test="homeAdressCountry != null">
        "home_adress_country" = #{homeAdressCountry,jdbcType=VARCHAR},
      </if>
      <if test="birthPlace != null">
        "birth_place" = #{birthPlace,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceProvince != null">
        "birth_place_province" = #{birthPlaceProvince,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCity != null">
        "birth_place_city" = #{birthPlaceCity,jdbcType=VARCHAR},
      </if>
      <if test="birthPlaceCountry != null">
        "birth_place_country" = #{birthPlaceCountry,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidence != null">
        "registered_residence" = #{registeredResidence,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceProvince != null">
        "registered_residence_province" = #{registeredResidenceProvince,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceCity != null">
        "registered_residence_city" = #{registeredResidenceCity,jdbcType=VARCHAR},
      </if>
      <if test="registeredResidenceCountry != null">
        "registered_residence_country" = #{registeredResidenceCountry,jdbcType=VARCHAR},
      </if>
      <if test="workUnit != null">
        "work_unit" = #{workUnit,jdbcType=VARCHAR},
      </if>
      <if test="workUnitPhone != null">
        "work_unit_phone" = #{workUnitPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        "contact_name" = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactTelephone != null">
        "contact_telephone" = #{contactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="contactRelationship != null">
        "contact_relationship" = #{contactRelationship,jdbcType=VARCHAR},
      </if>
      <if test="sourcePath != null">
        "source_path" = #{sourcePath,jdbcType=VARCHAR},
      </if>
      <if test="dataState != null">
        "data_state" = #{dataState,jdbcType=VARCHAR},
      </if>
    </set>
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.rdr.model.Patients">
    update "public"."patients"
    set "hospital_code" = #{hospitalCode,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "patient_class" = #{patientClass,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      "gender" = #{gender,jdbcType=VARCHAR},
      "date_of_birth" = #{dateOfBirth,jdbcType=TIMESTAMP},
      "id_no" = #{idNo,jdbcType=VARCHAR},
      "citizenship" = #{citizenship,jdbcType=VARCHAR},
      "nation" = #{nation,jdbcType=VARCHAR},
      "marriage" = #{marriage,jdbcType=VARCHAR},
      "occupation" = #{occupation,jdbcType=VARCHAR},
      "native_place" = #{nativePlace,jdbcType=VARCHAR},
      "native_place_province" = #{nativePlaceProvince,jdbcType=VARCHAR},
      "native_place_city" = #{nativePlaceCity,jdbcType=VARCHAR},
      "home_adress" = #{homeAdress,jdbcType=VARCHAR},
      "home_adress_province" = #{homeAdressProvince,jdbcType=VARCHAR},
      "home_adress_city" = #{homeAdressCity,jdbcType=VARCHAR},
      "home_adress_country" = #{homeAdressCountry,jdbcType=VARCHAR},
      "birth_place" = #{birthPlace,jdbcType=VARCHAR},
      "birth_place_province" = #{birthPlaceProvince,jdbcType=VARCHAR},
      "birth_place_city" = #{birthPlaceCity,jdbcType=VARCHAR},
      "birth_place_country" = #{birthPlaceCountry,jdbcType=VARCHAR},
      "registered_residence" = #{registeredResidence,jdbcType=VARCHAR},
      "registered_residence_province" = #{registeredResidenceProvince,jdbcType=VARCHAR},
      "registered_residence_city" = #{registeredResidenceCity,jdbcType=VARCHAR},
      "registered_residence_country" = #{registeredResidenceCountry,jdbcType=VARCHAR},
      "work_unit" = #{workUnit,jdbcType=VARCHAR},
      "work_unit_phone" = #{workUnitPhone,jdbcType=VARCHAR},
      "contact_name" = #{contactName,jdbcType=VARCHAR},
      "contact_telephone" = #{contactTelephone,jdbcType=VARCHAR},
      "contact_relationship" = #{contactRelationship,jdbcType=VARCHAR},
      "source_path" = #{sourcePath,jdbcType=VARCHAR},
      "data_state" = #{dataState,jdbcType=VARCHAR}
    where "pk_id" = #{pkId,jdbcType=VARCHAR}
  </update>

  <select id="getPatientById" resultMap="BaseResultMap">
    select * from patients where patient_sn = #{patientSn} limit 1
  </select>

  <select id="count" resultType="java.lang.Long">
    select count(distinct patients.patient_sn) from patients
    <if test="dataBaseId != null and dataBaseId != ''">
      join rdr_patient_data_base_record  on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id=cast(#{dataBaseId} as text )
    </if>
  </select>

  <select id="countPgSqlPatientToAge" resultType="com.haoys.rdr.domain.vo.CountPatientToAgeVo">
    SELECT SUBSTRING
    ( now() :: TEXT FROM 1 FOR 4 ) :: INTEGER - SUBSTRING (date_of_birth :: TEXT FROM 1 FOR 4 ) :: INTEGER AS age ,
    count(0) as num
    FROM
    patients
    <if test="dataBaseId != null and dataBaseId != ''">
      join rdr_patient_data_base_record  on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id=cast(#{dataBaseId} as text )
    </if>
    GROUP BY age
  </select>

  <select id="getPatientsCount" resultType="int">
    SELECT COUNT(DISTINCT patient_sn) FROM patients
  </select>

  <select id="countBySex" resultType="java.lang.Long">
    select count(0) from patients
    <if test="dataBaseId != null and dataBaseId != ''">
      join rdr_patient_data_base_record  on rdr_patient_data_base_record.patient_sn=patients.patient_sn and rdr_patient_data_base_record.data_base_id=cast(#{dataBaseId} as text )
    </if>
    where 1=1 and patients.gender=#{code}
  </select>

  <select id="getPatientSnList" resultType="java.lang.String">
    select patient_sn from patients group by patient_sn order by patient_sn
  </select>

</mapper>
