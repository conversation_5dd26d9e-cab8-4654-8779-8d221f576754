package com.haoys.rdr.mapper;

import com.haoys.rdr.model.EmrInformedConsent;
import com.haoys.rdr.model.EmrInformedConsentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EmrInformedConsentMapper {
    long countByExample(EmrInformedConsentExample example);

    int deleteByExample(EmrInformedConsentExample example);

    int deleteByPrimaryKey(String pkId);

    int insert(EmrInformedConsent record);

    int insertSelective(EmrInformedConsent record);

    List<EmrInformedConsent> selectByExample(EmrInformedConsentExample example);

    EmrInformedConsent selectByPrimaryKey(String pkId);

    int updateByExampleSelective(@Param("record") EmrInformedConsent record, @Param("example") EmrInformedConsentExample example);

    int updateByExample(@Param("record") EmrInformedConsent record, @Param("example") EmrInformedConsentExample example);

    int updateByPrimaryKeySelective(EmrInformedConsent record);

    int updateByPrimaryKey(EmrInformedConsent record);
}