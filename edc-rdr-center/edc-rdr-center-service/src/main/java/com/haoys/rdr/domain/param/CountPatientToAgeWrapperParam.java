package com.haoys.rdr.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CountPatientToAgeWrapperParam implements Serializable {

    @ApiModelProperty(value = "数据库id")
    private String dataBaseId;

    List<CountPatientToAgeParam> params = new ArrayList<>();

}
