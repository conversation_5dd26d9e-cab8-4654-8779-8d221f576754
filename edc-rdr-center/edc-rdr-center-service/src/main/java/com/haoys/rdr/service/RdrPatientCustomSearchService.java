package com.haoys.rdr.service;


import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.rdr.domain.param.SearchParam;
import com.haoys.rdr.model.RdrPatientCustomSearch;

import java.util.List;

/**
 * 纳排搜索
 */

public interface RdrPatientCustomSearchService {

    /**
     * 保存或者更新搜索条件
     * @return
     */
    CommonResult<Object> saveOrUpdateSearch(RdrPatientCustomSearch search);

    /**
     * 列表
     * @return
     */
    CommonPage<List<RdrPatientCustomSearch>> searchList(String dataBaseId, Integer pageNum, Integer pageSize);


    /**
     * 根据id进行删除
     * @param id 纳排搜索条件的id
     * @return
     */
    CommonResult<Object> remove(String id);

    /**
     * 根据id获取
     * @param id 搜索条件的id
     * @return
     */
    RdrPatientCustomSearch getById(String id);

    /**
     * 根据搜索条件进行查询
     * @param param 搜索和显示信息
     * @return
     */
    CommonResult list(SearchParam param);


}
