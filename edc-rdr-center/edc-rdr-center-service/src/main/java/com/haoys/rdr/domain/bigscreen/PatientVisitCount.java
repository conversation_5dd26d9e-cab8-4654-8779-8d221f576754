package com.haoys.rdr.domain.bigscreen;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PatientVisitCount {
    
    @ApiModelProperty(value = "统计日期")
    private String dateCode;
    
    @ApiModelProperty(value = "患者登记表入库总病例人数")
    private long patientsCount;
    
    @ApiModelProperty(value = "统计就诊表总病例人数")
    private int totalCount;
    
    @ApiModelProperty(value = "统计就诊表门诊就诊人数")
    private int outpVisitCount;
    
    @ApiModelProperty(value = "统计就诊表急诊就诊人数")
    private int emergencyCount;
    
    @ApiModelProperty(value = "统计住院就诊人数")
    private int inpVisitCount;
    
    private List<PatientGenderCount> patientGenderCountList;
    
    private GenderPercent genderPercent;
    
    @Data
    public static class PatientGenderCount {
        @ApiModelProperty(value = "性别")
        private String gender;
        @ApiModelProperty(value = "数量")
        private int count;
    }
    
    @Data
    public static class GenderPercent {
        
        @ApiModelProperty(value = "男性数量")
        private int maleCount;
        
        @ApiModelProperty(value = "女性数量")
        private int femaleCount;
    
    }
    
    
    
}
