package com.haoys.rdr.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class RdrPatientModelRecord implements Serializable {
    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "患者id")
    private String patientId;

    @ApiModelProperty(value = "表单code")
    private String modelSourceCode;

    @ApiModelProperty(value = "表单名称")
    private String modelSourceName;

    @ApiModelProperty(value = "患者数据JSON")
    private Object content;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getModelSourceCode() {
        return modelSourceCode;
    }

    public void setModelSourceCode(String modelSourceCode) {
        this.modelSourceCode = modelSourceCode;
    }

    public String getModelSourceName() {
        return modelSourceName;
    }

    public void setModelSourceName(String modelSourceName) {
        this.modelSourceName = modelSourceName;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", patientId=").append(patientId);
        sb.append(", modelSourceCode=").append(modelSourceCode);
        sb.append(", modelSourceName=").append(modelSourceName);
        sb.append(", content=").append(content);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}