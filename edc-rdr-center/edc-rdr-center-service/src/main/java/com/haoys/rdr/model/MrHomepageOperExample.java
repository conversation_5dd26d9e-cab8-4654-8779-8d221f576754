package com.haoys.rdr.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MrHomepageOperExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MrHomepageOperExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andHospitalCodeIsNull() {
            addCriterion("\"hospital_code\" is null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIsNotNull() {
            addCriterion("\"hospital_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeEqualTo(String value) {
            addCriterion("\"hospital_code\" =", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotEqualTo(String value) {
            addCriterion("\"hospital_code\" <>", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThan(String value) {
            addCriterion("\"hospital_code\" >", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" >=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThan(String value) {
            addCriterion("\"hospital_code\" <", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLessThanOrEqualTo(String value) {
            addCriterion("\"hospital_code\" <=", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeLike(String value) {
            addCriterion("\"hospital_code\" like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotLike(String value) {
            addCriterion("\"hospital_code\" not like", value, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeIn(List<String> values) {
            addCriterion("\"hospital_code\" in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotIn(List<String> values) {
            addCriterion("\"hospital_code\" not in", values, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andHospitalCodeNotBetween(String value1, String value2) {
            addCriterion("\"hospital_code\" not between", value1, value2, "hospitalCode");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNull() {
            addCriterion("\"patient_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnIsNotNull() {
            addCriterion("\"patient_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnEqualTo(String value) {
            addCriterion("\"patient_sn\" =", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotEqualTo(String value) {
            addCriterion("\"patient_sn\" <>", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThan(String value) {
            addCriterion("\"patient_sn\" >", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" >=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThan(String value) {
            addCriterion("\"patient_sn\" <", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn\" <=", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnLike(String value) {
            addCriterion("\"patient_sn\" like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotLike(String value) {
            addCriterion("\"patient_sn\" not like", value, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnIn(List<String> values) {
            addCriterion("\"patient_sn\" in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotIn(List<String> values) {
            addCriterion("\"patient_sn\" not in", values, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andPatientSnNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn\" not between", value1, value2, "patientSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNull() {
            addCriterion("\"visit_sn\" is null");
            return (Criteria) this;
        }

        public Criteria andVisitSnIsNotNull() {
            addCriterion("\"visit_sn\" is not null");
            return (Criteria) this;
        }

        public Criteria andVisitSnEqualTo(String value) {
            addCriterion("\"visit_sn\" =", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotEqualTo(String value) {
            addCriterion("\"visit_sn\" <>", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThan(String value) {
            addCriterion("\"visit_sn\" >", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnGreaterThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" >=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThan(String value) {
            addCriterion("\"visit_sn\" <", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLessThanOrEqualTo(String value) {
            addCriterion("\"visit_sn\" <=", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnLike(String value) {
            addCriterion("\"visit_sn\" like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotLike(String value) {
            addCriterion("\"visit_sn\" not like", value, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnIn(List<String> values) {
            addCriterion("\"visit_sn\" in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotIn(List<String> values) {
            addCriterion("\"visit_sn\" not in", values, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andVisitSnNotBetween(String value1, String value2) {
            addCriterion("\"visit_sn\" not between", value1, value2, "visitSn");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNull() {
            addCriterion("\"tpatno\" is null");
            return (Criteria) this;
        }

        public Criteria andTpatnoIsNotNull() {
            addCriterion("\"tpatno\" is not null");
            return (Criteria) this;
        }

        public Criteria andTpatnoEqualTo(String value) {
            addCriterion("\"tpatno\" =", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotEqualTo(String value) {
            addCriterion("\"tpatno\" <>", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThan(String value) {
            addCriterion("\"tpatno\" >", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoGreaterThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" >=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThan(String value) {
            addCriterion("\"tpatno\" <", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLessThanOrEqualTo(String value) {
            addCriterion("\"tpatno\" <=", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoLike(String value) {
            addCriterion("\"tpatno\" like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotLike(String value) {
            addCriterion("\"tpatno\" not like", value, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoIn(List<String> values) {
            addCriterion("\"tpatno\" in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotIn(List<String> values) {
            addCriterion("\"tpatno\" not in", values, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoBetween(String value1, String value2) {
            addCriterion("\"tpatno\" between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andTpatnoNotBetween(String value1, String value2) {
            addCriterion("\"tpatno\" not between", value1, value2, "tpatno");
            return (Criteria) this;
        }

        public Criteria andOperationNoIsNull() {
            addCriterion("\"operation_no\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationNoIsNotNull() {
            addCriterion("\"operation_no\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNoEqualTo(String value) {
            addCriterion("\"operation_no\" =", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotEqualTo(String value) {
            addCriterion("\"operation_no\" <>", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoGreaterThan(String value) {
            addCriterion("\"operation_no\" >", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_no\" >=", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLessThan(String value) {
            addCriterion("\"operation_no\" <", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLessThanOrEqualTo(String value) {
            addCriterion("\"operation_no\" <=", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoLike(String value) {
            addCriterion("\"operation_no\" like", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotLike(String value) {
            addCriterion("\"operation_no\" not like", value, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoIn(List<String> values) {
            addCriterion("\"operation_no\" in", values, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotIn(List<String> values) {
            addCriterion("\"operation_no\" not in", values, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoBetween(String value1, String value2) {
            addCriterion("\"operation_no\" between", value1, value2, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationNoNotBetween(String value1, String value2) {
            addCriterion("\"operation_no\" not between", value1, value2, "operationNo");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIsNull() {
            addCriterion("\"operation_code\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIsNotNull() {
            addCriterion("\"operation_code\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationCodeEqualTo(String value) {
            addCriterion("\"operation_code\" =", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotEqualTo(String value) {
            addCriterion("\"operation_code\" <>", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeGreaterThan(String value) {
            addCriterion("\"operation_code\" >", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_code\" >=", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLessThan(String value) {
            addCriterion("\"operation_code\" <", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLessThanOrEqualTo(String value) {
            addCriterion("\"operation_code\" <=", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeLike(String value) {
            addCriterion("\"operation_code\" like", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotLike(String value) {
            addCriterion("\"operation_code\" not like", value, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeIn(List<String> values) {
            addCriterion("\"operation_code\" in", values, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotIn(List<String> values) {
            addCriterion("\"operation_code\" not in", values, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeBetween(String value1, String value2) {
            addCriterion("\"operation_code\" between", value1, value2, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationCodeNotBetween(String value1, String value2) {
            addCriterion("\"operation_code\" not between", value1, value2, "operationCode");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIsNull() {
            addCriterion("\"operation_datetime\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIsNotNull() {
            addCriterion("\"operation_datetime\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeEqualTo(Date value) {
            addCriterion("\"operation_datetime\" =", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotEqualTo(Date value) {
            addCriterion("\"operation_datetime\" <>", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeGreaterThan(Date value) {
            addCriterion("\"operation_datetime\" >", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("\"operation_datetime\" >=", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeLessThan(Date value) {
            addCriterion("\"operation_datetime\" <", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeLessThanOrEqualTo(Date value) {
            addCriterion("\"operation_datetime\" <=", value, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeIn(List<Date> values) {
            addCriterion("\"operation_datetime\" in", values, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotIn(List<Date> values) {
            addCriterion("\"operation_datetime\" not in", values, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeBetween(Date value1, Date value2) {
            addCriterion("\"operation_datetime\" between", value1, value2, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationDatetimeNotBetween(Date value1, Date value2) {
            addCriterion("\"operation_datetime\" not between", value1, value2, "operationDatetime");
            return (Criteria) this;
        }

        public Criteria andOperationLevelIsNull() {
            addCriterion("\"operation_level\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationLevelIsNotNull() {
            addCriterion("\"operation_level\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationLevelEqualTo(String value) {
            addCriterion("\"operation_level\" =", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelNotEqualTo(String value) {
            addCriterion("\"operation_level\" <>", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelGreaterThan(String value) {
            addCriterion("\"operation_level\" >", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_level\" >=", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelLessThan(String value) {
            addCriterion("\"operation_level\" <", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelLessThanOrEqualTo(String value) {
            addCriterion("\"operation_level\" <=", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelLike(String value) {
            addCriterion("\"operation_level\" like", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelNotLike(String value) {
            addCriterion("\"operation_level\" not like", value, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelIn(List<String> values) {
            addCriterion("\"operation_level\" in", values, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelNotIn(List<String> values) {
            addCriterion("\"operation_level\" not in", values, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelBetween(String value1, String value2) {
            addCriterion("\"operation_level\" between", value1, value2, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationLevelNotBetween(String value1, String value2) {
            addCriterion("\"operation_level\" not between", value1, value2, "operationLevel");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNull() {
            addCriterion("\"operation_name\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationNameIsNotNull() {
            addCriterion("\"operation_name\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationNameEqualTo(String value) {
            addCriterion("\"operation_name\" =", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotEqualTo(String value) {
            addCriterion("\"operation_name\" <>", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThan(String value) {
            addCriterion("\"operation_name\" >", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" >=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThan(String value) {
            addCriterion("\"operation_name\" <", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLessThanOrEqualTo(String value) {
            addCriterion("\"operation_name\" <=", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameLike(String value) {
            addCriterion("\"operation_name\" like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotLike(String value) {
            addCriterion("\"operation_name\" not like", value, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameIn(List<String> values) {
            addCriterion("\"operation_name\" in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotIn(List<String> values) {
            addCriterion("\"operation_name\" not in", values, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameBetween(String value1, String value2) {
            addCriterion("\"operation_name\" between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationNameNotBetween(String value1, String value2) {
            addCriterion("\"operation_name\" not between", value1, value2, "operationName");
            return (Criteria) this;
        }

        public Criteria andOperationPartIsNull() {
            addCriterion("\"operation_part\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationPartIsNotNull() {
            addCriterion("\"operation_part\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPartEqualTo(String value) {
            addCriterion("\"operation_part\" =", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartNotEqualTo(String value) {
            addCriterion("\"operation_part\" <>", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartGreaterThan(String value) {
            addCriterion("\"operation_part\" >", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_part\" >=", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartLessThan(String value) {
            addCriterion("\"operation_part\" <", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartLessThanOrEqualTo(String value) {
            addCriterion("\"operation_part\" <=", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartLike(String value) {
            addCriterion("\"operation_part\" like", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartNotLike(String value) {
            addCriterion("\"operation_part\" not like", value, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartIn(List<String> values) {
            addCriterion("\"operation_part\" in", values, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartNotIn(List<String> values) {
            addCriterion("\"operation_part\" not in", values, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartBetween(String value1, String value2) {
            addCriterion("\"operation_part\" between", value1, value2, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationPartNotBetween(String value1, String value2) {
            addCriterion("\"operation_part\" not between", value1, value2, "operationPart");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNull() {
            addCriterion("\"operation_duration\" is null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIsNotNull() {
            addCriterion("\"operation_duration\" is not null");
            return (Criteria) this;
        }

        public Criteria andOperationDurationEqualTo(String value) {
            addCriterion("\"operation_duration\" =", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotEqualTo(String value) {
            addCriterion("\"operation_duration\" <>", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThan(String value) {
            addCriterion("\"operation_duration\" >", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationGreaterThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" >=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThan(String value) {
            addCriterion("\"operation_duration\" <", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLessThanOrEqualTo(String value) {
            addCriterion("\"operation_duration\" <=", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationLike(String value) {
            addCriterion("\"operation_duration\" like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotLike(String value) {
            addCriterion("\"operation_duration\" not like", value, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationIn(List<String> values) {
            addCriterion("\"operation_duration\" in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotIn(List<String> values) {
            addCriterion("\"operation_duration\" not in", values, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andOperationDurationNotBetween(String value1, String value2) {
            addCriterion("\"operation_duration\" not between", value1, value2, "operationDuration");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIsNull() {
            addCriterion("\"surgeon_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIsNotNull() {
            addCriterion("\"surgeon_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" =", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" <>", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorGreaterThan(String value) {
            addCriterion("\"surgeon_doctor\" >", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" >=", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLessThan(String value) {
            addCriterion("\"surgeon_doctor\" <", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"surgeon_doctor\" <=", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorLike(String value) {
            addCriterion("\"surgeon_doctor\" like", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotLike(String value) {
            addCriterion("\"surgeon_doctor\" not like", value, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorIn(List<String> values) {
            addCriterion("\"surgeon_doctor\" in", values, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotIn(List<String> values) {
            addCriterion("\"surgeon_doctor\" not in", values, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorBetween(String value1, String value2) {
            addCriterion("\"surgeon_doctor\" between", value1, value2, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andSurgeonDoctorNotBetween(String value1, String value2) {
            addCriterion("\"surgeon_doctor\" not between", value1, value2, "surgeonDoctor");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIsNull() {
            addCriterion("\"first_assistant\" is null");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIsNotNull() {
            addCriterion("\"first_assistant\" is not null");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantEqualTo(String value) {
            addCriterion("\"first_assistant\" =", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotEqualTo(String value) {
            addCriterion("\"first_assistant\" <>", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantGreaterThan(String value) {
            addCriterion("\"first_assistant\" >", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantGreaterThanOrEqualTo(String value) {
            addCriterion("\"first_assistant\" >=", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLessThan(String value) {
            addCriterion("\"first_assistant\" <", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLessThanOrEqualTo(String value) {
            addCriterion("\"first_assistant\" <=", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantLike(String value) {
            addCriterion("\"first_assistant\" like", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotLike(String value) {
            addCriterion("\"first_assistant\" not like", value, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantIn(List<String> values) {
            addCriterion("\"first_assistant\" in", values, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotIn(List<String> values) {
            addCriterion("\"first_assistant\" not in", values, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantBetween(String value1, String value2) {
            addCriterion("\"first_assistant\" between", value1, value2, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andFirstAssistantNotBetween(String value1, String value2) {
            addCriterion("\"first_assistant\" not between", value1, value2, "firstAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIsNull() {
            addCriterion("\"second_assistant\" is null");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIsNotNull() {
            addCriterion("\"second_assistant\" is not null");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantEqualTo(String value) {
            addCriterion("\"second_assistant\" =", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotEqualTo(String value) {
            addCriterion("\"second_assistant\" <>", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantGreaterThan(String value) {
            addCriterion("\"second_assistant\" >", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantGreaterThanOrEqualTo(String value) {
            addCriterion("\"second_assistant\" >=", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLessThan(String value) {
            addCriterion("\"second_assistant\" <", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLessThanOrEqualTo(String value) {
            addCriterion("\"second_assistant\" <=", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantLike(String value) {
            addCriterion("\"second_assistant\" like", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotLike(String value) {
            addCriterion("\"second_assistant\" not like", value, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantIn(List<String> values) {
            addCriterion("\"second_assistant\" in", values, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotIn(List<String> values) {
            addCriterion("\"second_assistant\" not in", values, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantBetween(String value1, String value2) {
            addCriterion("\"second_assistant\" between", value1, value2, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andSecondAssistantNotBetween(String value1, String value2) {
            addCriterion("\"second_assistant\" not between", value1, value2, "secondAssistant");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNull() {
            addCriterion("\"anesthesia_method\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIsNotNull() {
            addCriterion("\"anesthesia_method\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodEqualTo(String value) {
            addCriterion("\"anesthesia_method\" =", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <>", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThan(String value) {
            addCriterion("\"anesthesia_method\" >", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" >=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThan(String value) {
            addCriterion("\"anesthesia_method\" <", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_method\" <=", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodLike(String value) {
            addCriterion("\"anesthesia_method\" like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotLike(String value) {
            addCriterion("\"anesthesia_method\" not like", value, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodIn(List<String> values) {
            addCriterion("\"anesthesia_method\" in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotIn(List<String> values) {
            addCriterion("\"anesthesia_method\" not in", values, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaMethodNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_method\" not between", value1, value2, "anesthesiaMethod");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelIsNull() {
            addCriterion("\"anesthesia_level\" is null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelIsNotNull() {
            addCriterion("\"anesthesia_level\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelEqualTo(String value) {
            addCriterion("\"anesthesia_level\" =", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelNotEqualTo(String value) {
            addCriterion("\"anesthesia_level\" <>", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelGreaterThan(String value) {
            addCriterion("\"anesthesia_level\" >", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelGreaterThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_level\" >=", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelLessThan(String value) {
            addCriterion("\"anesthesia_level\" <", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelLessThanOrEqualTo(String value) {
            addCriterion("\"anesthesia_level\" <=", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelLike(String value) {
            addCriterion("\"anesthesia_level\" like", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelNotLike(String value) {
            addCriterion("\"anesthesia_level\" not like", value, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelIn(List<String> values) {
            addCriterion("\"anesthesia_level\" in", values, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelNotIn(List<String> values) {
            addCriterion("\"anesthesia_level\" not in", values, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelBetween(String value1, String value2) {
            addCriterion("\"anesthesia_level\" between", value1, value2, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andAnesthesiaLevelNotBetween(String value1, String value2) {
            addCriterion("\"anesthesia_level\" not between", value1, value2, "anesthesiaLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelIsNull() {
            addCriterion("\"surgical_incision_level\" is null");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelIsNotNull() {
            addCriterion("\"surgical_incision_level\" is not null");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelEqualTo(String value) {
            addCriterion("\"surgical_incision_level\" =", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelNotEqualTo(String value) {
            addCriterion("\"surgical_incision_level\" <>", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelGreaterThan(String value) {
            addCriterion("\"surgical_incision_level\" >", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelGreaterThanOrEqualTo(String value) {
            addCriterion("\"surgical_incision_level\" >=", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelLessThan(String value) {
            addCriterion("\"surgical_incision_level\" <", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelLessThanOrEqualTo(String value) {
            addCriterion("\"surgical_incision_level\" <=", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelLike(String value) {
            addCriterion("\"surgical_incision_level\" like", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelNotLike(String value) {
            addCriterion("\"surgical_incision_level\" not like", value, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelIn(List<String> values) {
            addCriterion("\"surgical_incision_level\" in", values, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelNotIn(List<String> values) {
            addCriterion("\"surgical_incision_level\" not in", values, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelBetween(String value1, String value2) {
            addCriterion("\"surgical_incision_level\" between", value1, value2, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andSurgicalIncisionLevelNotBetween(String value1, String value2) {
            addCriterion("\"surgical_incision_level\" not between", value1, value2, "surgicalIncisionLevel");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIsNull() {
            addCriterion("\"anaesthesia_doctor\" is null");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIsNotNull() {
            addCriterion("\"anaesthesia_doctor\" is not null");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" =", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" <>", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorGreaterThan(String value) {
            addCriterion("\"anaesthesia_doctor\" >", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorGreaterThanOrEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" >=", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLessThan(String value) {
            addCriterion("\"anaesthesia_doctor\" <", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLessThanOrEqualTo(String value) {
            addCriterion("\"anaesthesia_doctor\" <=", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorLike(String value) {
            addCriterion("\"anaesthesia_doctor\" like", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotLike(String value) {
            addCriterion("\"anaesthesia_doctor\" not like", value, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorIn(List<String> values) {
            addCriterion("\"anaesthesia_doctor\" in", values, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotIn(List<String> values) {
            addCriterion("\"anaesthesia_doctor\" not in", values, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorBetween(String value1, String value2) {
            addCriterion("\"anaesthesia_doctor\" between", value1, value2, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andAnaesthesiaDoctorNotBetween(String value1, String value2) {
            addCriterion("\"anaesthesia_doctor\" not between", value1, value2, "anaesthesiaDoctor");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNull() {
            addCriterion("\"source_path\" is null");
            return (Criteria) this;
        }

        public Criteria andSourcePathIsNotNull() {
            addCriterion("\"source_path\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourcePathEqualTo(String value) {
            addCriterion("\"source_path\" =", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotEqualTo(String value) {
            addCriterion("\"source_path\" <>", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThan(String value) {
            addCriterion("\"source_path\" >", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathGreaterThanOrEqualTo(String value) {
            addCriterion("\"source_path\" >=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThan(String value) {
            addCriterion("\"source_path\" <", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLessThanOrEqualTo(String value) {
            addCriterion("\"source_path\" <=", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathLike(String value) {
            addCriterion("\"source_path\" like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotLike(String value) {
            addCriterion("\"source_path\" not like", value, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathIn(List<String> values) {
            addCriterion("\"source_path\" in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotIn(List<String> values) {
            addCriterion("\"source_path\" not in", values, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathBetween(String value1, String value2) {
            addCriterion("\"source_path\" between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andSourcePathNotBetween(String value1, String value2) {
            addCriterion("\"source_path\" not between", value1, value2, "sourcePath");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNull() {
            addCriterion("\"pk_id\" is null");
            return (Criteria) this;
        }

        public Criteria andPkIdIsNotNull() {
            addCriterion("\"pk_id\" is not null");
            return (Criteria) this;
        }

        public Criteria andPkIdEqualTo(String value) {
            addCriterion("\"pk_id\" =", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotEqualTo(String value) {
            addCriterion("\"pk_id\" <>", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThan(String value) {
            addCriterion("\"pk_id\" >", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdGreaterThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" >=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThan(String value) {
            addCriterion("\"pk_id\" <", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLessThanOrEqualTo(String value) {
            addCriterion("\"pk_id\" <=", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdLike(String value) {
            addCriterion("\"pk_id\" like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotLike(String value) {
            addCriterion("\"pk_id\" not like", value, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdIn(List<String> values) {
            addCriterion("\"pk_id\" in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotIn(List<String> values) {
            addCriterion("\"pk_id\" not in", values, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdBetween(String value1, String value2) {
            addCriterion("\"pk_id\" between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andPkIdNotBetween(String value1, String value2) {
            addCriterion("\"pk_id\" not between", value1, value2, "pkId");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNull() {
            addCriterion("\"data_state\" is null");
            return (Criteria) this;
        }

        public Criteria andDataStateIsNotNull() {
            addCriterion("\"data_state\" is not null");
            return (Criteria) this;
        }

        public Criteria andDataStateEqualTo(String value) {
            addCriterion("\"data_state\" =", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotEqualTo(String value) {
            addCriterion("\"data_state\" <>", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThan(String value) {
            addCriterion("\"data_state\" >", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateGreaterThanOrEqualTo(String value) {
            addCriterion("\"data_state\" >=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThan(String value) {
            addCriterion("\"data_state\" <", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLessThanOrEqualTo(String value) {
            addCriterion("\"data_state\" <=", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateLike(String value) {
            addCriterion("\"data_state\" like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotLike(String value) {
            addCriterion("\"data_state\" not like", value, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateIn(List<String> values) {
            addCriterion("\"data_state\" in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotIn(List<String> values) {
            addCriterion("\"data_state\" not in", values, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateBetween(String value1, String value2) {
            addCriterion("\"data_state\" between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andDataStateNotBetween(String value1, String value2) {
            addCriterion("\"data_state\" not between", value1, value2, "dataState");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNull() {
            addCriterion("\"patient_sn_org\" is null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIsNotNull() {
            addCriterion("\"patient_sn_org\" is not null");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgEqualTo(String value) {
            addCriterion("\"patient_sn_org\" =", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <>", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThan(String value) {
            addCriterion("\"patient_sn_org\" >", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgGreaterThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" >=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThan(String value) {
            addCriterion("\"patient_sn_org\" <", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLessThanOrEqualTo(String value) {
            addCriterion("\"patient_sn_org\" <=", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgLike(String value) {
            addCriterion("\"patient_sn_org\" like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotLike(String value) {
            addCriterion("\"patient_sn_org\" not like", value, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgIn(List<String> values) {
            addCriterion("\"patient_sn_org\" in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotIn(List<String> values) {
            addCriterion("\"patient_sn_org\" not in", values, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }

        public Criteria andPatientSnOrgNotBetween(String value1, String value2) {
            addCriterion("\"patient_sn_org\" not between", value1, value2, "patientSnOrg");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}