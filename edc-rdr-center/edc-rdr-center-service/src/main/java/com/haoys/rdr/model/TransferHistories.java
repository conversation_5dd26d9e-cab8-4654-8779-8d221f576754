package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class TransferHistories implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号/门诊号")
    private String visitSn;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "转科类别")
    private String transferType;

    @ApiModelProperty(value = "转入科室")
    private String transferedToDept;

    @ApiModelProperty(value = "转入病区")
    private String transferedToWard;

    @ApiModelProperty(value = "转入床号")
    private String transferedToBed;

    @ApiModelProperty(value = "转科/转入时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferedEndTime;

    @ApiModelProperty(value = "转出科室")
    private String transferedFromDept;

    @ApiModelProperty(value = "转出病区")
    private String transferedFromWard;

    @ApiModelProperty(value = "转出床号")
    private String transferedFromBed;

    @ApiModelProperty(value = "转出时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transferedStartTime;

    @ApiModelProperty(value = "转科原因")
    private String reasonOfTransfer;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getTransferType() {
        return transferType;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    public String getTransferedToDept() {
        return transferedToDept;
    }

    public void setTransferedToDept(String transferedToDept) {
        this.transferedToDept = transferedToDept;
    }

    public String getTransferedToWard() {
        return transferedToWard;
    }

    public void setTransferedToWard(String transferedToWard) {
        this.transferedToWard = transferedToWard;
    }

    public String getTransferedToBed() {
        return transferedToBed;
    }

    public void setTransferedToBed(String transferedToBed) {
        this.transferedToBed = transferedToBed;
    }

    public Date getTransferedEndTime() {
        return transferedEndTime;
    }

    public void setTransferedEndTime(Date transferedEndTime) {
        this.transferedEndTime = transferedEndTime;
    }

    public String getTransferedFromDept() {
        return transferedFromDept;
    }

    public void setTransferedFromDept(String transferedFromDept) {
        this.transferedFromDept = transferedFromDept;
    }

    public String getTransferedFromWard() {
        return transferedFromWard;
    }

    public void setTransferedFromWard(String transferedFromWard) {
        this.transferedFromWard = transferedFromWard;
    }

    public String getTransferedFromBed() {
        return transferedFromBed;
    }

    public void setTransferedFromBed(String transferedFromBed) {
        this.transferedFromBed = transferedFromBed;
    }

    public Date getTransferedStartTime() {
        return transferedStartTime;
    }

    public void setTransferedStartTime(Date transferedStartTime) {
        this.transferedStartTime = transferedStartTime;
    }

    public String getReasonOfTransfer() {
        return reasonOfTransfer;
    }

    public void setReasonOfTransfer(String reasonOfTransfer) {
        this.reasonOfTransfer = reasonOfTransfer;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", transferType=").append(transferType);
        sb.append(", transferedToDept=").append(transferedToDept);
        sb.append(", transferedToWard=").append(transferedToWard);
        sb.append(", transferedToBed=").append(transferedToBed);
        sb.append(", transferedEndTime=").append(transferedEndTime);
        sb.append(", transferedFromDept=").append(transferedFromDept);
        sb.append(", transferedFromWard=").append(transferedFromWard);
        sb.append(", transferedFromBed=").append(transferedFromBed);
        sb.append(", transferedStartTime=").append(transferedStartTime);
        sb.append(", reasonOfTransfer=").append(reasonOfTransfer);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}