package com.haoys.rdr.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class MrHomepage implements Serializable {
    @ApiModelProperty(value = "医疗机构代码")
    private String hospitalCode;

    @ApiModelProperty(value = "机构名称")
    private String hospitalName;

    @ApiModelProperty(value = "医疗保险手册（卡）号")
    private String insuranceCardNo;

    @ApiModelProperty(value = "健康卡号")
    private String healthCardNo;

    @ApiModelProperty(value = "医疗付款方式")
    private String payWay;

    @ApiModelProperty(value = "住院次")
    private Integer admissionNumber;

    @ApiModelProperty(value = "病案号")
    private String tpatno;

    @ApiModelProperty(value = "患者ID")
    private String patientSn;

    @ApiModelProperty(value = "住院号")
    private String visitSn;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "出生日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateOfBirth;

    @ApiModelProperty(value = "年龄")
    private String age;

    @ApiModelProperty(value = "婚姻状况")
    private String marriage;

    @ApiModelProperty(value = "职业")
    private String occupation;

    @ApiModelProperty(value = "出生地址")
    private String birthPlace;

    @ApiModelProperty(value = "出生省份")
    private String birthPlaceProvince;

    @ApiModelProperty(value = "出生地市")
    private String birthPlaceCity;

    @ApiModelProperty(value = "出生地县")
    private String birthPlaceCountry;

    @ApiModelProperty(value = "民族")
    private String nation;

    @ApiModelProperty(value = "国籍")
    private String citizenship;

    @ApiModelProperty(value = "身份证号")
    private String idNo;

    @ApiModelProperty(value = "现住址")
    private String homeAdress;

    @ApiModelProperty(value = "住宅电话")
    private String homePhone;

    @ApiModelProperty(value = "现住址邮政编码")
    private String homeAdressPostcode;

    @ApiModelProperty(value = "工作单位及地址")
    private String workUnitAndAdress;

    @ApiModelProperty(value = "电话")
    private String contactTelephone;

    @ApiModelProperty(value = "工作单位邮政编码")
    private String workUnitPostcode;

    @ApiModelProperty(value = "户口地址")
    private String registeredResidence;

    @ApiModelProperty(value = "户口所在地邮政编码")
    private String registeredResidencePostcode;

    @ApiModelProperty(value = "联系人姓名")
    private String contactName;

    @ApiModelProperty(value = "关系")
    private String contactRelationship;

    @ApiModelProperty(value = "联系人地址")
    private String contactAdress;

    @ApiModelProperty(value = "联系人电话")
    private String contactPhone;

    @ApiModelProperty(value = "入院途径")
    private String admissionWay;

    @ApiModelProperty(value = "入院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date admissionDateTime;

    @ApiModelProperty(value = "入院科别")
    private String deptAdmissionTo;

    @ApiModelProperty(value = "入院病室")
    private String wardAdmissionTo;

    @ApiModelProperty(value = "转科科别")
    private String deptTransferFrom;

    @ApiModelProperty(value = "出院日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dischargeDateTime;

    @ApiModelProperty(value = "出院科别")
    private String deptDischargeFrom;

    @ApiModelProperty(value = "出院病室")
    private String wardDischargeFrom;

    @ApiModelProperty(value = "实际住院天数")
    private Integer inDays;

    @ApiModelProperty(value = "医院感染总次数")
    private Integer infectedTimes;

    @ApiModelProperty(value = "损伤、中毒的外部因素编码")
    private String injuryPoisoningCode;

    @ApiModelProperty(value = "损伤、中毒的外部因素名称")
    private String injuryPoisoningCauses;

    @ApiModelProperty(value = "是否过敏")
    private String isAllergic;

    @ApiModelProperty(value = "过敏药物名称")
    private String allergenDrug;

    @ApiModelProperty(value = "HBsAg检查结果")
    private String hbsag;

    @ApiModelProperty(value = "HCV-Ab检查结果")
    private String hcvAb;

    @ApiModelProperty(value = "HIV-Ab检查结果")
    private String hivAb;

    @ApiModelProperty(value = "门诊与出院诊断符合情况")
    private String outpDisDiagConformity;

    @ApiModelProperty(value = "入院与出院诊断符合情况")
    private String admitDisDiagConformity;

    @ApiModelProperty(value = "术前与术后诊断符合情况")
    private String preopPostopDiagConformity;

    @ApiModelProperty(value = "临床与病理诊断符合情况")
    private String clinicPathoDiagConformity;

    @ApiModelProperty(value = "放射与病理诊断符合情况")
    private String radioPathoDiagConformity;

    @ApiModelProperty(value = "抢救次数")
    private Integer rescueTimes;

    @ApiModelProperty(value = "抢救成功次数")
    private Integer rescueSuccessTimes;

    @ApiModelProperty(value = "最高诊断依据")
    private String diagBasis;

    @ApiModelProperty(value = "分化程度")
    private String differentiationDegree;

    @ApiModelProperty(value = "科主任")
    private String director;

    @ApiModelProperty(value = "主(副主)任医师")
    private String chiefDoctor;

    @ApiModelProperty(value = "主治医师")
    private String attendingDoctor;

    @ApiModelProperty(value = "住院医师")
    private String residentDoctor;

    @ApiModelProperty(value = "责任护士")
    private String responsibleNurse;

    @ApiModelProperty(value = "进修医师")
    private String traineeDoctor;

    @ApiModelProperty(value = "研究生实习医师")
    private String graduateInternDoctor;

    @ApiModelProperty(value = "实习医师")
    private String internDoctor;

    @ApiModelProperty(value = "编码员")
    private String coderName;

    @ApiModelProperty(value = "病案质量")
    private String mrQuality;

    @ApiModelProperty(value = "质控医师")
    private String qualityControlDoctor;

    @ApiModelProperty(value = "质控护师")
    private String qualityControlNurse;

    @ApiModelProperty(value = "质控日期")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date qualityConfirmDatetime;

    @ApiModelProperty(value = "特级护理天数")
    private Integer specialNursingDays;

    @ApiModelProperty(value = "一级护理天数")
    private Integer firstGradeNursingDays;

    @ApiModelProperty(value = "二级护理天数")
    private Integer secondGradeNursingDays;

    @ApiModelProperty(value = "三级护理天数")
    private Integer thirdGradeNursingDays;

    @ApiModelProperty(value = "重症监护室名称")
    private String icuName;

    @ApiModelProperty(value = "入ICU时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date icuInDatetime;

    @ApiModelProperty(value = "出ICU时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date icuOutDatetime;

    @ApiModelProperty(value = "死亡患者尸检")
    private String deathPatientAutopsy;

    @ApiModelProperty(value = "手术、治疗、检查、诊断 为本院第一例")
    private String firstExampleInHospital;

    @ApiModelProperty(value = "手术患者类型")
    private String operationPatientType;

    @ApiModelProperty(value = "随诊")
    private String followUp;

    @ApiModelProperty(value = "随诊周数")
    private Double followUpWeeks;

    @ApiModelProperty(value = "随诊月数")
    private Double followUpMonths;

    @ApiModelProperty(value = "随诊年数")
    private Double followUpYears;

    @ApiModelProperty(value = "示教病例")
    private String demonstrationCase;

    @ApiModelProperty(value = "ABO血型")
    private String aboBloodType;

    @ApiModelProperty(value = "Rh血型")
    private String rhBloodType;

    @ApiModelProperty(value = "输血反应")
    private String adverseReaction;

    @ApiModelProperty(value = "红细胞")
    private Double erythrocyte;

    @ApiModelProperty(value = "血小板")
    private Double platelet;

    @ApiModelProperty(value = "血浆")
    private Double plasma;

    @ApiModelProperty(value = "全血")
    private Double wholeBlood;

    @ApiModelProperty(value = "自体回收")
    private String autologousBloodCallback;

    @ApiModelProperty(value = "其它")
    private Double othersBlood;

    @ApiModelProperty(value = "（年龄不足1周岁的）年龄")
    private String ageUnderOneYear;

    @ApiModelProperty(value = "新生儿出生体重")
    private Double newbornWeight;

    @ApiModelProperty(value = "新生儿入院体重")
    private Double newbornAdmitWeight;

    @ApiModelProperty(value = "入院前多少小时(颅脑损伤患者昏迷时间)")
    private Double comaHoursBeforeAdmit;

    @ApiModelProperty(value = "入院前多少分钟(颅脑损伤患者昏迷时间)")
    private Double comaMinutesBeforeAdmit;

    @ApiModelProperty(value = "入院后多少小时(颅脑损伤患者昏迷时间)")
    private Double comaHoursAfterAdmit;

    @ApiModelProperty(value = "入院后多少分钟(颅脑损伤患者昏迷时间)")
    private Double comaMinutesAfterAdmit;

    @ApiModelProperty(value = "呼吸机使用时间")
    private Double respiratorUsingTime;

    @ApiModelProperty(value = "是否有出院31天内再住院计划")
    private String readmitPlanInThirtyDays;

    @ApiModelProperty(value = "出院31天再住院计划目的")
    private String readmitReasonInThirtyDays;

    @ApiModelProperty(value = "离院方式")
    private String dischargeWay;

    @ApiModelProperty(value = "转入医院名称")
    private String thansferToHospital;

    @ApiModelProperty(value = "溯源路径")
    private String sourcePath;

    @ApiModelProperty(value = "院内唯一id")
    private String pkId;

    @ApiModelProperty(value = "数据状态")
    private String dataState;

    @ApiModelProperty(value = "ICU天数")
    private Integer icuDays;

    @ApiModelProperty(value = "CCU天数")
    private Integer ccuDays;

    @ApiModelProperty(value = "结核治疗类型")
    private String tbPatientTreatmrnt;

    @ApiModelProperty(value = "结核耐药类型")
    private String tbResistanceType;

    @ApiModelProperty(value = "结核痰培养")
    private String tbSputumCulture;

    @ApiModelProperty(value = "结核痰涂片")
    private String tbSputumSmear;

    @ApiModelProperty(value = "原始患者ID")
    private String patientSnOrg;

    private static final long serialVersionUID = 1L;

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }

    public String getHospitalName() {
        return hospitalName;
    }

    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    public String getInsuranceCardNo() {
        return insuranceCardNo;
    }

    public void setInsuranceCardNo(String insuranceCardNo) {
        this.insuranceCardNo = insuranceCardNo;
    }

    public String getHealthCardNo() {
        return healthCardNo;
    }

    public void setHealthCardNo(String healthCardNo) {
        this.healthCardNo = healthCardNo;
    }

    public String getPayWay() {
        return payWay;
    }

    public void setPayWay(String payWay) {
        this.payWay = payWay;
    }

    public Integer getAdmissionNumber() {
        return admissionNumber;
    }

    public void setAdmissionNumber(Integer admissionNumber) {
        this.admissionNumber = admissionNumber;
    }

    public String getTpatno() {
        return tpatno;
    }

    public void setTpatno(String tpatno) {
        this.tpatno = tpatno;
    }

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getBirthPlaceProvince() {
        return birthPlaceProvince;
    }

    public void setBirthPlaceProvince(String birthPlaceProvince) {
        this.birthPlaceProvince = birthPlaceProvince;
    }

    public String getBirthPlaceCity() {
        return birthPlaceCity;
    }

    public void setBirthPlaceCity(String birthPlaceCity) {
        this.birthPlaceCity = birthPlaceCity;
    }

    public String getBirthPlaceCountry() {
        return birthPlaceCountry;
    }

    public void setBirthPlaceCountry(String birthPlaceCountry) {
        this.birthPlaceCountry = birthPlaceCountry;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCitizenship() {
        return citizenship;
    }

    public void setCitizenship(String citizenship) {
        this.citizenship = citizenship;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getHomeAdress() {
        return homeAdress;
    }

    public void setHomeAdress(String homeAdress) {
        this.homeAdress = homeAdress;
    }

    public String getHomePhone() {
        return homePhone;
    }

    public void setHomePhone(String homePhone) {
        this.homePhone = homePhone;
    }

    public String getHomeAdressPostcode() {
        return homeAdressPostcode;
    }

    public void setHomeAdressPostcode(String homeAdressPostcode) {
        this.homeAdressPostcode = homeAdressPostcode;
    }

    public String getWorkUnitAndAdress() {
        return workUnitAndAdress;
    }

    public void setWorkUnitAndAdress(String workUnitAndAdress) {
        this.workUnitAndAdress = workUnitAndAdress;
    }

    public String getContactTelephone() {
        return contactTelephone;
    }

    public void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

    public String getWorkUnitPostcode() {
        return workUnitPostcode;
    }

    public void setWorkUnitPostcode(String workUnitPostcode) {
        this.workUnitPostcode = workUnitPostcode;
    }

    public String getRegisteredResidence() {
        return registeredResidence;
    }

    public void setRegisteredResidence(String registeredResidence) {
        this.registeredResidence = registeredResidence;
    }

    public String getRegisteredResidencePostcode() {
        return registeredResidencePostcode;
    }

    public void setRegisteredResidencePostcode(String registeredResidencePostcode) {
        this.registeredResidencePostcode = registeredResidencePostcode;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactRelationship() {
        return contactRelationship;
    }

    public void setContactRelationship(String contactRelationship) {
        this.contactRelationship = contactRelationship;
    }

    public String getContactAdress() {
        return contactAdress;
    }

    public void setContactAdress(String contactAdress) {
        this.contactAdress = contactAdress;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getAdmissionWay() {
        return admissionWay;
    }

    public void setAdmissionWay(String admissionWay) {
        this.admissionWay = admissionWay;
    }

    public Date getAdmissionDateTime() {
        return admissionDateTime;
    }

    public void setAdmissionDateTime(Date admissionDateTime) {
        this.admissionDateTime = admissionDateTime;
    }

    public String getDeptAdmissionTo() {
        return deptAdmissionTo;
    }

    public void setDeptAdmissionTo(String deptAdmissionTo) {
        this.deptAdmissionTo = deptAdmissionTo;
    }

    public String getWardAdmissionTo() {
        return wardAdmissionTo;
    }

    public void setWardAdmissionTo(String wardAdmissionTo) {
        this.wardAdmissionTo = wardAdmissionTo;
    }

    public String getDeptTransferFrom() {
        return deptTransferFrom;
    }

    public void setDeptTransferFrom(String deptTransferFrom) {
        this.deptTransferFrom = deptTransferFrom;
    }

    public Date getDischargeDateTime() {
        return dischargeDateTime;
    }

    public void setDischargeDateTime(Date dischargeDateTime) {
        this.dischargeDateTime = dischargeDateTime;
    }

    public String getDeptDischargeFrom() {
        return deptDischargeFrom;
    }

    public void setDeptDischargeFrom(String deptDischargeFrom) {
        this.deptDischargeFrom = deptDischargeFrom;
    }

    public String getWardDischargeFrom() {
        return wardDischargeFrom;
    }

    public void setWardDischargeFrom(String wardDischargeFrom) {
        this.wardDischargeFrom = wardDischargeFrom;
    }

    public Integer getInDays() {
        return inDays;
    }

    public void setInDays(Integer inDays) {
        this.inDays = inDays;
    }

    public Integer getInfectedTimes() {
        return infectedTimes;
    }

    public void setInfectedTimes(Integer infectedTimes) {
        this.infectedTimes = infectedTimes;
    }

    public String getInjuryPoisoningCode() {
        return injuryPoisoningCode;
    }

    public void setInjuryPoisoningCode(String injuryPoisoningCode) {
        this.injuryPoisoningCode = injuryPoisoningCode;
    }

    public String getInjuryPoisoningCauses() {
        return injuryPoisoningCauses;
    }

    public void setInjuryPoisoningCauses(String injuryPoisoningCauses) {
        this.injuryPoisoningCauses = injuryPoisoningCauses;
    }

    public String getIsAllergic() {
        return isAllergic;
    }

    public void setIsAllergic(String isAllergic) {
        this.isAllergic = isAllergic;
    }

    public String getAllergenDrug() {
        return allergenDrug;
    }

    public void setAllergenDrug(String allergenDrug) {
        this.allergenDrug = allergenDrug;
    }

    public String getHbsag() {
        return hbsag;
    }

    public void setHbsag(String hbsag) {
        this.hbsag = hbsag;
    }

    public String getHcvAb() {
        return hcvAb;
    }

    public void setHcvAb(String hcvAb) {
        this.hcvAb = hcvAb;
    }

    public String getHivAb() {
        return hivAb;
    }

    public void setHivAb(String hivAb) {
        this.hivAb = hivAb;
    }

    public String getOutpDisDiagConformity() {
        return outpDisDiagConformity;
    }

    public void setOutpDisDiagConformity(String outpDisDiagConformity) {
        this.outpDisDiagConformity = outpDisDiagConformity;
    }

    public String getAdmitDisDiagConformity() {
        return admitDisDiagConformity;
    }

    public void setAdmitDisDiagConformity(String admitDisDiagConformity) {
        this.admitDisDiagConformity = admitDisDiagConformity;
    }

    public String getPreopPostopDiagConformity() {
        return preopPostopDiagConformity;
    }

    public void setPreopPostopDiagConformity(String preopPostopDiagConformity) {
        this.preopPostopDiagConformity = preopPostopDiagConformity;
    }

    public String getClinicPathoDiagConformity() {
        return clinicPathoDiagConformity;
    }

    public void setClinicPathoDiagConformity(String clinicPathoDiagConformity) {
        this.clinicPathoDiagConformity = clinicPathoDiagConformity;
    }

    public String getRadioPathoDiagConformity() {
        return radioPathoDiagConformity;
    }

    public void setRadioPathoDiagConformity(String radioPathoDiagConformity) {
        this.radioPathoDiagConformity = radioPathoDiagConformity;
    }

    public Integer getRescueTimes() {
        return rescueTimes;
    }

    public void setRescueTimes(Integer rescueTimes) {
        this.rescueTimes = rescueTimes;
    }

    public Integer getRescueSuccessTimes() {
        return rescueSuccessTimes;
    }

    public void setRescueSuccessTimes(Integer rescueSuccessTimes) {
        this.rescueSuccessTimes = rescueSuccessTimes;
    }

    public String getDiagBasis() {
        return diagBasis;
    }

    public void setDiagBasis(String diagBasis) {
        this.diagBasis = diagBasis;
    }

    public String getDifferentiationDegree() {
        return differentiationDegree;
    }

    public void setDifferentiationDegree(String differentiationDegree) {
        this.differentiationDegree = differentiationDegree;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public String getChiefDoctor() {
        return chiefDoctor;
    }

    public void setChiefDoctor(String chiefDoctor) {
        this.chiefDoctor = chiefDoctor;
    }

    public String getAttendingDoctor() {
        return attendingDoctor;
    }

    public void setAttendingDoctor(String attendingDoctor) {
        this.attendingDoctor = attendingDoctor;
    }

    public String getResidentDoctor() {
        return residentDoctor;
    }

    public void setResidentDoctor(String residentDoctor) {
        this.residentDoctor = residentDoctor;
    }

    public String getResponsibleNurse() {
        return responsibleNurse;
    }

    public void setResponsibleNurse(String responsibleNurse) {
        this.responsibleNurse = responsibleNurse;
    }

    public String getTraineeDoctor() {
        return traineeDoctor;
    }

    public void setTraineeDoctor(String traineeDoctor) {
        this.traineeDoctor = traineeDoctor;
    }

    public String getGraduateInternDoctor() {
        return graduateInternDoctor;
    }

    public void setGraduateInternDoctor(String graduateInternDoctor) {
        this.graduateInternDoctor = graduateInternDoctor;
    }

    public String getInternDoctor() {
        return internDoctor;
    }

    public void setInternDoctor(String internDoctor) {
        this.internDoctor = internDoctor;
    }

    public String getCoderName() {
        return coderName;
    }

    public void setCoderName(String coderName) {
        this.coderName = coderName;
    }

    public String getMrQuality() {
        return mrQuality;
    }

    public void setMrQuality(String mrQuality) {
        this.mrQuality = mrQuality;
    }

    public String getQualityControlDoctor() {
        return qualityControlDoctor;
    }

    public void setQualityControlDoctor(String qualityControlDoctor) {
        this.qualityControlDoctor = qualityControlDoctor;
    }

    public String getQualityControlNurse() {
        return qualityControlNurse;
    }

    public void setQualityControlNurse(String qualityControlNurse) {
        this.qualityControlNurse = qualityControlNurse;
    }

    public Date getQualityConfirmDatetime() {
        return qualityConfirmDatetime;
    }

    public void setQualityConfirmDatetime(Date qualityConfirmDatetime) {
        this.qualityConfirmDatetime = qualityConfirmDatetime;
    }

    public Integer getSpecialNursingDays() {
        return specialNursingDays;
    }

    public void setSpecialNursingDays(Integer specialNursingDays) {
        this.specialNursingDays = specialNursingDays;
    }

    public Integer getFirstGradeNursingDays() {
        return firstGradeNursingDays;
    }

    public void setFirstGradeNursingDays(Integer firstGradeNursingDays) {
        this.firstGradeNursingDays = firstGradeNursingDays;
    }

    public Integer getSecondGradeNursingDays() {
        return secondGradeNursingDays;
    }

    public void setSecondGradeNursingDays(Integer secondGradeNursingDays) {
        this.secondGradeNursingDays = secondGradeNursingDays;
    }

    public Integer getThirdGradeNursingDays() {
        return thirdGradeNursingDays;
    }

    public void setThirdGradeNursingDays(Integer thirdGradeNursingDays) {
        this.thirdGradeNursingDays = thirdGradeNursingDays;
    }

    public String getIcuName() {
        return icuName;
    }

    public void setIcuName(String icuName) {
        this.icuName = icuName;
    }

    public Date getIcuInDatetime() {
        return icuInDatetime;
    }

    public void setIcuInDatetime(Date icuInDatetime) {
        this.icuInDatetime = icuInDatetime;
    }

    public Date getIcuOutDatetime() {
        return icuOutDatetime;
    }

    public void setIcuOutDatetime(Date icuOutDatetime) {
        this.icuOutDatetime = icuOutDatetime;
    }

    public String getDeathPatientAutopsy() {
        return deathPatientAutopsy;
    }

    public void setDeathPatientAutopsy(String deathPatientAutopsy) {
        this.deathPatientAutopsy = deathPatientAutopsy;
    }

    public String getFirstExampleInHospital() {
        return firstExampleInHospital;
    }

    public void setFirstExampleInHospital(String firstExampleInHospital) {
        this.firstExampleInHospital = firstExampleInHospital;
    }

    public String getOperationPatientType() {
        return operationPatientType;
    }

    public void setOperationPatientType(String operationPatientType) {
        this.operationPatientType = operationPatientType;
    }

    public String getFollowUp() {
        return followUp;
    }

    public void setFollowUp(String followUp) {
        this.followUp = followUp;
    }

    public Double getFollowUpWeeks() {
        return followUpWeeks;
    }

    public void setFollowUpWeeks(Double followUpWeeks) {
        this.followUpWeeks = followUpWeeks;
    }

    public Double getFollowUpMonths() {
        return followUpMonths;
    }

    public void setFollowUpMonths(Double followUpMonths) {
        this.followUpMonths = followUpMonths;
    }

    public Double getFollowUpYears() {
        return followUpYears;
    }

    public void setFollowUpYears(Double followUpYears) {
        this.followUpYears = followUpYears;
    }

    public String getDemonstrationCase() {
        return demonstrationCase;
    }

    public void setDemonstrationCase(String demonstrationCase) {
        this.demonstrationCase = demonstrationCase;
    }

    public String getAboBloodType() {
        return aboBloodType;
    }

    public void setAboBloodType(String aboBloodType) {
        this.aboBloodType = aboBloodType;
    }

    public String getRhBloodType() {
        return rhBloodType;
    }

    public void setRhBloodType(String rhBloodType) {
        this.rhBloodType = rhBloodType;
    }

    public String getAdverseReaction() {
        return adverseReaction;
    }

    public void setAdverseReaction(String adverseReaction) {
        this.adverseReaction = adverseReaction;
    }

    public Double getErythrocyte() {
        return erythrocyte;
    }

    public void setErythrocyte(Double erythrocyte) {
        this.erythrocyte = erythrocyte;
    }

    public Double getPlatelet() {
        return platelet;
    }

    public void setPlatelet(Double platelet) {
        this.platelet = platelet;
    }

    public Double getPlasma() {
        return plasma;
    }

    public void setPlasma(Double plasma) {
        this.plasma = plasma;
    }

    public Double getWholeBlood() {
        return wholeBlood;
    }

    public void setWholeBlood(Double wholeBlood) {
        this.wholeBlood = wholeBlood;
    }

    public String getAutologousBloodCallback() {
        return autologousBloodCallback;
    }

    public void setAutologousBloodCallback(String autologousBloodCallback) {
        this.autologousBloodCallback = autologousBloodCallback;
    }

    public Double getOthersBlood() {
        return othersBlood;
    }

    public void setOthersBlood(Double othersBlood) {
        this.othersBlood = othersBlood;
    }

    public String getAgeUnderOneYear() {
        return ageUnderOneYear;
    }

    public void setAgeUnderOneYear(String ageUnderOneYear) {
        this.ageUnderOneYear = ageUnderOneYear;
    }

    public Double getNewbornWeight() {
        return newbornWeight;
    }

    public void setNewbornWeight(Double newbornWeight) {
        this.newbornWeight = newbornWeight;
    }

    public Double getNewbornAdmitWeight() {
        return newbornAdmitWeight;
    }

    public void setNewbornAdmitWeight(Double newbornAdmitWeight) {
        this.newbornAdmitWeight = newbornAdmitWeight;
    }

    public Double getComaHoursBeforeAdmit() {
        return comaHoursBeforeAdmit;
    }

    public void setComaHoursBeforeAdmit(Double comaHoursBeforeAdmit) {
        this.comaHoursBeforeAdmit = comaHoursBeforeAdmit;
    }

    public Double getComaMinutesBeforeAdmit() {
        return comaMinutesBeforeAdmit;
    }

    public void setComaMinutesBeforeAdmit(Double comaMinutesBeforeAdmit) {
        this.comaMinutesBeforeAdmit = comaMinutesBeforeAdmit;
    }

    public Double getComaHoursAfterAdmit() {
        return comaHoursAfterAdmit;
    }

    public void setComaHoursAfterAdmit(Double comaHoursAfterAdmit) {
        this.comaHoursAfterAdmit = comaHoursAfterAdmit;
    }

    public Double getComaMinutesAfterAdmit() {
        return comaMinutesAfterAdmit;
    }

    public void setComaMinutesAfterAdmit(Double comaMinutesAfterAdmit) {
        this.comaMinutesAfterAdmit = comaMinutesAfterAdmit;
    }

    public Double getRespiratorUsingTime() {
        return respiratorUsingTime;
    }

    public void setRespiratorUsingTime(Double respiratorUsingTime) {
        this.respiratorUsingTime = respiratorUsingTime;
    }

    public String getReadmitPlanInThirtyDays() {
        return readmitPlanInThirtyDays;
    }

    public void setReadmitPlanInThirtyDays(String readmitPlanInThirtyDays) {
        this.readmitPlanInThirtyDays = readmitPlanInThirtyDays;
    }

    public String getReadmitReasonInThirtyDays() {
        return readmitReasonInThirtyDays;
    }

    public void setReadmitReasonInThirtyDays(String readmitReasonInThirtyDays) {
        this.readmitReasonInThirtyDays = readmitReasonInThirtyDays;
    }

    public String getDischargeWay() {
        return dischargeWay;
    }

    public void setDischargeWay(String dischargeWay) {
        this.dischargeWay = dischargeWay;
    }

    public String getThansferToHospital() {
        return thansferToHospital;
    }

    public void setThansferToHospital(String thansferToHospital) {
        this.thansferToHospital = thansferToHospital;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPkId() {
        return pkId;
    }

    public void setPkId(String pkId) {
        this.pkId = pkId;
    }

    public String getDataState() {
        return dataState;
    }

    public void setDataState(String dataState) {
        this.dataState = dataState;
    }

    public Integer getIcuDays() {
        return icuDays;
    }

    public void setIcuDays(Integer icuDays) {
        this.icuDays = icuDays;
    }

    public Integer getCcuDays() {
        return ccuDays;
    }

    public void setCcuDays(Integer ccuDays) {
        this.ccuDays = ccuDays;
    }

    public String getTbPatientTreatmrnt() {
        return tbPatientTreatmrnt;
    }

    public void setTbPatientTreatmrnt(String tbPatientTreatmrnt) {
        this.tbPatientTreatmrnt = tbPatientTreatmrnt;
    }

    public String getTbResistanceType() {
        return tbResistanceType;
    }

    public void setTbResistanceType(String tbResistanceType) {
        this.tbResistanceType = tbResistanceType;
    }

    public String getTbSputumCulture() {
        return tbSputumCulture;
    }

    public void setTbSputumCulture(String tbSputumCulture) {
        this.tbSputumCulture = tbSputumCulture;
    }

    public String getTbSputumSmear() {
        return tbSputumSmear;
    }

    public void setTbSputumSmear(String tbSputumSmear) {
        this.tbSputumSmear = tbSputumSmear;
    }

    public String getPatientSnOrg() {
        return patientSnOrg;
    }

    public void setPatientSnOrg(String patientSnOrg) {
        this.patientSnOrg = patientSnOrg;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", hospitalCode=").append(hospitalCode);
        sb.append(", hospitalName=").append(hospitalName);
        sb.append(", insuranceCardNo=").append(insuranceCardNo);
        sb.append(", healthCardNo=").append(healthCardNo);
        sb.append(", payWay=").append(payWay);
        sb.append(", admissionNumber=").append(admissionNumber);
        sb.append(", tpatno=").append(tpatno);
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", name=").append(name);
        sb.append(", gender=").append(gender);
        sb.append(", dateOfBirth=").append(dateOfBirth);
        sb.append(", age=").append(age);
        sb.append(", marriage=").append(marriage);
        sb.append(", occupation=").append(occupation);
        sb.append(", birthPlace=").append(birthPlace);
        sb.append(", birthPlaceProvince=").append(birthPlaceProvince);
        sb.append(", birthPlaceCity=").append(birthPlaceCity);
        sb.append(", birthPlaceCountry=").append(birthPlaceCountry);
        sb.append(", nation=").append(nation);
        sb.append(", citizenship=").append(citizenship);
        sb.append(", idNo=").append(idNo);
        sb.append(", homeAdress=").append(homeAdress);
        sb.append(", homePhone=").append(homePhone);
        sb.append(", homeAdressPostcode=").append(homeAdressPostcode);
        sb.append(", workUnitAndAdress=").append(workUnitAndAdress);
        sb.append(", contactTelephone=").append(contactTelephone);
        sb.append(", workUnitPostcode=").append(workUnitPostcode);
        sb.append(", registeredResidence=").append(registeredResidence);
        sb.append(", registeredResidencePostcode=").append(registeredResidencePostcode);
        sb.append(", contactName=").append(contactName);
        sb.append(", contactRelationship=").append(contactRelationship);
        sb.append(", contactAdress=").append(contactAdress);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", admissionWay=").append(admissionWay);
        sb.append(", admissionDateTime=").append(admissionDateTime);
        sb.append(", deptAdmissionTo=").append(deptAdmissionTo);
        sb.append(", wardAdmissionTo=").append(wardAdmissionTo);
        sb.append(", deptTransferFrom=").append(deptTransferFrom);
        sb.append(", dischargeDateTime=").append(dischargeDateTime);
        sb.append(", deptDischargeFrom=").append(deptDischargeFrom);
        sb.append(", wardDischargeFrom=").append(wardDischargeFrom);
        sb.append(", inDays=").append(inDays);
        sb.append(", infectedTimes=").append(infectedTimes);
        sb.append(", injuryPoisoningCode=").append(injuryPoisoningCode);
        sb.append(", injuryPoisoningCauses=").append(injuryPoisoningCauses);
        sb.append(", isAllergic=").append(isAllergic);
        sb.append(", allergenDrug=").append(allergenDrug);
        sb.append(", hbsag=").append(hbsag);
        sb.append(", hcvAb=").append(hcvAb);
        sb.append(", hivAb=").append(hivAb);
        sb.append(", outpDisDiagConformity=").append(outpDisDiagConformity);
        sb.append(", admitDisDiagConformity=").append(admitDisDiagConformity);
        sb.append(", preopPostopDiagConformity=").append(preopPostopDiagConformity);
        sb.append(", clinicPathoDiagConformity=").append(clinicPathoDiagConformity);
        sb.append(", radioPathoDiagConformity=").append(radioPathoDiagConformity);
        sb.append(", rescueTimes=").append(rescueTimes);
        sb.append(", rescueSuccessTimes=").append(rescueSuccessTimes);
        sb.append(", diagBasis=").append(diagBasis);
        sb.append(", differentiationDegree=").append(differentiationDegree);
        sb.append(", director=").append(director);
        sb.append(", chiefDoctor=").append(chiefDoctor);
        sb.append(", attendingDoctor=").append(attendingDoctor);
        sb.append(", residentDoctor=").append(residentDoctor);
        sb.append(", responsibleNurse=").append(responsibleNurse);
        sb.append(", traineeDoctor=").append(traineeDoctor);
        sb.append(", graduateInternDoctor=").append(graduateInternDoctor);
        sb.append(", internDoctor=").append(internDoctor);
        sb.append(", coderName=").append(coderName);
        sb.append(", mrQuality=").append(mrQuality);
        sb.append(", qualityControlDoctor=").append(qualityControlDoctor);
        sb.append(", qualityControlNurse=").append(qualityControlNurse);
        sb.append(", qualityConfirmDatetime=").append(qualityConfirmDatetime);
        sb.append(", specialNursingDays=").append(specialNursingDays);
        sb.append(", firstGradeNursingDays=").append(firstGradeNursingDays);
        sb.append(", secondGradeNursingDays=").append(secondGradeNursingDays);
        sb.append(", thirdGradeNursingDays=").append(thirdGradeNursingDays);
        sb.append(", icuName=").append(icuName);
        sb.append(", icuInDatetime=").append(icuInDatetime);
        sb.append(", icuOutDatetime=").append(icuOutDatetime);
        sb.append(", deathPatientAutopsy=").append(deathPatientAutopsy);
        sb.append(", firstExampleInHospital=").append(firstExampleInHospital);
        sb.append(", operationPatientType=").append(operationPatientType);
        sb.append(", followUp=").append(followUp);
        sb.append(", followUpWeeks=").append(followUpWeeks);
        sb.append(", followUpMonths=").append(followUpMonths);
        sb.append(", followUpYears=").append(followUpYears);
        sb.append(", demonstrationCase=").append(demonstrationCase);
        sb.append(", aboBloodType=").append(aboBloodType);
        sb.append(", rhBloodType=").append(rhBloodType);
        sb.append(", adverseReaction=").append(adverseReaction);
        sb.append(", erythrocyte=").append(erythrocyte);
        sb.append(", platelet=").append(platelet);
        sb.append(", plasma=").append(plasma);
        sb.append(", wholeBlood=").append(wholeBlood);
        sb.append(", autologousBloodCallback=").append(autologousBloodCallback);
        sb.append(", othersBlood=").append(othersBlood);
        sb.append(", ageUnderOneYear=").append(ageUnderOneYear);
        sb.append(", newbornWeight=").append(newbornWeight);
        sb.append(", newbornAdmitWeight=").append(newbornAdmitWeight);
        sb.append(", comaHoursBeforeAdmit=").append(comaHoursBeforeAdmit);
        sb.append(", comaMinutesBeforeAdmit=").append(comaMinutesBeforeAdmit);
        sb.append(", comaHoursAfterAdmit=").append(comaHoursAfterAdmit);
        sb.append(", comaMinutesAfterAdmit=").append(comaMinutesAfterAdmit);
        sb.append(", respiratorUsingTime=").append(respiratorUsingTime);
        sb.append(", readmitPlanInThirtyDays=").append(readmitPlanInThirtyDays);
        sb.append(", readmitReasonInThirtyDays=").append(readmitReasonInThirtyDays);
        sb.append(", dischargeWay=").append(dischargeWay);
        sb.append(", thansferToHospital=").append(thansferToHospital);
        sb.append(", sourcePath=").append(sourcePath);
        sb.append(", pkId=").append(pkId);
        sb.append(", dataState=").append(dataState);
        sb.append(", icuDays=").append(icuDays);
        sb.append(", ccuDays=").append(ccuDays);
        sb.append(", tbPatientTreatmrnt=").append(tbPatientTreatmrnt);
        sb.append(", tbResistanceType=").append(tbResistanceType);
        sb.append(", tbSputumCulture=").append(tbSputumCulture);
        sb.append(", tbSputumSmear=").append(tbSputumSmear);
        sb.append(", patientSnOrg=").append(patientSnOrg);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
