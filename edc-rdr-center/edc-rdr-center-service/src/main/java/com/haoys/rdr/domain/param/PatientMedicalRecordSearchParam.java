package com.haoys.rdr.domain.param;

import com.haoys.user.common.core.domain.vo.BaseVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class PatientMedicalRecordSearchParam extends BaseVo {

    private String dataFrom = "rdr";
    // 患者视图patient-view 就诊视图visit-view
    private String searchView = "";
    private String searchType = "";
    private String dataBaseId = "";
    private String dataSetId = "";
    private String modelSourceCode;
    private String searchWord;
    private String querySegment = "";
    private String startDate = "";
    private String endDate = "";
    private Boolean exportPatientRecord = false;

    private List<SearcherModeRule> dataList = new ArrayList<>();

    private String sortField = "patientSn";
    private String sortType = "asc";

    private long totalCount;

    @Data
    public static class SearcherModeRule {
        // 取值 AND OR NOT
        private String operatorValue;
        private String modelSourceCode;
        private String variableCode;
        private String variableType;
        private String searchWord;
        private String searchStartDate;
        private String searchEndDate;
        // 取值 accurate fuzzy
        private String queryConfig;
    }

}
