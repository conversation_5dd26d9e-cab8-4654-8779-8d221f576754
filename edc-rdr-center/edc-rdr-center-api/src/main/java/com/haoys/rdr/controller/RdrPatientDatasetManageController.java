package com.haoys.rdr.controller;


import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.annotation.NoRepeatSubmit;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.CustomResult;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.enums.system.PlatformType;
import com.haoys.rdr.domain.param.ExportPatientAnalysisDataParam;
import com.haoys.rdr.domain.param.RdrPatientAnalysisDatasetParam;
import com.haoys.rdr.domain.param.RdrPatientAnalysisRecordParam;
import com.haoys.rdr.domain.param.RdrPatientSnModifyParam;
import com.haoys.rdr.domain.vo.RdrPatientAnalysisDatasetVo;
import com.haoys.rdr.service.RdrPatientAnalysisDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "数据中心-数据集分析管理")
@RequestMapping("/rdr-patient-analysis")
public class RdrPatientDatasetManageController {

    @Autowired
    private RdrPatientAnalysisDataService rdrPatientAnalysisDataService;

    @NoRepeatSubmit
    @Log(title = "创建和修改分析数据集", businessType = BusinessType.INSERT, platformType = PlatformType.DISEASE)
    @ApiOperation(value = "创建和修改分析数据集")
    @PostMapping(value = "/savePatientAnalysisDataSet")
    public CommonResult<Object> savePatientAnalysisDataSet(@Validated @RequestBody RdrPatientAnalysisDatasetParam patientAnalysisDatasetParam){
        CustomResult data = rdrPatientAnalysisDataService.savePatientAnalysisDataSet(patientAnalysisDatasetParam);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "将选中患者加入到数据集", businessType = BusinessType.INSERT, platformType = PlatformType.DISEASE)
    @ApiOperation(value = "将选中患者加入到数据集")
    @PostMapping(value = "/savePatientAnalysisRecord")
    public CommonResult<Object> savePatientAnalysisRecord(@Validated @RequestBody RdrPatientAnalysisRecordParam patientAnalysisRecordParam){
        CustomResult data = rdrPatientAnalysisDataService.savePatientAnalysisRecordJoinDataSet(patientAnalysisRecordParam);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "启用停用数据集", businessType = BusinessType.UPDATE, platformType = PlatformType.DISEASE)
    @ApiOperation(value = "启用停用数据集")
    @PostMapping(value = "/modifyPatientAnalysisDataSet")
    public CommonResult<Object> modifyPatientAnalysisDataSet(String dataSetId, String enabled){
        CustomResult data = rdrPatientAnalysisDataService.modifyPatientAnalysisDataSet(dataSetId, enabled);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "删除数据集", businessType = BusinessType.DELETE, platformType = PlatformType.DISEASE)
    @ApiOperation(value = "删除数据集")
    @PostMapping(value = "/removePatientAnalysisDataSet")
    public CommonResult<Object> removePatientAnalysisDataSet(String dataSetId){
        CustomResult data = rdrPatientAnalysisDataService.removePatientAnalysisDataSet(dataSetId);
        return CommonResult.success(data);
    }

    @NoRepeatSubmit
    @Log(title = "删除数据集患者明细记录", businessType = BusinessType.DELETE, platformType = PlatformType.DISEASE)
    @ApiOperation(value = "删除数据集患者明细记录")
    @PostMapping(value = "/removePatientAnalysisRecord")
    public CustomResult removePatientAnalysisRecord(@RequestBody RdrPatientSnModifyParam rdrPatientSnModifyParam){
        return rdrPatientAnalysisDataService.removePatientAnalysisRecord(rdrPatientSnModifyParam.getDataSetId(), rdrPatientSnModifyParam.getPatientSn());
    }

    @ApiOperation("查询分析数据集分页列表")
    @RequestMapping(value = "/getPatientAnalysisDataSetForPage", method = RequestMethod.GET)
    public CommonResult<CommonPage<RdrPatientAnalysisDatasetVo>> getPatientAnalysisDataSetForPage(String code, String enabled, String dataBaseId,
                                                                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize){
        CommonPage<RdrPatientAnalysisDatasetVo> dataList = rdrPatientAnalysisDataService.getPatientAnalysisDataSetForPage(dataBaseId, code, enabled, pageNum, pageSize);
        return CommonResult.success(dataList);
    }


    @ApiOperation("数据集导出")
    @PostMapping(value = "/exportPatientAnalysisData")
    public CommonResult<Object> exportPatientAnalysisData(@RequestBody ExportPatientAnalysisDataParam param){
        return rdrPatientAnalysisDataService.exportPatientAnalysisData(param);
    }

}
