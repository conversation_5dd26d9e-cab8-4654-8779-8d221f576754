package com.haoys.rdr.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.elasticsearch.TreeSearchParam;
import com.haoys.rdr.service.RdrPatientTreeSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

@RestController
@Api(tags = "数据中心-条件检索-elasticsearch")
@RequestMapping("/rdr-treeSearch")
public class RdrTreeSearchController extends BaseController {

    @Resource
    private RdrPatientTreeSearchService treeSearchService;


    @ApiOperation("条件检索")
    @PostMapping(value = "treeSearch")
    public CommonResult<Map<String, Object>> treeSearch(@RequestBody TreeSearchParam param) throws IOException {
        return treeSearchService.list(param);
    }

}
