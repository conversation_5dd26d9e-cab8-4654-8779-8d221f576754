<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.PatientModelDefineMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.PatientModelDefine">
    <id column="model_source_id" jdbcType="VARCHAR" property="modelSourceId" />
    <result column="model_source_code" jdbcType="VARCHAR" property="modelSourceCode" />
    <result column="model_source_name" jdbcType="VARCHAR" property="modelSourceName" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="custom_model" jdbcType="SMALLINT" property="customModel" />
    <result column="abbreviate_code" jdbcType="VARCHAR" property="abbreviateCode" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="group_by" jdbcType="VARCHAR" property="groupBy" />
    <result column="owner_group" jdbcType="VARCHAR" property="ownerGroup" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "model_source_id", "model_source_code", "model_source_name", "parent_code", "custom_model", 
    "abbreviate_code", "sort", "enabled", "expand", "create_time", "group_by", "owner_group"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.PatientModelDefineExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."patient_model_define"
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."patient_model_define"
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.PatientModelDefineExample">
    delete from "public"."patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.PatientModelDefine">
    insert into "public"."patient_model_define" ("model_source_id", "model_source_code", "model_source_name", 
      "parent_code", "custom_model", "abbreviate_code", 
      "sort", "enabled", "expand", 
      "create_time", "group_by", "owner_group"
      )
    values (#{modelSourceId,jdbcType=VARCHAR}, #{modelSourceCode,jdbcType=VARCHAR}, #{modelSourceName,jdbcType=VARCHAR}, 
      #{parentCode,jdbcType=VARCHAR}, #{customModel,jdbcType=SMALLINT}, #{abbreviateCode,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{enabled,jdbcType=BIT}, #{expand,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{groupBy,jdbcType=VARCHAR}, #{ownerGroup,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.PatientModelDefine">
    insert into "public"."patient_model_define"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelSourceId != null">
        "model_source_id",
      </if>
      <if test="modelSourceCode != null">
        "model_source_code",
      </if>
      <if test="modelSourceName != null">
        "model_source_name",
      </if>
      <if test="parentCode != null">
        "parent_code",
      </if>
      <if test="customModel != null">
        "custom_model",
      </if>
      <if test="abbreviateCode != null">
        "abbreviate_code",
      </if>
      <if test="sort != null">
        "sort",
      </if>
      <if test="enabled != null">
        "enabled",
      </if>
      <if test="expand != null">
        "expand",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
      <if test="groupBy != null">
        "group_by",
      </if>
      <if test="ownerGroup != null">
        "owner_group",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelSourceId != null">
        #{modelSourceId,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceCode != null">
        #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="customModel != null">
        #{customModel,jdbcType=SMALLINT},
      </if>
      <if test="abbreviateCode != null">
        #{abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBy != null">
        #{groupBy,jdbcType=VARCHAR},
      </if>
      <if test="ownerGroup != null">
        #{ownerGroup,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.PatientModelDefineExample" resultType="java.lang.Long">
    select count(*) from "public"."patient_model_define"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."patient_model_define"
    <set>
      <if test="record.modelSourceId != null">
        "model_source_id" = #{record.modelSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceCode != null">
        "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelSourceName != null">
        "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCode != null">
        "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.customModel != null">
        "custom_model" = #{record.customModel,jdbcType=SMALLINT},
      </if>
      <if test="record.abbreviateCode != null">
        "abbreviate_code" = #{record.abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        "sort" = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.enabled != null">
        "enabled" = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.expand != null">
        "expand" = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groupBy != null">
        "group_by" = #{record.groupBy,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerGroup != null">
        "owner_group" = #{record.ownerGroup,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."patient_model_define"
    set "model_source_id" = #{record.modelSourceId,jdbcType=VARCHAR},
      "model_source_code" = #{record.modelSourceCode,jdbcType=VARCHAR},
      "model_source_name" = #{record.modelSourceName,jdbcType=VARCHAR},
      "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      "custom_model" = #{record.customModel,jdbcType=SMALLINT},
      "abbreviate_code" = #{record.abbreviateCode,jdbcType=VARCHAR},
      "sort" = #{record.sort,jdbcType=INTEGER},
      "enabled" = #{record.enabled,jdbcType=BIT},
      "expand" = #{record.expand,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      "group_by" = #{record.groupBy,jdbcType=VARCHAR},
      "owner_group" = #{record.ownerGroup,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.PatientModelDefine">
    update "public"."patient_model_define"
    <set>
      <if test="modelSourceCode != null">
        "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      </if>
      <if test="modelSourceName != null">
        "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        "parent_code" = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="customModel != null">
        "custom_model" = #{customModel,jdbcType=SMALLINT},
      </if>
      <if test="abbreviateCode != null">
        "abbreviate_code" = #{abbreviateCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        "sort" = #{sort,jdbcType=INTEGER},
      </if>
      <if test="enabled != null">
        "enabled" = #{enabled,jdbcType=BIT},
      </if>
      <if test="expand != null">
        "expand" = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupBy != null">
        "group_by" = #{groupBy,jdbcType=VARCHAR},
      </if>
      <if test="ownerGroup != null">
        "owner_group" = #{ownerGroup,jdbcType=VARCHAR},
      </if>
    </set>
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.PatientModelDefine">
    update "public"."patient_model_define"
    set "model_source_code" = #{modelSourceCode,jdbcType=VARCHAR},
      "model_source_name" = #{modelSourceName,jdbcType=VARCHAR},
      "parent_code" = #{parentCode,jdbcType=VARCHAR},
      "custom_model" = #{customModel,jdbcType=SMALLINT},
      "abbreviate_code" = #{abbreviateCode,jdbcType=VARCHAR},
      "sort" = #{sort,jdbcType=INTEGER},
      "enabled" = #{enabled,jdbcType=BIT},
      "expand" = #{expand,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP},
      "group_by" = #{groupBy,jdbcType=VARCHAR},
      "owner_group" = #{ownerGroup,jdbcType=VARCHAR}
    where "model_source_id" = #{modelSourceId,jdbcType=VARCHAR}
  </update>


  <!--查询表单模型定义表-->
  <select id="getTableModelDefineList" resultType="com.haoys.xinjiang.domain.schmea.TableModelVo">
    SELECT
    table_schema,table_name,table_comment
    FROM
    information_schema.TABLES
    WHERE
    table_schema = 'disease_data_center'
    AND table_name IN ('patient_base_info','patient_visit_info','patient_diagnosis_info','patient_document_info','patient_emr_info','patient_inspection_apply','patient_inspection_report','patient_vitalsign_info')
  </select>

  <!--查询表单模型字段表-->
  <select id="getTableModelColumnByModelCode" resultType="com.haoys.xinjiang.domain.schmea.TableModelVo">
    SELECT table_schema,table_name,column_name,data_type,column_comment,ordinal_position FROM information_schema.COLUMNS WHERE table_schema  = 'disease_data_center' AND table_name = #{tableName} ORDER BY ordinal_position;
  </select>

  <!--根据modelSourceId查询基本信息-->
  <select id="getPatientModelDefineByModelSourceId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from patient_model_define where model_source_id = #{modelSourceId} limit 1
  </select>

  <!--根据modelSourceCode查询基本信息-->
  <select id="getPatientModelDefineByModelSourceCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from patient_model_define where model_source_code = #{modelSourceCode} limit 1
  </select>

  <select id="getPatientVariableConfigListByModelSourceCode" resultType="com.haoys.xinjiang.domain.vo.PatientModelVariableVo">
    SELECT
      max(patient_model_variable.id) "variableId",
      max(patient_model_variable.variable_code) "variableCode",
      max(patient_model_variable.variable_name) "variableName",
      max(patient_model_variable.variable_type) "variableType",
      max(patient_model_variable.group_name) "groupName",
      max(patient_model_variable.combobox_data) "comboboxData",
      bool_and(patient_model_variable.custom_variable) "customVariable",
      bool_and(patient_model_variable.default_query) "defaultQuery",
      max(patient_model_variable.model_source_code) "modelSourceCode",
      max(patient_model_variable.id) "modelSourceId",
      max(patient_model_define.sort) "modelSort",
      max(patient_model_variable.sort) "sort"
    FROM
      patient_model_variable
    JOIN patient_model_define ON patient_model_define.model_source_code = patient_model_define.model_source_code
    where 1 = 1
      and patient_model_variable.model_source_code = #{modelSourceCode}
      and patient_model_variable.default_query = true
      <if test="enableCustomVariable == false">
        and patient_model_variable.custom_variable = #{enableCustomVariable}
      </if>
    GROUP BY
      patient_model_variable.variable_code
    ORDER BY
      max(patient_model_define.sort) ASC,
      max(patient_model_variable.sort) ASC,
      max(patient_model_variable.id) ASC
  </select>

  <update id="updatePatientModelDefineBySourceId">
    update patient_model_define
    <set>
      <if test="record.modelSourceId != null">
        model_source_id = #{record.modelSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelCode != null">
        model_code = #{record.modelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.modelName != null">
        model_name = #{record.modelName,jdbcType=VARCHAR},
      </if>
      <if test="record.customModel != null">
        custom_model = #{record.customModel,jdbcType=BOOLEAN},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.expand != null">
        expand = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BOOLEAN},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where patient_model_define.model_source_id = #{modelSourceId}
  </update>

  <select id="getPatientVariableConfig" resultType="com.haoys.xinjiang.model.PatientModelVariable">
    select * from patient_model_variable where model_source_code = #{modelSourceCode} and variable_code = #{variableCode} order by create_time desc limit 1
  </select>

  <select id="getPatientModelSourceCodeListForDisease" resultType="com.haoys.xinjiang.domain.vo.PatientModelDefineVo">
    select model_source_code as variableCode,model_source_name as variableName from patient_model_define order by sort asc
  </select>

  <select id="getPatientDefaultModelVariableConfig" resultType="com.haoys.xinjiang.domain.vo.PatientModelVariableVo">
    select * from patient_model_variable where 1 = 1 and default_query = true order by create_time desc
  </select>

  <select id="getPatientVariableConfigById" resultType="com.haoys.xinjiang.model.PatientModelVariable">
    select * from patient_model_variable where variable_id = #{variableId}
  </select>


  <select id="getModelVariablesConfig" resultType="com.haoys.xinjiang.domain.vo.PatientModelDefineVo">
    SELECT
      max(patient_model_variable.id) "variableId",
      max(patient_model_variable.variable_code) "variableCode",
      max(patient_model_variable.variable_type) "variableType",
      max(patient_model_variable.variable_name) variableName,
      <!--CONCAT(max(patient_model_variable.variable_name),' (',max(patient_model_define.abbreviate_code),')') "variableName",-->
      bool_and(patient_model_variable.default_query) "defaultQuery",
      max(patient_model_variable.sort) "sort"
    FROM
      patient_model_variable
    INNER JOIN patient_model_define ON patient_model_define.model_source_code = patient_model_variable.model_source_code
    WHERE 1 = 1
      and patient_model_variable.model_source_code = #{modelSourceCode}
      and patient_model_variable.default_query = true
      and patient_model_variable.custom_variable = false
    GROUP BY
      patient_model_variable.variable_code
    ORDER BY
      max(patient_model_variable.sort) ASC
  </select>

  <update id="updatePatientModelVariableByCondition" parameterType="com.haoys.xinjiang.model.PatientModelVariable">
    update patient_model_variable
    <set>
      <if test="comboboxData != null">
        combobox_data = #{comboboxData,jdbcType=VARCHAR},
      </if>
      <if test="dictionaryValue != null">
        dictionary_value = #{dictionaryValue,jdbcType=VARCHAR},
      </if>
      <if test="defaultValue != null">
        default_value = #{defaultValue,jdbcType=VARCHAR},
      </if>
      <if test="defaultQuery != null">
        default_query = #{defaultQuery,jdbcType=BOOLEAN},
      </if>
      <if test="maxLength != null">
        max_length = #{maxLength,jdbcType=INTEGER},
      </if>
      <if test="enableNull != null">
        enable_null = #{enableNull,jdbcType=BOOLEAN},
      </if>
      <if test="enableUnique != null">
        enable_unique = #{enableUnique,jdbcType=BOOLEAN},
      </if>
      <if test="customVariable != null">
        custom_variable = #{customVariable,jdbcType=BOOLEAN},
      </if>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where patient_model_variable.variable_id = #{variableId,jdbcType=VARCHAR}
  </update>

  <select id="getPatientTableModelConfigList" resultType="com.haoys.xinjiang.domain.vo.PatientTableModelVo">
    SELECT
      relname AS "tableName",
      cast(obj_description (relfilenode, 'pg_class') AS VARCHAR) AS "tableComment"
    FROM
      pg_class c
    WHERE
      relkind = 'r'
      AND relname NOT LIKE 'pg_%'
      AND relname NOT LIKE 'sql_%'
      AND relname NOT LIKE 'patient_%'
      AND cast(obj_description (relfilenode, 'pg_class') AS VARCHAR) is not null
    ORDER BY relname
  </select>

  <select id="getTableModelColumnOrdinalPositionBySourceModelCode" resultType="com.haoys.xinjiang.domain.vo.PatientTableModelVo">
    select "table_catalog", "table_name", "column_name", "ordinal_position" from information_schema.columns where table_schema='public' and table_name = #{modelSourceCode} order by ordinal_position
  </select>

  <select id="getTableModelColumnBySourceModelCode" resultType="com.haoys.xinjiang.domain.vo.PatientTableModelVo">
    SELECT
      C.relname "tableName",
      CAST (obj_description (C.OID, 'pg_class') AS VARCHAR) "tableComment",
      A.attname "columnName",
      D.description "columnComment",
      T.typname AS "dataType"
    <!--concat_ws ( '', T.typname, SUBSTRING ( format_type ( A.atttypid, A.atttypmod ) FROM '\(.*\)' ) ) AS dataType-->
    FROM
      pg_class C,
      pg_attribute A,
      pg_type T,
      pg_description D
    WHERE C.relname = #{tableName}
    AND A.attnum > 0
    AND A.attrelid = C.OID
    AND A.atttypid = T.OID
    AND D.objoid = A.attrelid
    AND D.objsubid = A.attnum
<!--    AND C.relname IN ( SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND POSITION ( '_2' IN tablename ) = 0 )-->
    AND CAST (obj_description (C.OID, 'pg_class') AS VARCHAR) is not null
    ORDER BY C.relname, A.attnum
  </select>

  <select id="truncateTable">
    truncate table "public"."patient_model_define"
  </select>

  <select id="getModelSourceCodeListByOwnerGroup" resultType="com.haoys.xinjiang.domain.vo.PatientModelDefineGroup">
    SELECT
      patient_model_group.group_code "groupCode",
      patient_model_group.group_name "groupName",
      patient_model_define.model_source_code "modelSourceCode",
      patient_model_define.model_source_name "modelSourceName",
      patient_model_group.sort "sort"
    FROM
      patient_model_define
    INNER JOIN patient_model_group ON patient_model_group.group_code = patient_model_define.owner_group
    WHERE 1 = 1
    <if test="groupCode != null and groupCode != ''">
      and patient_model_group.group_code = #{groupCode}
    </if>
    ORDER BY patient_model_group.sort,patient_model_define.sort

  </select>

</mapper>