<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.PatientModelGroupMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.PatientModelGroup">
    <id column="group_code" jdbcType="VARCHAR" property="groupCode" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="expand" jdbcType="VARCHAR" property="expand" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "group_code", "group_name", "parent_code", "sort", "expand", "create_time"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.PatientModelGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."patient_model_group"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."patient_model_group"
    where "group_code" = #{groupCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."patient_model_group"
    where "group_code" = #{groupCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.PatientModelGroupExample">
    delete from "public"."patient_model_group"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.PatientModelGroup">
    insert into "public"."patient_model_group" ("group_code", "group_name", "parent_code", 
      "sort", "expand", "create_time"
      )
    values (#{groupCode,jdbcType=VARCHAR}, #{groupName,jdbcType=VARCHAR}, #{parentCode,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{expand,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.PatientModelGroup">
    insert into "public"."patient_model_group"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupCode != null">
        "group_code",
      </if>
      <if test="groupName != null">
        "group_name",
      </if>
      <if test="parentCode != null">
        "parent_code",
      </if>
      <if test="sort != null">
        "sort",
      </if>
      <if test="expand != null">
        "expand",
      </if>
      <if test="createTime != null">
        "create_time",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupCode != null">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="expand != null">
        #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.PatientModelGroupExample" resultType="java.lang.Long">
    select count(*) from "public"."patient_model_group"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."patient_model_group"
    <set>
      <if test="record.groupCode != null">
        "group_code" = #{record.groupCode,jdbcType=VARCHAR},
      </if>
      <if test="record.groupName != null">
        "group_name" = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCode != null">
        "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        "sort" = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.expand != null">
        "expand" = #{record.expand,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        "create_time" = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."patient_model_group"
    set "group_code" = #{record.groupCode,jdbcType=VARCHAR},
      "group_name" = #{record.groupName,jdbcType=VARCHAR},
      "parent_code" = #{record.parentCode,jdbcType=VARCHAR},
      "sort" = #{record.sort,jdbcType=INTEGER},
      "expand" = #{record.expand,jdbcType=VARCHAR},
      "create_time" = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.PatientModelGroup">
    update "public"."patient_model_group"
    <set>
      <if test="groupName != null">
        "group_name" = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="parentCode != null">
        "parent_code" = #{parentCode,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        "sort" = #{sort,jdbcType=INTEGER},
      </if>
      <if test="expand != null">
        "expand" = #{expand,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        "create_time" = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where "group_code" = #{groupCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.PatientModelGroup">
    update "public"."patient_model_group"
    set "group_name" = #{groupName,jdbcType=VARCHAR},
      "parent_code" = #{parentCode,jdbcType=VARCHAR},
      "sort" = #{sort,jdbcType=INTEGER},
      "expand" = #{expand,jdbcType=VARCHAR},
      "create_time" = #{createTime,jdbcType=TIMESTAMP}
    where "group_code" = #{groupCode,jdbcType=VARCHAR}
  </update>

  <select id="getModelSourceGroupNameList" resultMap="BaseResultMap">
    select group_code,group_name from patient_model_group where group_name is not null order by sort
  </select>

</mapper>