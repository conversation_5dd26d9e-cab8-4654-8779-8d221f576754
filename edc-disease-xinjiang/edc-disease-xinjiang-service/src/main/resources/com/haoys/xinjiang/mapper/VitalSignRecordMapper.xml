<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.VitalSignRecordMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.VitalSignRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_datetime" jdbcType="TIMESTAMP" property="recordDatetime" />
    <result column="temperature" jdbcType="DOUBLE" property="temperature" />
    <result column="pulse_rate" jdbcType="INTEGER" property="pulseRate" />
    <result column="respiratory_rate" jdbcType="INTEGER" property="respiratoryRate" />
    <result column="systolic_blood_pressure" jdbcType="INTEGER" property="systolicBloodPressure" />
    <result column="diastolic_blood_pressure" jdbcType="INTEGER" property="diastolicBloodPressure" />
    <result column="heart_rate" jdbcType="INTEGER" property="heartRate" />
    <result column="weight" jdbcType="DOUBLE" property="weight" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "record_datetime", "temperature", "pulse_rate", "respiratory_rate", "systolic_blood_pressure", 
    "diastolic_blood_pressure", "heart_rate", "weight", "data_source", "patient_sn", 
    "visit_sn", "pkid"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.VitalSignRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."vital_sign_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."vital_sign_record"
    where "id" = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "public"."vital_sign_record"
    where "id" = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.VitalSignRecordExample">
    delete from "public"."vital_sign_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.VitalSignRecord">
    insert into "public"."vital_sign_record" ("id", "record_datetime", "temperature", 
      "pulse_rate", "respiratory_rate", "systolic_blood_pressure", 
      "diastolic_blood_pressure", "heart_rate", "weight", 
      "data_source", "patient_sn", "visit_sn", 
      "pkid")
    values (#{id,jdbcType=INTEGER}, #{recordDatetime,jdbcType=TIMESTAMP}, #{temperature,jdbcType=DOUBLE}, 
      #{pulseRate,jdbcType=INTEGER}, #{respiratoryRate,jdbcType=INTEGER}, #{systolicBloodPressure,jdbcType=INTEGER}, 
      #{diastolicBloodPressure,jdbcType=INTEGER}, #{heartRate,jdbcType=INTEGER}, #{weight,jdbcType=DOUBLE}, 
      #{dataSource,jdbcType=VARCHAR}, #{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, 
      #{pkid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.VitalSignRecord">
    insert into "public"."vital_sign_record"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="recordDatetime != null">
        "record_datetime",
      </if>
      <if test="temperature != null">
        "temperature",
      </if>
      <if test="pulseRate != null">
        "pulse_rate",
      </if>
      <if test="respiratoryRate != null">
        "respiratory_rate",
      </if>
      <if test="systolicBloodPressure != null">
        "systolic_blood_pressure",
      </if>
      <if test="diastolicBloodPressure != null">
        "diastolic_blood_pressure",
      </if>
      <if test="heartRate != null">
        "heart_rate",
      </if>
      <if test="weight != null">
        "weight",
      </if>
      <if test="dataSource != null">
        "data_source",
      </if>
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="recordDatetime != null">
        #{recordDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="temperature != null">
        #{temperature,jdbcType=DOUBLE},
      </if>
      <if test="pulseRate != null">
        #{pulseRate,jdbcType=INTEGER},
      </if>
      <if test="respiratoryRate != null">
        #{respiratoryRate,jdbcType=INTEGER},
      </if>
      <if test="systolicBloodPressure != null">
        #{systolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="diastolicBloodPressure != null">
        #{diastolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="heartRate != null">
        #{heartRate,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=DOUBLE},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.VitalSignRecordExample" resultType="java.lang.Long">
    select count(*) from "public"."vital_sign_record"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."vital_sign_record"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.recordDatetime != null">
        "record_datetime" = #{record.recordDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.temperature != null">
        "temperature" = #{record.temperature,jdbcType=DOUBLE},
      </if>
      <if test="record.pulseRate != null">
        "pulse_rate" = #{record.pulseRate,jdbcType=INTEGER},
      </if>
      <if test="record.respiratoryRate != null">
        "respiratory_rate" = #{record.respiratoryRate,jdbcType=INTEGER},
      </if>
      <if test="record.systolicBloodPressure != null">
        "systolic_blood_pressure" = #{record.systolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="record.diastolicBloodPressure != null">
        "diastolic_blood_pressure" = #{record.diastolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="record.heartRate != null">
        "heart_rate" = #{record.heartRate,jdbcType=INTEGER},
      </if>
      <if test="record.weight != null">
        "weight" = #{record.weight,jdbcType=DOUBLE},
      </if>
      <if test="record.dataSource != null">
        "data_source" = #{record.dataSource,jdbcType=VARCHAR},
      </if>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."vital_sign_record"
    set "id" = #{record.id,jdbcType=INTEGER},
      "record_datetime" = #{record.recordDatetime,jdbcType=TIMESTAMP},
      "temperature" = #{record.temperature,jdbcType=DOUBLE},
      "pulse_rate" = #{record.pulseRate,jdbcType=INTEGER},
      "respiratory_rate" = #{record.respiratoryRate,jdbcType=INTEGER},
      "systolic_blood_pressure" = #{record.systolicBloodPressure,jdbcType=INTEGER},
      "diastolic_blood_pressure" = #{record.diastolicBloodPressure,jdbcType=INTEGER},
      "heart_rate" = #{record.heartRate,jdbcType=INTEGER},
      "weight" = #{record.weight,jdbcType=DOUBLE},
      "data_source" = #{record.dataSource,jdbcType=VARCHAR},
      "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.VitalSignRecord">
    update "public"."vital_sign_record"
    <set>
      <if test="recordDatetime != null">
        "record_datetime" = #{recordDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="temperature != null">
        "temperature" = #{temperature,jdbcType=DOUBLE},
      </if>
      <if test="pulseRate != null">
        "pulse_rate" = #{pulseRate,jdbcType=INTEGER},
      </if>
      <if test="respiratoryRate != null">
        "respiratory_rate" = #{respiratoryRate,jdbcType=INTEGER},
      </if>
      <if test="systolicBloodPressure != null">
        "systolic_blood_pressure" = #{systolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="diastolicBloodPressure != null">
        "diastolic_blood_pressure" = #{diastolicBloodPressure,jdbcType=INTEGER},
      </if>
      <if test="heartRate != null">
        "heart_rate" = #{heartRate,jdbcType=INTEGER},
      </if>
      <if test="weight != null">
        "weight" = #{weight,jdbcType=DOUBLE},
      </if>
      <if test="dataSource != null">
        "data_source" = #{dataSource,jdbcType=VARCHAR},
      </if>
      <if test="patientSn != null">
        "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        "pkid" = #{pkid,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.VitalSignRecord">
    update "public"."vital_sign_record"
    set "record_datetime" = #{recordDatetime,jdbcType=TIMESTAMP},
      "temperature" = #{temperature,jdbcType=DOUBLE},
      "pulse_rate" = #{pulseRate,jdbcType=INTEGER},
      "respiratory_rate" = #{respiratoryRate,jdbcType=INTEGER},
      "systolic_blood_pressure" = #{systolicBloodPressure,jdbcType=INTEGER},
      "diastolic_blood_pressure" = #{diastolicBloodPressure,jdbcType=INTEGER},
      "heart_rate" = #{heartRate,jdbcType=INTEGER},
      "weight" = #{weight,jdbcType=DOUBLE},
      "data_source" = #{dataSource,jdbcType=VARCHAR},
      "patient_sn" = #{patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{visitSn,jdbcType=VARCHAR},
      "pkid" = #{pkid,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=INTEGER}
  </update>
</mapper>