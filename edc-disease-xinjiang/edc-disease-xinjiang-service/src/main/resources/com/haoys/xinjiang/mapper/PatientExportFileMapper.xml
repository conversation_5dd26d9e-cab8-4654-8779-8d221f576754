<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.xinjiang.mapper.PatientExportFileMapper">
  <resultMap id="BaseResultMap" type="com.haoys.xinjiang.model.PatientExportFile">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="export_file_type" jdbcType="VARCHAR" property="exportFileType" />
    <result column="disease_type" jdbcType="VARCHAR" property="diseaseType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="export_status" jdbcType="VARCHAR" property="exportStatus" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="file_source" jdbcType="VARCHAR" property="fileSource" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "id", "file_name", "export_file_type", "disease_type", "operator", "download_url", 
    "export_status", "status", "file_source"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.xinjiang.model.PatientExportFileExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "public"."patient_export_file"
    where "id" = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from "public"."patient_export_file"
    where "id" = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.xinjiang.model.PatientExportFileExample">
    delete from "public"."patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.xinjiang.model.PatientExportFile">
    insert into "public"."patient_export_file" ("id", "file_name", "export_file_type", 
      "disease_type", "operator", "download_url", 
      "export_status", "status", "file_source"
      )
    values (#{id,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{exportFileType,jdbcType=VARCHAR}, 
      #{diseaseType,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{downloadUrl,jdbcType=VARCHAR}, 
      #{exportStatus,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{fileSource,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.xinjiang.model.PatientExportFile">
    insert into "public"."patient_export_file"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        "id",
      </if>
      <if test="fileName != null">
        "file_name",
      </if>
      <if test="exportFileType != null">
        "export_file_type",
      </if>
      <if test="diseaseType != null">
        "disease_type",
      </if>
      <if test="operator != null">
        "operator",
      </if>
      <if test="downloadUrl != null">
        "download_url",
      </if>
      <if test="exportStatus != null">
        "export_status",
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="fileSource != null">
        "file_source",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="exportFileType != null">
        #{exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="diseaseType != null">
        #{diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        #{exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="fileSource != null">
        #{fileSource,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.xinjiang.model.PatientExportFileExample" resultType="java.lang.Long">
    select count(*) from "public"."patient_export_file"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."patient_export_file"
    <set>
      <if test="record.id != null">
        "id" = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        "file_name" = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.exportFileType != null">
        "export_file_type" = #{record.exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="record.diseaseType != null">
        "disease_type" = #{record.diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        "operator" = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadUrl != null">
        "download_url" = #{record.downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.exportStatus != null">
        "export_status" = #{record.exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.fileSource != null">
        "file_source" = #{record.fileSource,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."patient_export_file"
    set "id" = #{record.id,jdbcType=VARCHAR},
      "file_name" = #{record.fileName,jdbcType=VARCHAR},
      "export_file_type" = #{record.exportFileType,jdbcType=VARCHAR},
      "disease_type" = #{record.diseaseType,jdbcType=VARCHAR},
      "operator" = #{record.operator,jdbcType=VARCHAR},
      "download_url" = #{record.downloadUrl,jdbcType=VARCHAR},
      "export_status" = #{record.exportStatus,jdbcType=VARCHAR},
      "status" = #{record.status,jdbcType=VARCHAR},
      "file_source" = #{record.fileSource,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.xinjiang.model.PatientExportFile">
    update "public"."patient_export_file"
    <set>
      <if test="fileName != null">
        "file_name" = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="exportFileType != null">
        "export_file_type" = #{exportFileType,jdbcType=VARCHAR},
      </if>
      <if test="diseaseType != null">
        "disease_type" = #{diseaseType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        "operator" = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null">
        "download_url" = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="exportStatus != null">
        "export_status" = #{exportStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="fileSource != null">
        "file_source" = #{fileSource,jdbcType=VARCHAR},
      </if>
    </set>
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.xinjiang.model.PatientExportFile">
    update "public"."patient_export_file"
    set "file_name" = #{fileName,jdbcType=VARCHAR},
      "export_file_type" = #{exportFileType,jdbcType=VARCHAR},
      "disease_type" = #{diseaseType,jdbcType=VARCHAR},
      "operator" = #{operator,jdbcType=VARCHAR},
      "download_url" = #{downloadUrl,jdbcType=VARCHAR},
      "export_status" = #{exportStatus,jdbcType=VARCHAR},
      "status" = #{status,jdbcType=VARCHAR},
      "file_source" = #{fileSource,jdbcType=VARCHAR}
    where "id" = #{id,jdbcType=VARCHAR}
  </update>
</mapper>