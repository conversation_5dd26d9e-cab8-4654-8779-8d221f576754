package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.TbAntibodyTest;
import com.haoys.xinjiang.model.TbAntibodyTestExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbAntibodyTestMapper {
    long countByExample(TbAntibodyTestExample example);

    int deleteByExample(TbAntibodyTestExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbAntibodyTest record);

    int insertSelective(TbAntibodyTest record);

    List<TbAntibodyTest> selectByExample(TbAntibodyTestExample example);

    TbAntibodyTest selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TbAntibodyTest record, @Param("example") TbAntibodyTestExample example);

    int updateByExample(@Param("record") TbAntibodyTest record, @Param("example") TbAntibodyTestExample example);

    int updateByPrimaryKeySelective(TbAntibodyTest record);

    int updateByPrimaryKey(TbAntibodyTest record);
}