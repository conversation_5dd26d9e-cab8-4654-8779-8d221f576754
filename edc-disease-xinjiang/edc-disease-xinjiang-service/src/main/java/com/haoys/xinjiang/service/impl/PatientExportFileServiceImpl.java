package com.haoys.xinjiang.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.bussiness.BusinessConfig;
import com.haoys.user.common.bussiness.RedisKeyContants;
import com.haoys.user.common.service.RedisTemplateService;
import com.haoys.user.common.util.SecurityUtils;
import com.haoys.xinjiang.mapper.PatientExportFileMapper;
import com.haoys.xinjiang.model.PatientExportFile;
import com.haoys.xinjiang.model.PatientExportFileExample;
import com.haoys.xinjiang.service.PatientExportFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@DS("disease_xinjiang")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientExportFileServiceImpl  implements PatientExportFileService {
    private final RedisTemplateService redisTemplateService;

    private final PatientExportFileMapper patientExportFileMapper;
    @Override
    public List<PatientExportFile> exportList() {

        String key = RedisKeyContants.USER_DOWN + SecurityUtils.getUserId();
        List<Object> list = redisTemplateService.lRange(key, 0, redisTemplateService.lSize(key));
        if (CollectionUtil.isNotEmpty(list)){
            List<String> ids=new ArrayList<>();
            list.forEach(id->ids.add(id.toString()));
            PatientExportFileExample example = new PatientExportFileExample();
            PatientExportFileExample.Criteria criteria = example.createCriteria();
            criteria.andIdIn(ids);
            criteria.andOperatorEqualTo(Objects.requireNonNull(SecurityUtils.getUserId()).toString());
            return patientExportFileMapper.selectByExample(example);
        }
        return new ArrayList<>();
    }



    @Override
    public CommonResult<Object> removeFile(String id) {
        PatientExportFileExample example = new PatientExportFileExample();
        PatientExportFileExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(id);
        List<PatientExportFile> exportFiles = patientExportFileMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(exportFiles)) {
            PatientExportFile export = exportFiles.get(0);
            redisTemplateService.lRemove(RedisKeyContants.USER_DOWN + export.getOperator(), 0, id);
            export.setStatus(BusinessConfig.NO_VALID_STATUS);
            patientExportFileMapper.updateByExample(export, example);
            return CommonResult.success("");
        }
        return CommonResult.failed();
    }

    @Override
    public CommonResult<Object> alreadyDown(String id) {
        redisTemplateService.lRemove(RedisKeyContants.USER_DOWN + SecurityUtils.getUserId(), 0, id);
        return CommonResult.success("");
    }
}
