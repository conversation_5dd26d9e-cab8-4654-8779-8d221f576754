package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.TbDrugSensitiveTest;
import com.haoys.xinjiang.model.TbDrugSensitiveTestExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbDrugSensitiveTestMapper {
    long countByExample(TbDrugSensitiveTestExample example);

    int deleteByExample(TbDrugSensitiveTestExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbDrugSensitiveTest record);

    int insertSelective(TbDrugSensitiveTest record);

    List<TbDrugSensitiveTest> selectByExample(TbDrugSensitiveTestExample example);

    TbDrugSensitiveTest selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TbDrugSensitiveTest record, @Param("example") TbDrugSensitiveTestExample example);

    int updateByExample(@Param("record") TbDrugSensitiveTest record, @Param("example") TbDrugSensitiveTestExample example);

    int updateByPrimaryKeySelective(TbDrugSensitiveTest record);

    int updateByPrimaryKey(TbDrugSensitiveTest record);
}