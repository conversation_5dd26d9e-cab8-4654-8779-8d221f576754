package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.SurgeryRecord;
import com.haoys.xinjiang.model.SurgeryRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SurgeryRecordMapper {
    long countByExample(SurgeryRecordExample example);

    int deleteByExample(SurgeryRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(SurgeryRecord record);

    int insertSelective(SurgeryRecord record);

    List<SurgeryRecord> selectByExample(SurgeryRecordExample example);

    SurgeryRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SurgeryRecord record, @Param("example") SurgeryRecordExample example);

    int updateByExample(@Param("record") SurgeryRecord record, @Param("example") SurgeryRecordExample example);

    int updateByPrimaryKeySelective(SurgeryRecord record);

    int updateByPrimaryKey(SurgeryRecord record);
}