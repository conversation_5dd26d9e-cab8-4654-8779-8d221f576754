package com.haoys.xinjiang.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch.core.CountRequest;
import co.elastic.clients.elasticsearch.core.CountResponse;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.util.StringUtils;
import com.haoys.user.config.PatientXinjiangDiseaseCenterConfig;
import com.haoys.user.elasticsearch.ElasticTreeSearchUtil;
import com.haoys.user.elasticsearch.SearchDto;
import com.haoys.user.elasticsearch.SearchTypeEnum;
import com.haoys.user.elasticsearch.TreeSearchParam;
import com.haoys.xinjiang.mapper.PatientNapiSearchMapper;
import com.haoys.xinjiang.model.PatientNapiSearch;
import com.haoys.xinjiang.model.PatientNapiSearchExample;
import com.haoys.xinjiang.service.PatientNaPiSearchTreeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Slf4j
@Service
@DS("disease_xinjiang")
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class PatientNaPiSearchTreeServiceImpl implements PatientNaPiSearchTreeService {

    @Resource(name = "clientByPasswd")
    private ElasticsearchClient client;


    /**
     * 病历视图 排序字段
     */
    private static String VISIT_SORT_FIELD="visitSn";

    /**
     * 患者视图 排序字段
     */
    private static String PATIENT_SORT_FIELD="patientSn";

    /**
     * 列表查询要展示的信息的表名称，对个用逗号隔开
     */
    private static String PATIENT_SOURCE="patients,visitInformation";


    private final PatientNapiSearchMapper patientNaPiSearchMapper;
    private final PatientXinjiangDiseaseCenterConfig patientXinjiangDiseaseCenterConfig;

    @Override
    public CommonResult<Map<String, Object>> list(TreeSearchParam param) throws IOException {


        Map<String, Object> map = new HashMap<>();
        CommonPage<Map<String, Object>> commonPage = new CommonPage<>();


        // 获取数据
        if (StringUtils.isNotEmpty(param.getDataBaseId())) {
            // 构建es搜索条件
            SearchRequest request = SearchRequest.of(i -> {
                SearchRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index()).from((param.getPageNum() - 1) * param.getPageSize())
                        .size(param.getPageSize()).source(s -> s.filter(f -> f.includes(StrUtil.split(PATIENT_SOURCE, ","))))
                        .sort(s -> s.field(f -> f.field(PATIENT_SORT_FIELD).order(SortOrder.Asc)));
                Query query = buildViewPatientQuery(param);
                builder.query(query);
                return builder;
            });

            CountRequest countRequest = CountRequest.of(i -> {
                CountRequest.Builder builder = i.index(patientXinjiangDiseaseCenterConfig.getPatient_join_visit_index());
                Query query = buildViewPatientQuery(param);
                builder.query(query);
                return builder;
            });
            CountResponse count = client.count(countRequest);

            SearchResponse<JSONObject> response = client.search(request, JSONObject.class);

            // 从es中获取数据
            List<Hit<JSONObject>> hits = response.hits().hits();
            if (CollectionUtil.isNotEmpty(hits)) {
                // 从es数据库中获取到数据后，处理成前端所需要的数据格式
                List<Map<String, Object>> list = new ArrayList<>();
                for (Hit<JSONObject> hit : hits) {
                    Map<String, Object> dataMap = JSONObject.parseObject(hit.source().toJSONString(), new TypeReference<Map<String, Object>>() {
                    });
                    dataMap.put("height", hit.highlight());
                    list.add(dataMap);
                }
                commonPage.setList(list);
                commonPage.setTotal(count.count());
            } else {
                commonPage.setList(new ArrayList<>());
                commonPage.setTotal(0l);
            }

            commonPage.setPageNum(param.getPageNum());
            commonPage.setPageSize(param.getPageSize());
            map.put("data", commonPage);
        }
        return CommonResult.success(map);
    }


    private Query buildViewPatientQuery(TreeSearchParam param) {
        List<String> ids = new ArrayList<>();

        PatientNapiSearch naPiSearch = null;
        if(StringUtils.isNotEmpty(param.getSearchId())){
            naPiSearch = getById(param.getSearchId());
        }
        BoolQuery.Builder bd = new BoolQuery.Builder();
        if(CollectionUtil.isNotEmpty(ids)){
            bd.must(qy -> qy.ids(id->id.values(ids)));
        }
        bd.must(qy -> qy.term(t -> t.field("docType").value("patient")));
        // 纳入条件
        if (naPiSearch != null) {
            String naSearch = naPiSearch.getNaSearch();
            if (StringUtils.isNotEmpty(naSearch)) {
                SearchDto include = JSON.parseObject(naSearch, SearchDto.class);
                if (StringUtils.isNotEmpty(include.getFieldCode())) {
                    // 说明树形搜索条件只有一个分支
                    // 构建嵌套条件
                    List<String> searchValues = include.getSearchValues();
                    if (CollectionUtil.isNotEmpty(searchValues)) {
                        // 说明是同患者
                        NestedQuery.Builder query = patientNestedQuery(include, searchValues);
                        bd.must(b -> b.nested(query.build()));
                    }
                } else {
                    // 说明树形搜索条件有多个分支
                    buildPatientQuery(include.getChildren().get(0), bd, null);
                }
            }
        }
        return bd.build()._toQuery();
    }


    private static void buildPatientQuery(SearchDto include, BoolQuery.Builder bd,HasChildQuery.Builder hcq) {
        // 获取下层级分支
        List<SearchDto> childList = include.getChildren();
        if (CollectionUtil.isNotEmpty(childList)) {
            // 如果分支之间是并且的关系，那么构建条件是must
            if (include.getSearchType().equals(SearchTypeEnum.AND.getCode())){
                // 循环条件
                BoolQuery.Builder bq = new BoolQuery.Builder();
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        List<String> searchValues = child.getSearchValues();
                        NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                        bq.must(b -> b.nested(builder.build()));
                    }else {
                        // 说明是有下级分支，需要进行循环
                        buildPatientQuery(child, bq,null);
                        bd.must(b->b.bool(bq.build()));
                    }
                }
            } else if (include.getSearchType().equals(SearchTypeEnum.OR.getCode())) {
                BoolQuery.Builder bq = new BoolQuery.Builder();
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        List<String> searchValues = child.getSearchValues();
                        if (CollectionUtil.isNotEmpty(searchValues)) {
                            // 说明是同患者
                            NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                            bq.should(b -> b.nested(builder.build()));
                        }
                    }else {
                        // 说明是有下级分支，需要进行循环
                        buildPatientQuery(child, bq,null);
                        bd.should(b->b.bool(bq.build()));
                    }
                }
            }else if (include.getSearchType().equals(SearchTypeEnum.NOT.getCode())) {
                BoolQuery.Builder bq = new BoolQuery.Builder();
                for (SearchDto child : childList) {
                    // 创建一个子的bool条件,每个子条件都需要加入到总的bool条件中
                    if (StringUtils.isNotEmpty(child.getFieldCode())) {
                        List<String> searchValues = child.getSearchValues();
                        if (CollectionUtil.isNotEmpty(searchValues)) {
                            // 说明是同患者
                            NestedQuery.Builder builder = patientNestedQuery(child, searchValues);
                            bq.mustNot(b -> b.nested(builder.build()));
                        }
                    }else {
                        // 说明是有下级分支，需要进行循环
                        buildPatientQuery(child, bq,null);
                        bd.mustNot(b->b.bool(bq.build()));
                    }
                }
            }
        }
    }


    private static NestedQuery.Builder patientNestedQuery(SearchDto child,List<String> searchValues) {

        NestedQuery.Builder nestedQuery = new NestedQuery.Builder();

        String formCode = StrUtil.toCamelCase(child.getFormCode());
        nestedQuery.path(formCode);
        // 循环值
        BoolQuery.Builder searchQuery = new BoolQuery.Builder();
        // 构建搜索条件的字段
        final String filed = formCode + "." + StrUtil.toCamelCase(child.getFieldCode());

        ElasticTreeSearchUtil.buildQuery(child, searchValues, searchQuery, filed);

        nestedQuery.query(q -> q.bool(searchQuery.build()));

        return nestedQuery;
    }



    public PatientNapiSearch getById(String id) {
        if (StringUtils.isNotBlank(id)) {
            PatientNapiSearchExample example = new PatientNapiSearchExample();
            PatientNapiSearchExample.Criteria criteria = example.createCriteria();
            criteria.andIdEqualTo(id);
            List<PatientNapiSearch> list = patientNaPiSearchMapper.selectByExample(example);
            if (CollectionUtil.isNotEmpty(list)) {
                return list.get(0);
            }
        }
        return null;
    }

}
