package com.haoys.xinjiang.service;

import com.haoys.user.common.api.CustomResult;
import com.haoys.xinjiang.domain.param.PatientMedicalRecordSearchParam;
import com.haoys.xinjiang.domain.wrapper.PatientBaseWrapper;

import java.util.List;

public interface PatientVariableRecordService {


    List<PatientBaseWrapper> getPatientMedicalRecordForPage(String searchType, String querySegment, String modelSourceCode, String searchWord,
                                                            List<PatientMedicalRecordSearchParam.SearcherModeRule> searcherModeAndRuleList,
                                                            List<PatientMedicalRecordSearchParam.SearcherModeRule> searcherModeNotRuleList,
                                                            String executeSqlValue, List<String> patientIds, String startDate, String endDate, String sortField, String sortType);

    
    CustomResult savePatientModelVariableRecordDataSourceForMongodb(String collectionName);
}
