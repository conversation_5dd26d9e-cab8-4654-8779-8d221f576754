package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.DeathRecord;
import com.haoys.xinjiang.model.DeathRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DeathRecordMapper {
    long countByExample(DeathRecordExample example);

    int deleteByExample(DeathRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(DeathRecord record);

    int insertSelective(DeathRecord record);

    List<DeathRecord> selectByExample(DeathRecordExample example);

    DeathRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") DeathRecord record, @Param("example") DeathRecordExample example);

    int updateByExample(@Param("record") DeathRecord record, @Param("example") DeathRecordExample example);

    int updateByPrimaryKeySelective(DeathRecord record);

    int updateByPrimaryKey(DeathRecord record);
}