package com.haoys.xinjiang.mapper;

import com.haoys.xinjiang.model.ExamRecord;
import com.haoys.xinjiang.model.ExamRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ExamRecordMapper {
    long countByExample(ExamRecordExample example);

    int deleteByExample(ExamRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ExamRecord record);

    int insertSelective(ExamRecord record);

    List<ExamRecord> selectByExample(ExamRecordExample example);

    ExamRecord selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ExamRecord record, @Param("example") ExamRecordExample example);

    int updateByExample(@Param("record") ExamRecord record, @Param("example") ExamRecordExample example);

    int updateByPrimaryKeySelective(ExamRecord record);

    int updateByPrimaryKey(ExamRecord record);
}