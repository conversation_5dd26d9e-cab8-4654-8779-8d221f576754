<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.disease.mapper.VisitInformationMapper">
  <resultMap id="BaseResultMap" type="com.haoys.disease.model.VisitInformation">
    <result column="patient_sn" jdbcType="VARCHAR" property="patientSn" />
    <result column="visit_sn" jdbcType="VARCHAR" property="visitSn" />
    <result column="pkid" jdbcType="VARCHAR" property="pkid" />
    <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
    <result column="visit_or_admission_datetime" jdbcType="TIMESTAMP" property="visitOrAdmissionDatetime" />
    <result column="visit_or_admission_dept" jdbcType="VARCHAR" property="visitOrAdmissionDept" />
    <result column="visit_or_attending_doctor" jdbcType="VARCHAR" property="visitOrAttendingDoctor" />
    <result column="visit_age" jdbcType="INTEGER" property="visitAge" />
    <result column="admission_way" jdbcType="VARCHAR" property="admissionWay" />
    <result column="discharge_datetime" jdbcType="TIMESTAMP" property="dischargeDatetime" />
    <result column="discharge_dept" jdbcType="VARCHAR" property="dischargeDept" />
    <result column="discharge_way" jdbcType="VARCHAR" property="dischargeWay" />
    <result column="in_days" jdbcType="INTEGER" property="inDays" />
    <result column="total_costs" jdbcType="DOUBLE" property="totalCosts" />
    <result column="self_pay" jdbcType="DOUBLE" property="selfPay" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    "patient_sn", "visit_sn", "pkid", "visit_type", "visit_or_admission_datetime", "visit_or_admission_dept", 
    "visit_or_attending_doctor", "visit_age", "admission_way", "discharge_datetime", 
    "discharge_dept", "discharge_way", "in_days", "total_costs", "self_pay"
  </sql>
  <select id="selectByExample" parameterType="com.haoys.disease.model.VisitInformationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.haoys.disease.model.VisitInformationExample">
    delete from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.disease.model.VisitInformation">
    insert into "public"."visit_information" ("patient_sn", "visit_sn", "pkid", 
      "visit_type", "visit_or_admission_datetime", 
      "visit_or_admission_dept", "visit_or_attending_doctor", 
      "visit_age", "admission_way", "discharge_datetime", 
      "discharge_dept", "discharge_way", "in_days", 
      "total_costs", "self_pay")
    values (#{patientSn,jdbcType=VARCHAR}, #{visitSn,jdbcType=VARCHAR}, #{pkid,jdbcType=VARCHAR}, 
      #{visitType,jdbcType=VARCHAR}, #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP}, 
      #{visitOrAdmissionDept,jdbcType=VARCHAR}, #{visitOrAttendingDoctor,jdbcType=VARCHAR}, 
      #{visitAge,jdbcType=INTEGER}, #{admissionWay,jdbcType=VARCHAR}, #{dischargeDatetime,jdbcType=TIMESTAMP}, 
      #{dischargeDept,jdbcType=VARCHAR}, #{dischargeWay,jdbcType=VARCHAR}, #{inDays,jdbcType=INTEGER}, 
      #{totalCosts,jdbcType=DOUBLE}, #{selfPay,jdbcType=DOUBLE})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.disease.model.VisitInformation">
    insert into "public"."visit_information"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        "patient_sn",
      </if>
      <if test="visitSn != null">
        "visit_sn",
      </if>
      <if test="pkid != null">
        "pkid",
      </if>
      <if test="visitType != null">
        "visit_type",
      </if>
      <if test="visitOrAdmissionDatetime != null">
        "visit_or_admission_datetime",
      </if>
      <if test="visitOrAdmissionDept != null">
        "visit_or_admission_dept",
      </if>
      <if test="visitOrAttendingDoctor != null">
        "visit_or_attending_doctor",
      </if>
      <if test="visitAge != null">
        "visit_age",
      </if>
      <if test="admissionWay != null">
        "admission_way",
      </if>
      <if test="dischargeDatetime != null">
        "discharge_datetime",
      </if>
      <if test="dischargeDept != null">
        "discharge_dept",
      </if>
      <if test="dischargeWay != null">
        "discharge_way",
      </if>
      <if test="inDays != null">
        "in_days",
      </if>
      <if test="totalCosts != null">
        "total_costs",
      </if>
      <if test="selfPay != null">
        "self_pay",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="patientSn != null">
        #{patientSn,jdbcType=VARCHAR},
      </if>
      <if test="visitSn != null">
        #{visitSn,jdbcType=VARCHAR},
      </if>
      <if test="pkid != null">
        #{pkid,jdbcType=VARCHAR},
      </if>
      <if test="visitType != null">
        #{visitType,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAdmissionDatetime != null">
        #{visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="visitOrAdmissionDept != null">
        #{visitOrAdmissionDept,jdbcType=VARCHAR},
      </if>
      <if test="visitOrAttendingDoctor != null">
        #{visitOrAttendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="visitAge != null">
        #{visitAge,jdbcType=INTEGER},
      </if>
      <if test="admissionWay != null">
        #{admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="dischargeDatetime != null">
        #{dischargeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="dischargeDept != null">
        #{dischargeDept,jdbcType=VARCHAR},
      </if>
      <if test="dischargeWay != null">
        #{dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="inDays != null">
        #{inDays,jdbcType=INTEGER},
      </if>
      <if test="totalCosts != null">
        #{totalCosts,jdbcType=DOUBLE},
      </if>
      <if test="selfPay != null">
        #{selfPay,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.disease.model.VisitInformationExample" resultType="java.lang.Long">
    select count(*) from "public"."visit_information"
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update "public"."visit_information"
    <set>
      <if test="record.patientSn != null">
        "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      </if>
      <if test="record.visitSn != null">
        "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      </if>
      <if test="record.pkid != null">
        "pkid" = #{record.pkid,jdbcType=VARCHAR},
      </if>
      <if test="record.visitType != null">
        "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitOrAdmissionDatetime != null">
        "visit_or_admission_datetime" = #{record.visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.visitOrAdmissionDept != null">
        "visit_or_admission_dept" = #{record.visitOrAdmissionDept,jdbcType=VARCHAR},
      </if>
      <if test="record.visitOrAttendingDoctor != null">
        "visit_or_attending_doctor" = #{record.visitOrAttendingDoctor,jdbcType=VARCHAR},
      </if>
      <if test="record.visitAge != null">
        "visit_age" = #{record.visitAge,jdbcType=INTEGER},
      </if>
      <if test="record.admissionWay != null">
        "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeDatetime != null">
        "discharge_datetime" = #{record.dischargeDatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dischargeDept != null">
        "discharge_dept" = #{record.dischargeDept,jdbcType=VARCHAR},
      </if>
      <if test="record.dischargeWay != null">
        "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      </if>
      <if test="record.inDays != null">
        "in_days" = #{record.inDays,jdbcType=INTEGER},
      </if>
      <if test="record.totalCosts != null">
        "total_costs" = #{record.totalCosts,jdbcType=DOUBLE},
      </if>
      <if test="record.selfPay != null">
        "self_pay" = #{record.selfPay,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update "public"."visit_information"
    set "patient_sn" = #{record.patientSn,jdbcType=VARCHAR},
      "visit_sn" = #{record.visitSn,jdbcType=VARCHAR},
      "pkid" = #{record.pkid,jdbcType=VARCHAR},
      "visit_type" = #{record.visitType,jdbcType=VARCHAR},
      "visit_or_admission_datetime" = #{record.visitOrAdmissionDatetime,jdbcType=TIMESTAMP},
      "visit_or_admission_dept" = #{record.visitOrAdmissionDept,jdbcType=VARCHAR},
      "visit_or_attending_doctor" = #{record.visitOrAttendingDoctor,jdbcType=VARCHAR},
      "visit_age" = #{record.visitAge,jdbcType=INTEGER},
      "admission_way" = #{record.admissionWay,jdbcType=VARCHAR},
      "discharge_datetime" = #{record.dischargeDatetime,jdbcType=TIMESTAMP},
      "discharge_dept" = #{record.dischargeDept,jdbcType=VARCHAR},
      "discharge_way" = #{record.dischargeWay,jdbcType=VARCHAR},
      "in_days" = #{record.inDays,jdbcType=INTEGER},
      "total_costs" = #{record.totalCosts,jdbcType=DOUBLE},
      "self_pay" = #{record.selfPay,jdbcType=DOUBLE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <select id="getPatientVisitTimeLineRecord" resultType="com.haoys.disease.domain.vo.PatientVisitTimeLineVo">
    select
      "patient_sn", "visit_sn", "visit_type", "visit_or_admission_datetime",
      "visit_or_admission_dept", "visit_or_attending_doctor"
    from "public"."visit_information"
    where "patient_sn" = #{patientSn}
    <if test="visitSn != null and visitSn != ''">
      and "visit_sn" = #{visitSn}
    </if>
    order by "visit_or_admission_datetime" asc
  </select>
</mapper>