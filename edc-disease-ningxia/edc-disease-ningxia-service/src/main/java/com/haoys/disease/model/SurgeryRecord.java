package com.haoys.disease.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class SurgeryRecord implements Serializable {
    @ApiModelProperty(value = "患者标识")
    private String patientSn;

    @ApiModelProperty(value = "就诊标识")
    private String visitSn;

    private String pkid;

    @ApiModelProperty(value = "手术开始时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationBeginTime;

    @ApiModelProperty(value = "手术名称")
    private String operationName;

    @ApiModelProperty(value = "术前诊断")
    private String diagPreoperation;

    @ApiModelProperty(value = "术后诊断")
    private String diagPostoperation;

    private static final long serialVersionUID = 1L;

    public String getPatientSn() {
        return patientSn;
    }

    public void setPatientSn(String patientSn) {
        this.patientSn = patientSn;
    }

    public String getVisitSn() {
        return visitSn;
    }

    public void setVisitSn(String visitSn) {
        this.visitSn = visitSn;
    }

    public String getPkid() {
        return pkid;
    }

    public void setPkid(String pkid) {
        this.pkid = pkid;
    }

    public Date getOperationBeginTime() {
        return operationBeginTime;
    }

    public void setOperationBeginTime(Date operationBeginTime) {
        this.operationBeginTime = operationBeginTime;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getDiagPreoperation() {
        return diagPreoperation;
    }

    public void setDiagPreoperation(String diagPreoperation) {
        this.diagPreoperation = diagPreoperation;
    }

    public String getDiagPostoperation() {
        return diagPostoperation;
    }

    public void setDiagPostoperation(String diagPostoperation) {
        this.diagPostoperation = diagPostoperation;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", patientSn=").append(patientSn);
        sb.append(", visitSn=").append(visitSn);
        sb.append(", pkid=").append(pkid);
        sb.append(", operationBeginTime=").append(operationBeginTime);
        sb.append(", operationName=").append(operationName);
        sb.append(", diagPreoperation=").append(diagPreoperation);
        sb.append(", diagPostoperation=").append(diagPostoperation);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}