package com.haoys.disease.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PatientModelVariableVo {

    @ApiModelProperty(value = "表单模型id")
    private String modelSourceId;

    @ApiModelProperty(value = "表单模型code")
    private String modelSourceCode;

    @ApiModelProperty(value = "字段code")
    private String variableCode;

    @ApiModelProperty(value = "字段ID")
    private String variableId;

    @ApiModelProperty(value = "字段名称")
    private String variableName;

    @ApiModelProperty(value = "字段类型")
    private String variableType;

    @ApiModelProperty(value = "字段下拉框数据")
    private String comboboxData;

    @ApiModelProperty(value = "字段默认值")
    private String defaultValue;

    @ApiModelProperty(value = "默认查询字段")
    private Boolean defaultQuery = false;

    @ApiModelProperty(value = "字段类型")
    private Integer maxLength;

    @ApiModelProperty(value = "是否为空")
    private Boolean enableNull;

    @ApiModelProperty(value = "是否唯一约束")
    private Boolean enableUnique;

    @ApiModelProperty(value = "是否自定义表单字段")
    private Boolean customVariable;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "描述信息")
    private String description;

}
