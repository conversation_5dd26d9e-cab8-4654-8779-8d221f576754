package com.haoys.disease.domain.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PatientModelVariableParam {

    @ApiModelProperty(value = "表单来源批次")
    private String modelSourceId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "变量ID")
    private String variableId;

    @ApiModelProperty(value = "变量名称")
    private String variableName;

    @ApiModelProperty(value = "变量类型")
    private String variableType;

    @ApiModelProperty(value = "变量下拉框数据")
    private String comboboxData;

    @ApiModelProperty(value = "变量类型")
    private Integer maxLength;

    @ApiModelProperty(value = "是否为空")
    private Boolean enableNull;

    @ApiModelProperty(value = "是否唯一约束")
    private Boolean enableUnique;

    @ApiModelProperty(value = "是否自定义表单变量")
    private Boolean customVariable;

    @ApiModelProperty(value = "字段排序")
    private Integer sort;

    @ApiModelProperty(value = "描述信息")
    private String description;

    @ApiModelProperty(value = "1-删除 0-正常")
    private Boolean deleted;

}
