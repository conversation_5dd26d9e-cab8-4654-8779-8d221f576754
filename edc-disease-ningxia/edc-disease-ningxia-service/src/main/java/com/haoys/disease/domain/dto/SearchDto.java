package com.haoys.disease.domain.dto;

import com.haoys.disease.domain.emums.PatientNaPiSearchEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SearchDto {

    @ApiModelProperty(value = "表单Code")
    private String formCode;

    @ApiModelProperty(value = "字段Code")
    private String fieldCode;

    @ApiModelProperty(value = "字段类型")
    private String variableType;

    @ApiModelProperty(value = "字段格式化类型")
    private String formatType;

    @ApiModelProperty(value = "搜索类型 与-add ,或-or,非-not,默认与关系")
    private String searchType= PatientNaPiSearchEnum.AND.getCode();

    @ApiModelProperty(value = "搜索值")
    private String searchValue;

    @ApiModelProperty(value = "搜索值1")
    private String searchValue1;

    @ApiModelProperty(value = "值类型")
    /**
     * 详细的规则见  常量类：DefineConstant   sql查询运算符号：DefineConstant.CONTAINS_CODE  DefineConstant.NOT_CONTAINS_CODE
     * DefineConstant.LESS_THAN_CODE  DefineConstant.GREATER_THAN_CODE
     * DefineConstant.EQUAL_CODE  DefineConstant.NOT_EQUAL_CODE  DefineConstant.LESS_THAN_EQUAL_CODE
     * DefineConstant. GREATER_THAN_EQUAL_CODE
     */
    private String valueType;

    @ApiModelProperty(value = "回显信息（前端回显使用）")
    private List<Map<String,Object>> fieldList;

}
