package com.haoys.disease.service;

import com.haoys.disease.domain.param.PatientMedicalRecordSearchParam;
import com.haoys.disease.domain.wrapper.PatientBaseWrapper;
import com.haoys.disease.model.PatientVariableRecord;
import com.haoys.user.common.api.CustomResult;

import java.util.List;

public interface PatientVariableRecordService {


    List<PatientBaseWrapper> getPatientMedicalRecordForPage(String searchType, String querySegment, String modelSourceCode, String searchWord,
                                                            List<PatientMedicalRecordSearchParam.SearcherModeRule> searcherModeAndRuleList,
                                                            List<PatientMedicalRecordSearchParam.SearcherModeRule> searcherModeNotRuleList,
                                                            String executeSqlValue, List<String> patientIds, String startDate, String endDate, String sortField, String sortType);

    /**
     * 同步患者表单记录数据到扩展表
     * @param patientId
     * @return
     */
    CustomResult savePatientModelVariableRecordByPatientId(String patientId);
    
    PatientVariableRecord getPatientModelVariableRecord(String patientDiseaseType, String patientId, String modelSourceCode, String variableCode);
    
    CustomResult savePatientModelVariableRecordDataSourceForMongodb(String collectionName);
}
