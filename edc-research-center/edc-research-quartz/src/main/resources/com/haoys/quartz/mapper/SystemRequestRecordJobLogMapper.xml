<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.quartz.mapper.SystemRequestRecordJobLogMapper">

    <resultMap type="com.haoys.quartz.domain.SystemRequestRecordJobLog" id="SystemRequestRecordJobLogResult">
        <result property="logId" column="log_id"/>
        <result property="jobId" column="job_id"/>
        <result property="jobName" column="job_name"/>
        <result property="jobGroup" column="job_group"/>
        <result property="invokeTarget" column="invoke_target"/>
        <result property="jobMessage" column="job_message"/>
        <result property="status" column="status"/>
        <result property="exceptionInfo" column="exception_info"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="executeTime" column="execute_time"/>
        <result property="createTime" column="create_time"/>
        <result property="jobType" column="job_type"/>
        <result property="jobParams" column="job_params"/>
        <result property="executeResult" column="execute_result"/>
        <result property="executeNode" column="execute_node"/>
        <result property="serverIp" column="server_ip"/>
        <result property="serverName" column="server_name"/>
        <result property="processId" column="process_id"/>
        <result property="threadId" column="thread_id"/>
        <result property="threadName" column="thread_name"/>
        <result property="memoryUsage" column="memory_usage"/>
        <result property="cpuUsage" column="cpu_usage"/>
        <result property="processedRecords" column="processed_records"/>
        <result property="successRecords" column="success_records"/>
        <result property="failureRecords" column="failure_records"/>
        <result property="skippedRecords" column="skipped_records"/>
        <result property="dbConnections" column="db_connections"/>
        <result property="cacheHitRate" column="cache_hit_rate"/>
        <result property="networkIo" column="network_io"/>
        <result property="diskIo" column="disk_io"/>
        <result property="priority" column="priority"/>
        <result property="retryCount" column="retry_count"/>
        <result property="lastRetry" column="last_retry"/>
        <result property="triggerType" column="trigger_type"/>
        <result property="triggerName" column="trigger_name"/>
        <result property="triggerGroup" column="trigger_group"/>
        <result property="scheduledTime" column="scheduled_time"/>
        <result property="actualTime" column="actual_time"/>
        <result property="delayTime" column="delay_time"/>
        <result property="jobVersion" column="job_version"/>
        <result property="environment" column="environment"/>
        <result property="configSnapshot" column="config_snapshot"/>
        <result property="executeContext" column="execute_context"/>
        <result property="businessData" column="business_data"/>
        <result property="ext1" column="ext1"/>
        <result property="ext2" column="ext2"/>
        <result property="ext3" column="ext3"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <sql id="selectJobLogVo">
        select log_id, job_id, job_name, job_group, invoke_target, job_message, status, exception_info,
               start_time, end_time, execute_time, create_time, job_type, job_params, execute_result,
               execute_node, server_ip, server_name, process_id, thread_id, thread_name, memory_usage,
               cpu_usage, processed_records, success_records, failure_records, skipped_records,
               db_connections, cache_hit_rate, network_io, disk_io, priority, retry_count, last_retry,
               trigger_type, trigger_name, trigger_group, scheduled_time, actual_time, delay_time,
               job_version, environment, config_snapshot, execute_context, business_data,
               ext1, ext2, ext3, tenant_id
        from system_request_record_job_log
    </sql>

    <select id="selectJobLogList" parameterType="com.haoys.quartz.domain.SystemRequestRecordJobLog" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        <where>
            <if test="jobName != null and jobName != ''">
                AND job_name like concat('%', #{jobName}, '%')
            </if>
            <if test="jobGroup != null and jobGroup != ''">
                AND job_group = #{jobGroup}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="jobType != null">
                AND job_type = #{jobType}
            </if>
            <if test="executeNode != null and executeNode != ''">
                AND execute_node = #{executeNode}
            </if>
            <if test="startTime != null">
                AND start_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND end_time &lt;= #{endTime}
            </if>
        </where>
        order by log_id desc
    </select>

    <select id="selectJobLogAll" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        order by log_id desc
    </select>

    <select id="selectJobLogById" parameterType="Long" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where log_id = #{logId}
    </select>

    <select id="selectJobLogListByJobId" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_id = #{jobId}
        order by log_id desc
    </select>

    <select id="selectLatestJobLogByJobId" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_id = #{jobId}
        order by log_id desc
        limit 1
    </select>

    <select id="selectLatestSuccessJobLogByJobId" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_id = #{jobId} and status = '0'
        order by log_id desc
        limit 1
    </select>

    <select id="selectLatestFailureJobLogByJobId" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_id = #{jobId} and status = '1'
        order by log_id desc
        limit 1
    </select>

    <select id="selectFailureJobLogList" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where status = '1'
        order by log_id desc
    </select>

    <select id="selectSlowJobLogList" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where execute_time &gt; #{threshold}
        order by execute_time desc
    </select>

    <select id="selectJobLogListByTimeRange" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where start_time between #{startTime} and #{endTime}
        order by log_id desc
    </select>

    <select id="selectJobLogListByType" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where job_type = #{jobType}
        order by log_id desc
    </select>

    <select id="selectJobLogListByNode" resultMap="SystemRequestRecordJobLogResult">
        <include refid="selectJobLogVo"/>
        where execute_node = #{executeNode}
        order by log_id desc
    </select>

    <select id="selectJobExecuteStatistics" resultType="map">
        select 
            count(*) as totalCount,
            sum(case when status = '0' then 1 else 0 end) as successCount,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            case 
                when count(*) > 0 then round(sum(case when status = '0' then 1 else 0 end) * 100.0 / count(*), 2)
                else 0 
            end as successRate,
            avg(execute_time) as avgExecuteTime,
            max(execute_time) as maxExecuteTime,
            min(execute_time) as minExecuteTime,
            sum(processed_records) as totalProcessedRecords,
            sum(success_records) as totalSuccessRecords,
            sum(failure_records) as totalFailureRecords
        from system_request_record_job_log 
        where job_id = #{jobId}
    </select>

    <select id="selectAllJobExecuteStatistics" resultType="map">
        select 
            count(*) as totalCount,
            sum(case when status = '0' then 1 else 0 end) as successCount,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            case 
                when count(*) > 0 then round(sum(case when status = '0' then 1 else 0 end) * 100.0 / count(*), 2)
                else 0 
            end as successRate,
            avg(execute_time) as avgExecuteTime,
            max(execute_time) as maxExecuteTime,
            min(execute_time) as minExecuteTime,
            sum(processed_records) as totalProcessedRecords,
            sum(success_records) as totalSuccessRecords,
            sum(failure_records) as totalFailureRecords
        from system_request_record_job_log
    </select>

    <select id="selectJobExecuteTrend" resultType="map">
        select 
            date_format(start_time, '%Y-%m-%d %H:00:00') as executeHour,
            count(*) as executeCount,
            sum(case when status = '0' then 1 else 0 end) as successCount,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            avg(execute_time) as avgExecuteTime
        from system_request_record_job_log
        where job_id = #{jobId} and start_time >= date_sub(now(), interval #{days} day)
        group by date_format(start_time, '%Y-%m-%d %H:00:00')
        order by executeHour desc
    </select>

    <select id="selectAllJobExecuteTrend" resultType="map">
        select 
            date_format(start_time, '%Y-%m-%d') as executeDate,
            count(*) as executeCount,
            sum(case when status = '0' then 1 else 0 end) as successCount,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            avg(execute_time) as avgExecuteTime
        from system_request_record_job_log
        where start_time >= date_sub(now(), interval #{days} day)
        group by date_format(start_time, '%Y-%m-%d')
        order by executeDate desc
    </select>

    <select id="selectJobPerformanceStatistics" resultType="map">
        select 
            avg(execute_time) as avgExecuteTime,
            max(execute_time) as maxExecuteTime,
            min(execute_time) as minExecuteTime,
            avg(memory_usage) as avgMemoryUsage,
            max(memory_usage) as maxMemoryUsage,
            avg(cpu_usage) as avgCpuUsage,
            max(cpu_usage) as maxCpuUsage,
            avg(processed_records) as avgProcessedRecords,
            sum(processed_records) as totalProcessedRecords
        from system_request_record_job_log
        where job_id = #{jobId} and status = '0'
    </select>

    <select id="selectJobPerformanceRanking" resultType="map">
        select 
            job_id as jobId,
            job_name as jobName,
            job_group as jobGroup,
            count(*) as executeCount,
            avg(execute_time) as avgExecuteTime,
            max(execute_time) as maxExecuteTime,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            case 
                when count(*) > 0 then round(sum(case when status = '0' then 1 else 0 end) * 100.0 / count(*), 2)
                else 0 
            end as successRate
        from system_request_record_job_log
        where start_time >= date_sub(now(), interval 7 day)
        group by job_id, job_name, job_group
        having executeCount > 0
        order by avgExecuteTime desc
        limit #{limit}
    </select>

    <select id="selectJobErrorStatistics" resultType="map">
        select 
            date_format(start_time, '%Y-%m-%d') as errorDate,
            count(*) as errorCount,
            job_name,
            job_group,
            substring(exception_info, 1, 200) as errorMessage
        from system_request_record_job_log
        where status = '1' and start_time >= date_sub(now(), interval #{days} day)
        group by date_format(start_time, '%Y-%m-%d'), job_name, job_group, substring(exception_info, 1, 200)
        order by errorDate desc, errorCount desc
    </select>

    <select id="selectJobNodeStatistics" resultType="map">
        select 
            execute_node as nodeId,
            count(*) as executeCount,
            sum(case when status = '0' then 1 else 0 end) as successCount,
            sum(case when status = '1' then 1 else 0 end) as failureCount,
            avg(execute_time) as avgExecuteTime,
            max(start_time) as lastExecuteTime
        from system_request_record_job_log
        where execute_node is not null and execute_node != ''
        group by execute_node
        order by executeCount desc
    </select>

    <select id="selectJobExecuteTimeDistribution" resultType="map">
        select 
            case 
                when execute_time &lt; 1000 then '&lt;1s'
                when execute_time &lt; 5000 then '1-5s'
                when execute_time &lt; 10000 then '5-10s'
                when execute_time &lt; 30000 then '10-30s'
                when execute_time &lt; 60000 then '30-60s'
                else '&gt;60s'
            end as timeRange,
            count(*) as count
        from system_request_record_job_log
        where job_id = #{jobId} and execute_time > 0
        group by case 
                when execute_time &lt; 1000 then '&lt;1s'
                when execute_time &lt; 5000 then '1-5s'
                when execute_time &lt; 10000 then '5-10s'
                when execute_time &lt; 30000 then '10-30s'
                when execute_time &lt; 60000 then '30-60s'
                else '&gt;60s'
            end
        order by min(execute_time)
    </select>

    <select id="selectSystemResourceStatistics" resultType="map">
        select 
            date_format(start_time, '%Y-%m-%d %H:00:00') as statTime,
            avg(memory_usage) as avgMemoryUsage,
            max(memory_usage) as maxMemoryUsage,
            avg(cpu_usage) as avgCpuUsage,
            max(cpu_usage) as maxCpuUsage,
            avg(db_connections) as avgDbConnections,
            max(db_connections) as maxDbConnections
        from system_request_record_job_log
        where start_time >= date_sub(now(), interval #{days} day)
        group by date_format(start_time, '%Y-%m-%d %H:00:00')
        order by statTime desc
    </select>

    <select id="selectJobLogCount" resultType="long">
        select count(*) from system_request_record_job_log
    </select>

    <select id="selectJobLogCountByJobId" resultType="long">
        select count(*) from system_request_record_job_log where job_id = #{jobId}
    </select>

    <select id="selectJobLogCountByStatus" resultType="long">
        select count(*) from system_request_record_job_log where status = #{status}
    </select>

    <select id="selectTodayJobLogCount" resultType="long">
        select count(*) from system_request_record_job_log 
        where date(start_time) = curdate()
    </select>

    <select id="selectTodaySuccessJobLogCount" resultType="long">
        select count(*) from system_request_record_job_log 
        where date(start_time) = curdate() and status = '0'
    </select>

    <select id="selectTodayFailureJobLogCount" resultType="long">
        select count(*) from system_request_record_job_log 
        where date(start_time) = curdate() and status = '1'
    </select>

    <select id="selectAvgExecuteTime" resultType="Long">
        select avg(execute_time) from system_request_record_job_log 
        where job_id = #{jobId} and execute_time > 0
    </select>

    <select id="selectMaxExecuteTime" resultType="Long">
        select max(execute_time) from system_request_record_job_log 
        where job_id = #{jobId}
    </select>

    <select id="selectMinExecuteTime" resultType="Long">
        select min(execute_time) from system_request_record_job_log 
        where job_id = #{jobId} and execute_time > 0
    </select>

    <select id="selectJobSuccessRate" resultType="Double">
        select case 
            when count(*) > 0 then round(sum(case when status = '0' then 1 else 0 end) * 100.0 / count(*), 2)
            else 0 
        end as successRate
        from system_request_record_job_log 
        where job_id = #{jobId}
    </select>

    <select id="selectRecentJobExecuteStatus" resultType="String">
        select status from system_request_record_job_log 
        where job_id = #{jobId}
        order by log_id desc
        limit #{limit}
    </select>

    <insert id="insertJobLog" parameterType="com.haoys.quartz.domain.SystemRequestRecordJobLog" useGeneratedKeys="true" keyProperty="logId">
        insert into system_request_record_job_log(
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="jobId != null">job_id,</if>
                <if test="jobName != null and jobName != ''">job_name,</if>
                <if test="jobGroup != null and jobGroup != ''">job_group,</if>
                <if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
                <if test="jobMessage != null">job_message,</if>
                <if test="status != null and status != ''">status,</if>
                <if test="exceptionInfo != null">exception_info,</if>
                <if test="startTime != null">start_time,</if>
                <if test="endTime != null">end_time,</if>
                <if test="executeTime != null">execute_time,</if>
                <if test="jobType != null">job_type,</if>
                <if test="jobParams != null">job_params,</if>
                <if test="executeResult != null">execute_result,</if>
                <if test="executeNode != null and executeNode != ''">execute_node,</if>
                <if test="serverIp != null and serverIp != ''">server_ip,</if>
                <if test="serverName != null and serverName != ''">server_name,</if>
                <if test="processId != null and processId != ''">process_id,</if>
                <if test="threadId != null and threadId != ''">thread_id,</if>
                <if test="threadName != null and threadName != ''">thread_name,</if>
                <if test="memoryUsage != null">memory_usage,</if>
                <if test="cpuUsage != null">cpu_usage,</if>
                <if test="processedRecords != null">processed_records,</if>
                <if test="successRecords != null">success_records,</if>
                <if test="failureRecords != null">failure_records,</if>
                <if test="skippedRecords != null">skipped_records,</if>
                <if test="dbConnections != null">db_connections,</if>
                <if test="cacheHitRate != null">cache_hit_rate,</if>
                <if test="networkIo != null">network_io,</if>
                <if test="diskIo != null">disk_io,</if>
                <if test="priority != null">priority,</if>
                <if test="retryCount != null">retry_count,</if>
                <if test="lastRetry != null">last_retry,</if>
                <if test="triggerType != null and triggerType != ''">trigger_type,</if>
                <if test="triggerName != null and triggerName != ''">trigger_name,</if>
                <if test="triggerGroup != null and triggerGroup != ''">trigger_group,</if>
                <if test="scheduledTime != null">scheduled_time,</if>
                <if test="actualTime != null">actual_time,</if>
                <if test="delayTime != null">delay_time,</if>
                <if test="jobVersion != null and jobVersion != ''">job_version,</if>
                <if test="environment != null and environment != ''">environment,</if>
                <if test="configSnapshot != null">config_snapshot,</if>
                <if test="executeContext != null">execute_context,</if>
                <if test="businessData != null">business_data,</if>
                <if test="ext1 != null and ext1 != ''">ext1,</if>
                <if test="ext2 != null and ext2 != ''">ext2,</if>
                <if test="ext3 != null and ext3 != ''">ext3,</if>
                <if test="tenantId != null and tenantId != ''">tenant_id,</if>
                create_time
            </trim>
        )values(
            <trim prefix="" suffix="" suffixOverrides=",">
                <if test="jobId != null">#{jobId},</if>
                <if test="jobName != null and jobName != ''">#{jobName},</if>
                <if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
                <if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
                <if test="jobMessage != null">#{jobMessage},</if>
                <if test="status != null and status != ''">#{status},</if>
                <if test="exceptionInfo != null">#{exceptionInfo},</if>
                <if test="startTime != null">#{startTime},</if>
                <if test="endTime != null">#{endTime},</if>
                <if test="executeTime != null">#{executeTime},</if>
                <if test="jobType != null">#{jobType},</if>
                <if test="jobParams != null">#{jobParams},</if>
                <if test="executeResult != null">#{executeResult},</if>
                <if test="executeNode != null and executeNode != ''">#{executeNode},</if>
                <if test="serverIp != null and serverIp != ''">#{serverIp},</if>
                <if test="serverName != null and serverName != ''">#{serverName},</if>
                <if test="processId != null and processId != ''">#{processId},</if>
                <if test="threadId != null and threadId != ''">#{threadId},</if>
                <if test="threadName != null and threadName != ''">#{threadName},</if>
                <if test="memoryUsage != null">#{memoryUsage},</if>
                <if test="cpuUsage != null">#{cpuUsage},</if>
                <if test="processedRecords != null">#{processedRecords},</if>
                <if test="successRecords != null">#{successRecords},</if>
                <if test="failureRecords != null">#{failureRecords},</if>
                <if test="skippedRecords != null">#{skippedRecords},</if>
                <if test="dbConnections != null">#{dbConnections},</if>
                <if test="cacheHitRate != null">#{cacheHitRate},</if>
                <if test="networkIo != null">#{networkIo},</if>
                <if test="diskIo != null">#{diskIo},</if>
                <if test="priority != null">#{priority},</if>
                <if test="retryCount != null">#{retryCount},</if>
                <if test="lastRetry != null">#{lastRetry},</if>
                <if test="triggerType != null and triggerType != ''">#{triggerType},</if>
                <if test="triggerName != null and triggerName != ''">#{triggerName},</if>
                <if test="triggerGroup != null and triggerGroup != ''">#{triggerGroup},</if>
                <if test="scheduledTime != null">#{scheduledTime},</if>
                <if test="actualTime != null">#{actualTime},</if>
                <if test="delayTime != null">#{delayTime},</if>
                <if test="jobVersion != null and jobVersion != ''">#{jobVersion},</if>
                <if test="environment != null and environment != ''">#{environment},</if>
                <if test="configSnapshot != null">#{configSnapshot},</if>
                <if test="executeContext != null">#{executeContext},</if>
                <if test="businessData != null">#{businessData},</if>
                <if test="ext1 != null and ext1 != ''">#{ext1},</if>
                <if test="ext2 != null and ext2 != ''">#{ext2},</if>
                <if test="ext3 != null and ext3 != ''">#{ext3},</if>
                <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
                now()
            </trim>
        )
    </insert>

    <update id="updateJobLogEndTime">
        update system_request_record_job_log 
        set end_time = #{endTime}, execute_time = #{executeTime}
        where log_id = #{logId}
    </update>

    <update id="updateJobLogResult">
        update system_request_record_job_log 
        set status = #{status}, execute_result = #{executeResult}, exception_info = #{exceptionInfo}
        where log_id = #{logId}
    </update>

    <delete id="deleteJobLogByIds">
        delete from system_request_record_job_log where log_id in
        <foreach item="logId" collection="logIds" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="deleteJobLogById" parameterType="Long">
        delete from system_request_record_job_log where log_id = #{logId}
    </delete>

    <delete id="deleteJobLogByJobId">
        delete from system_request_record_job_log where job_id = #{jobId}
    </delete>

    <delete id="cleanJobLog">
        delete from system_request_record_job_log
    </delete>

    <delete id="cleanExpiredJobLog">
        delete from system_request_record_job_log 
        where create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

    <delete id="cleanExpiredJobLogByJobId">
        delete from system_request_record_job_log 
        where job_id = #{jobId} and create_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>
