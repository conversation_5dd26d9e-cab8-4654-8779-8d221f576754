package com.haoys.quartz.util;

import com.haoys.user.common.spring.SpringUtils;
import com.haoys.quartz.domain.SystemRequestRecordJob;
import com.haoys.quartz.domain.SystemRequestRecordJobLog;
import com.haoys.quartz.service.ISystemRequestRecordJobLogService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 抽象quartz调用
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public abstract class AbstractQuartzJob implements Job {
    
    private static final Logger log = LoggerFactory.getLogger(AbstractQuartzJob.class);

    /**
     * 线程本地变量
     */
    private static ThreadLocal<Date> threadLocal = new ThreadLocal<>();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        SystemRequestRecordJob job = (SystemRequestRecordJob) context.getMergedJobDataMap().get("JOB_PARAM_KEY");
        
        ISystemRequestRecordJobLogService jobLogService = SpringUtils.getBean(ISystemRequestRecordJobLogService.class);
        
        Long logId = null;
        try {
            // 记录任务开始执行
            logId = jobLogService.createJobStartLog(
                job.getJobId(), 
                job.getJobName(), 
                job.getJobGroup(), 
                job.getInvokeTarget(), 
                job.getJobParams()
            );
            
            // 执行任务
            before(context, job);
            doExecute(context, job);
            after(context, job, null);
            
            // 记录执行成功
            if (logId != null) {
                jobLogService.completeJobLog(logId, "0", "任务执行成功", null, 0L, 0L, 0L);
            }
            
        } catch (Exception e) {
            log.error("任务执行异常 - 任务ID: {}, 任务名称: {}", job.getJobId(), job.getJobName(), e);
            after(context, job, e);
            
            // 记录执行失败
            if (logId != null) {
                jobLogService.completeJobLog(logId, "1", "任务执行失败", getExceptionMessage(e), 0L, 0L, 0L);
            }
            
            throw new JobExecutionException(e);
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param job     系统计划任务
     */
    protected void before(JobExecutionContext context, SystemRequestRecordJob job) {
        threadLocal.set(new Date());
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param job     系统计划任务
     */
    protected void after(JobExecutionContext context, SystemRequestRecordJob job, Exception e) {
        Date startTime = threadLocal.get();
        threadLocal.remove();

        final SystemRequestRecordJobLog jobLog = new SystemRequestRecordJobLog();
        jobLog.setJobId(job.getJobId());
        jobLog.setJobName(job.getJobName());
        jobLog.setJobGroup(job.getJobGroup());
        jobLog.setInvokeTarget(job.getInvokeTarget());
        jobLog.setStartTime(startTime);
        jobLog.setEndTime(new Date());
        long runTime = jobLog.getEndTime().getTime() - jobLog.getStartTime().getTime();
        jobLog.setExecuteTime(runTime);
        
        if (e != null) {
            jobLog.setStatus("1");
            jobLog.setExceptionInfo(getExceptionMessage(e));
            jobLog.setJobMessage("执行失败");
        } else {
            jobLog.setStatus("0");
            jobLog.setJobMessage("执行成功");
        }

        // 写入数据库当中
        try {
            ISystemRequestRecordJobLogService jobLogService = SpringUtils.getBean(ISystemRequestRecordJobLogService.class);
            jobLogService.insertJobLog(jobLog);
        } catch (Exception ex) {
            log.error("记录任务执行日志失败", ex);
        }
    }

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param job     系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, SystemRequestRecordJob job) throws Exception;
    
    /**
     * 获取异常信息
     */
    private String getExceptionMessage(Exception e) {
        if (e == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append(e.getClass().getSimpleName()).append(": ").append(e.getMessage());
        
        // 添加堆栈信息（限制长度）
        StackTraceElement[] stackTrace = e.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            sb.append("\n");
            for (int i = 0; i < Math.min(5, stackTrace.length); i++) {
                sb.append("\tat ").append(stackTrace[i].toString()).append("\n");
            }
            if (stackTrace.length > 5) {
                sb.append("\t... ").append(stackTrace.length - 5).append(" more");
            }
        }
        
        // 限制总长度
        String result = sb.toString();
        if (result.length() > 2000) {
            result = result.substring(0, 2000) + "...";
        }
        
        return result;
    }
}
