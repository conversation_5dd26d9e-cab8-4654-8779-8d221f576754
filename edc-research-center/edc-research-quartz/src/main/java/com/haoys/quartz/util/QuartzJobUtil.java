package com.haoys.quartz.util;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import org.quartz.*;

/**
 * Quartz定时任务工具类
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public class QuartzJobUtil {
    
    /**
     * 得到quartz任务类
     *
     * @param job 执行计划
     * @return 具体执行任务类
     */
    private static Class<? extends Job> getQuartzJobClass(SystemRequestRecordJob job) {
        boolean isConcurrent = "0".equals(job.getConcurrent());
        return isConcurrent ? QuartzJobExecution.class : QuartzJobExecutionDisallowConcurrency.class;
    }

    /**
     * 构建任务触发对象
     */
    public static TriggerKey getTriggerKey(Long jobId, String jobGroup) {
        return TriggerKey.triggerKey("TASK_" + jobId, jobGroup);
    }

    /**
     * 构建任务键对象
     */
    public static JobKey getJobKey(Long jobId, String jobGroup) {
        return JobKey.jobKey("TASK_" + jobId, jobGroup);
    }

    /**
     * 创建定时任务
     */
    public static void createScheduleJob(Scheduler scheduler, SystemRequestRecordJob job) throws SchedulerException {
        Class<? extends Job> jobClass = getQuartzJobClass(job);
        // 构建job信息
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();

        // 表达式调度构建器
        CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
        cronScheduleBuilder = handleCronScheduleMisfirePolicy(job, cronScheduleBuilder);

        // 按新的cronExpression表达式构建一个新的trigger
        CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup))
                .withSchedule(cronScheduleBuilder).build();

        // 放入参数，运行时的方法可以获取
        jobDetail.getJobDataMap().put("JOB_PARAM_KEY", job);

        // 判断是否存在
        if (scheduler.checkExists(getJobKey(jobId, jobGroup))) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(getJobKey(jobId, jobGroup));
        }

        // 判断任务是否过期
        try {
            if (CronExpression.isValidExpression(job.getCronExpression())
                    && new CronExpression(job.getCronExpression()).getNextValidTimeAfter(new java.util.Date()) != null) {
                scheduler.scheduleJob(jobDetail, trigger);
            }
        } catch (java.text.ParseException e) {
            throw new SchedulerException("Invalid cron expression: " + job.getCronExpression(), e);
        }

        // 暂停任务
        if ("1".equals(job.getStatus())) {
            scheduler.pauseJob(QuartzJobUtil.getJobKey(jobId, jobGroup));
        }
    }

    /**
     * 设置定时任务策略
     */
    public static CronScheduleBuilder handleCronScheduleMisfirePolicy(SystemRequestRecordJob job, CronScheduleBuilder cb) {
        switch (job.getMisfirePolicy()) {
            case "1":
                return cb.withMisfireHandlingInstructionIgnoreMisfires();
            case "2":
                return cb.withMisfireHandlingInstructionFireAndProceed();
            case "3":
                return cb.withMisfireHandlingInstructionDoNothing();
            default:
                throw new RuntimeException("The task misfire policy '" + job.getMisfirePolicy()
                        + "' cannot be used in cron schedule tasks");
        }
    }
}
