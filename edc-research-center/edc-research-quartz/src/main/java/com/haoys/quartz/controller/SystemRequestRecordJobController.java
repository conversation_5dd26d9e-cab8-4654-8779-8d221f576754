package com.haoys.quartz.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.quartz.domain.SystemRequestRecordJob;
import com.haoys.quartz.service.ISystemRequestRecordJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务管理控制器
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Api(tags = "系统访问记录定时任务管理")
@RestController
@RequestMapping("/api/quartz/job")
@Slf4j
public class SystemRequestRecordJobController {
    
    @Autowired
    private ISystemRequestRecordJobService jobService;

    @Value("${request.access.log.management.secret-key:EDC-QUARTZ-MANAGEMENT-SECRET-2025}")
    private String quartzSecretKey;

    /**
     * 定时任务管理页面
     */
    @ApiOperation("定时任务管理页面")
    @GetMapping("/management")
    public String quartzManagementPage(HttpServletRequest request) {
        try {
            // 尝试从classpath读取HTML文件
            org.springframework.core.io.ClassPathResource resource = new org.springframework.core.io.ClassPathResource("static/quartz-management/management.html");
            if (resource.exists()) {
                String content = org.springframework.util.StreamUtils.copyToString(resource.getInputStream(), java.nio.charset.StandardCharsets.UTF_8);

                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            java.nio.file.Path htmlPath = java.nio.file.Paths.get("edc-research-center/edc-research-api/src/main/resources/static/quartz-management/management.html");
            log.info("尝试从文件系统读取HTML文件: {}, 存在: {}", htmlPath.toAbsolutePath(), java.nio.file.Files.exists(htmlPath));

            if (java.nio.file.Files.exists(htmlPath)) {
                String content = new String(java.nio.file.Files.readAllBytes(htmlPath), java.nio.charset.StandardCharsets.UTF_8);

                // 获取服务器信息并注入到页面
                Map<String, Object> serverInfo = getServerInfo(request);
                if (serverInfo != null) {
                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    return content;
                }
            }

            // 如果文件不存在，返回错误页面
            return generateErrorPage("定时任务管理页面文件不存在");

        } catch (java.io.IOException e) {
            log.error("读取定时任务管理页面失败", e);
            return generateErrorPage("读取页面文件失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("定时任务管理页面处理失败", e);
            return generateErrorPage("页面处理失败: " + e.getMessage());
        }
    }

    /**
     * 验证配置秘钥
     */
    @ApiOperation("验证配置秘钥")
    @PostMapping("/auth/verify-secret")
    public CommonResult<Object> verifySecretKey(@RequestBody Map<String, String> request) {
        try {
            String secretKey = request.get("secretKey");

            if (secretKey == null || secretKey.trim().isEmpty()) {
                return CommonResult.failed("秘钥不能为空");
            }

            if (!quartzSecretKey.equals(secretKey.trim())) {
                log.warn("定时任务管理秘钥验证失败，输入的秘钥: {}", secretKey);
                return CommonResult.failed("秘钥验证失败");
            }

            log.info("定时任务管理秘钥验证成功");
            return CommonResult.success(null, "秘钥验证成功");

        } catch (Exception e) {
            log.error("验证配置秘钥失败", e);
            return CommonResult.failed("验证失败: " + e.getMessage());
        }
    }

    /**
     * 查询定时任务列表
     */
    @ApiOperation("查询定时任务列表")
    @GetMapping("/list")
    public CommonResult<List<SystemRequestRecordJob>> list(SystemRequestRecordJob job) {
        try {
            List<SystemRequestRecordJob> list = jobService.selectJobList(job);
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询定时任务列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取定时任务详细信息
     */
    @ApiOperation("获取定时任务详细信息")
    @GetMapping("/{jobId}")
    public CommonResult<SystemRequestRecordJob> getInfo(@PathVariable("jobId") Long jobId) {
        try {
            SystemRequestRecordJob job = jobService.selectJobById(jobId);
            if (job == null) {
                return CommonResult.failed("定时任务不存在");
            }
            return CommonResult.success(job);
        } catch (Exception e) {
            log.error("获取定时任务详细信息失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 新增定时任务
     */
    @ApiOperation("新增定时任务")
    @PostMapping
    public CommonResult<Object> add(@RequestBody SystemRequestRecordJob job) {
        try {
            if (!jobService.checkCronExpressionIsValid(job.getCronExpression())) {
                return CommonResult.failed("cron表达式无效");
            }
            
            if (!jobService.checkJobNameUnique(job)) {
                return CommonResult.failed("任务名称已存在");
            }
            
            int result = jobService.insertJob(job);
            if (result > 0) {
                return CommonResult.success(null, "新增成功");
            } else {
                return CommonResult.failed("新增失败");
            }
        } catch (SchedulerException e) {
            log.error("新增定时任务失败", e);
            return CommonResult.failed("新增失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("新增定时任务失败", e);
            return CommonResult.failed("新增失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改定时任务
     */
    @ApiOperation("修改定时任务")
    @PutMapping
    public CommonResult<Object> edit(@RequestBody SystemRequestRecordJob job) {
        try {
            if (!jobService.checkCronExpressionIsValid(job.getCronExpression())) {
                return CommonResult.failed("cron表达式无效");
            }
            
            if (!jobService.checkJobNameUnique(job)) {
                return CommonResult.failed("任务名称已存在");
            }
            
            int result = jobService.updateJob(job);
            if (result > 0) {
                return CommonResult.success(null, "修改成功");
            } else {
                return CommonResult.failed("修改失败");
            }
        } catch (SchedulerException e) {
            log.error("修改定时任务失败", e);
            return CommonResult.failed("修改失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("修改定时任务失败", e);
            return CommonResult.failed("修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除定时任务
     */
    @ApiOperation("删除定时任务")
    @DeleteMapping("/{jobIds}")
    public CommonResult<Object> remove(@PathVariable Long[] jobIds) {
        try {
            jobService.deleteJobByIds(jobIds);
            return CommonResult.success(null, "删除成功");
        } catch (SchedulerException e) {
            log.error("删除定时任务失败", e);
            return CommonResult.failed("删除失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("删除定时任务失败", e);
            return CommonResult.failed("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 定时任务状态修改
     */
    @ApiOperation("定时任务状态修改")
    @PutMapping("/changeStatus")
    public CommonResult<Object> changeStatus(@RequestBody SystemRequestRecordJob job) {
        try {
            int result = jobService.changeStatus(job);
            if (result > 0) {
                return CommonResult.success(null, "状态修改成功");
            } else {
                return CommonResult.failed("状态修改失败");
            }
        } catch (SchedulerException e) {
            log.error("定时任务状态修改失败", e);
            return CommonResult.failed("状态修改失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("定时任务状态修改失败", e);
            return CommonResult.failed("状态修改失败: " + e.getMessage());
        }
    }
    
    /**
     * 定时任务立即执行一次
     */
    @ApiOperation("定时任务立即执行一次")
    @PutMapping("/run")
    public CommonResult<Object> run(@RequestBody SystemRequestRecordJob job) {
        try {
            boolean result = jobService.run(job);
            if (result) {
                return CommonResult.success(null, "执行成功");
            } else {
                return CommonResult.failed("执行失败");
            }
        } catch (SchedulerException e) {
            log.error("定时任务立即执行失败", e);
            return CommonResult.failed("执行失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("定时任务立即执行失败", e);
            return CommonResult.failed("执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验cron表达式是否有效
     */
    @ApiOperation("校验cron表达式是否有效")
    @PostMapping("/checkCronExpression")
    public CommonResult<Object> checkCronExpression(@RequestParam String cronExpression) {
        try {
            boolean valid = jobService.checkCronExpressionIsValid(cronExpression);
            if (valid) {
                return CommonResult.success(null, "cron表达式有效");
            } else {
                return CommonResult.failed("cron表达式无效");
            }
        } catch (Exception e) {
            log.error("校验cron表达式失败", e);
            return CommonResult.failed("校验失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务下次执行时间
     */
    @ApiOperation("获取任务下次执行时间")
    @PostMapping("/getNextValidTime")
    public CommonResult<String> getNextValidTime(@RequestBody SystemRequestRecordJob job) {
        try {
            String nextValidTime = jobService.getNextValidTime(job);
            return CommonResult.success(nextValidTime);
        } catch (Exception e) {
            log.error("获取任务下次执行时间失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量启动任务
     */
    @ApiOperation("批量启动任务")
    @PutMapping("/batchStart")
    public CommonResult<Object> batchStart(@RequestParam Long[] jobIds) {
        try {
            int result = jobService.batchStartJobs(jobIds);
            return CommonResult.success(result, "批量启动成功");
        } catch (SchedulerException e) {
            log.error("批量启动任务失败", e);
            return CommonResult.failed("批量启动失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("批量启动任务失败", e);
            return CommonResult.failed("批量启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量暂停任务
     */
    @ApiOperation("批量暂停任务")
    @PutMapping("/batchPause")
    public CommonResult<Object> batchPause(@RequestParam Long[] jobIds) {
        try {
            int result = jobService.batchPauseJobs(jobIds);
            return CommonResult.success(result, "批量暂停成功");
        } catch (SchedulerException e) {
            log.error("批量暂停任务失败", e);
            return CommonResult.failed("批量暂停失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("批量暂停任务失败", e);
            return CommonResult.failed("批量暂停失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量恢复任务
     */
    @ApiOperation("批量恢复任务")
    @PutMapping("/batchResume")
    public CommonResult<Object> batchResume(@RequestParam Long[] jobIds) {
        try {
            int result = jobService.batchResumeJobs(jobIds);
            return CommonResult.success(result, "批量恢复成功");
        } catch (SchedulerException e) {
            log.error("批量恢复任务失败", e);
            return CommonResult.failed("批量恢复失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("批量恢复任务失败", e);
            return CommonResult.failed("批量恢复失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询定时任务统计信息
     */
    @ApiOperation("查询定时任务统计信息")
    @GetMapping("/statistics")
    public CommonResult<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = jobService.selectJobStatistics();
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("查询定时任务统计信息失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询定时任务执行趋势
     */
    @ApiOperation("查询定时任务执行趋势")
    @GetMapping("/trend")
    public CommonResult<List<Map<String, Object>>> getExecuteTrend(@RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> trend = jobService.selectJobExecuteTrend(days);
            return CommonResult.success(trend);
        } catch (Exception e) {
            log.error("查询定时任务执行趋势失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询定时任务性能排行
     */
    @ApiOperation("查询定时任务性能排行")
    @GetMapping("/performance")
    public CommonResult<List<Map<String, Object>>> getPerformanceRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Map<String, Object>> ranking = jobService.selectJobPerformanceRanking(limit);
            return CommonResult.success(ranking);
        } catch (Exception e) {
            log.error("查询定时任务性能排行失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 复制定时任务
     */
    @ApiOperation("复制定时任务")
    @PostMapping("/copy")
    public CommonResult<Object> copyJob(@RequestParam Long jobId, @RequestParam String newJobName) {
        try {
            int result = jobService.copyJob(jobId, newJobName);
            if (result > 0) {
                return CommonResult.success(null, "复制成功");
            } else {
                return CommonResult.failed("复制失败");
            }
        } catch (SchedulerException e) {
            log.error("复制定时任务失败", e);
            return CommonResult.failed("复制失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("复制定时任务失败", e);
            return CommonResult.failed("复制失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出定时任务配置
     */
    @ApiOperation("导出定时任务配置")
    @PostMapping("/export")
    public CommonResult<String> exportConfig(@RequestParam Long[] jobIds) {
        try {
            String configJson = jobService.exportJobConfig(jobIds);
            return CommonResult.success(configJson, "导出成功");
        } catch (Exception e) {
            log.error("导出定时任务配置失败", e);
            return CommonResult.failed("导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 导入定时任务配置
     */
    @ApiOperation("导入定时任务配置")
    @PostMapping("/import")
    public CommonResult<Object> importConfig(@RequestParam String configJson) {
        try {
            int result = jobService.importJobConfig(configJson);
            return CommonResult.success(result, "导入成功");
        } catch (SchedulerException e) {
            log.error("导入定时任务配置失败", e);
            return CommonResult.failed("导入失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("导入定时任务配置失败", e);
            return CommonResult.failed("导入失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取调度器状态
     */
    @ApiOperation("获取调度器状态")
    @GetMapping("/scheduler/status")
    public CommonResult<Map<String, Object>> getSchedulerStatus() {
        try {
            Map<String, Object> status = jobService.getSchedulerStatus();
            return CommonResult.success(status);
        } catch (Exception e) {
            log.error("获取调度器状态失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动调度器
     */
    @ApiOperation("启动调度器")
    @PostMapping("/scheduler/start")
    public CommonResult<Object> startScheduler() {
        try {
            boolean result = jobService.startScheduler();
            if (result) {
                return CommonResult.success(null, "启动成功");
            } else {
                return CommonResult.failed("启动失败");
            }
        } catch (SchedulerException e) {
            log.error("启动调度器失败", e);
            return CommonResult.failed("启动失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("启动调度器失败", e);
            return CommonResult.failed("启动失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭调度器
     */
    @ApiOperation("关闭调度器")
    @PostMapping("/scheduler/shutdown")
    public CommonResult<Object> shutdownScheduler() {
        try {
            boolean result = jobService.shutdownScheduler();
            if (result) {
                return CommonResult.success(null, "关闭成功");
            } else {
                return CommonResult.failed("关闭失败");
            }
        } catch (SchedulerException e) {
            log.error("关闭调度器失败", e);
            return CommonResult.failed("关闭失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("关闭调度器失败", e);
            return CommonResult.failed("关闭失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务器信息
     */
    private Map<String, Object> getServerInfo(HttpServletRequest request) {
        try {
            Map<String, Object> serverInfo = new java.util.HashMap<>();

            String scheme = request.getScheme();
            String serverName = request.getServerName();
            int serverPort = request.getServerPort();
            String contextPath = request.getContextPath();

            String requestUrl;
            if ((scheme.equals("http") && serverPort == 80) || (scheme.equals("https") && serverPort == 443)) {
                requestUrl = scheme + "://" + serverName + contextPath;
            } else {
                requestUrl = scheme + "://" + serverName + ":" + serverPort + contextPath;
            }

            serverInfo.put("requestUrl", requestUrl);

            return serverInfo;
        } catch (Exception e) {
            log.error("获取服务器信息失败", e);
            return null;
        }
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String errorMessage) {
        return String.format(
            "<!DOCTYPE html><html><head><title>错误</title></head><body>" +
            "<h1>页面加载失败</h1><p>%s</p>" +
            "<p><a href='javascript:history.back()'>返回上一页</a></p>" +
            "</body></html>",
            errorMessage
        );
    }
}
