package com.haoys.quartz.task;

import com.haoys.user.common.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统访问记录相关定时任务
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Component("systemRequestRecordTask")
public class SystemRequestRecordTask {
    
    /**
     * 清理过期的系统访问记录
     * 
     * @param retentionDays 保留天数，默认30天
     */
    public void cleanupExpiredRecords(String retentionDays) {
        log.info("开始执行系统访问记录清理任务，保留天数: {}", retentionDays);
        
        try {
            int days = 30; // 默认保留30天
            if (retentionDays != null && !retentionDays.trim().isEmpty()) {
                try {
                    days = Integer.parseInt(retentionDays.trim());
                } catch (NumberFormatException e) {
                    log.warn("保留天数参数格式错误，使用默认值30天: {}", retentionDays);
                }
            }
            
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用清理方法
            Object result = recordService.getClass().getMethod("deleteExpiredRecords", int.class).invoke(recordService, days);
            
            if (result instanceof Integer) {
                int deletedCount = (Integer) result;
                log.info("系统访问记录清理任务完成，删除记录数: {}, 保留天数: {}", deletedCount, days);
            } else {
                log.warn("系统访问记录清理任务返回结果异常: {}", result);
            }
            
        } catch (Exception e) {
            log.error("执行系统访问记录清理任务失败", e);
            throw new RuntimeException("清理过期记录失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 优化系统访问记录表
     */
    public void optimizeTable() {
        log.info("开始执行系统访问记录表优化任务");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用表优化方法
            recordService.getClass().getMethod("optimizeTable").invoke(recordService);
            
            log.info("系统访问记录表优化任务完成");
            
        } catch (Exception e) {
            log.error("执行系统访问记录表优化任务失败", e);
            throw new RuntimeException("表优化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 生成系统访问记录统计报告
     */
    public void generateStatisticsReport() {
        log.info("开始生成系统访问记录统计报告");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用生成报告方法
            Object reportData = recordService.getClass().getMethod("generateReport").invoke(recordService);
            
            if (reportData instanceof Map) {
                Map<?, ?> report = (Map<?, ?>) reportData;
                log.info("系统访问记录统计报告生成完成，数据项数: {}", report.size());
                
                // 这里可以添加报告发送逻辑，比如发送邮件或保存到文件
                saveReportToFile(report);
                
            } else {
                log.warn("生成统计报告返回结果异常: {}", reportData);
            }
            
        } catch (Exception e) {
            log.error("生成系统访问记录统计报告失败", e);
            throw new RuntimeException("生成统计报告失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 系统健康检查
     */
    public void healthCheck() {
        log.info("开始执行系统访问记录健康检查");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("SystemRequestRecordService 服务不可用");
                return;
            }
            
            // 执行健康检查
            Object healthStatus = recordService.getClass().getMethod("checkHealth").invoke(recordService);
            
            if (healthStatus instanceof Boolean) {
                boolean isHealthy = (Boolean) healthStatus;
                if (isHealthy) {
                    log.info("系统访问记录服务健康检查正常");
                } else {
                    log.warn("系统访问记录服务健康检查失败");
                    // 这里可以添加告警通知逻辑
                    sendHealthAlert("系统访问记录服务健康检查失败");
                }
            } else {
                log.warn("健康检查返回结果异常: {}", healthStatus);
            }
            
        } catch (Exception e) {
            log.error("执行系统访问记录健康检查失败", e);
            sendHealthAlert("系统访问记录健康检查异常: " + e.getMessage());
            throw new RuntimeException("健康检查失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 收集性能统计数据
     */
    public void collectPerformanceStats() {
        log.info("开始收集系统访问记录性能统计数据");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 收集统计数据
            Object statsData = recordService.getClass().getMethod("collectStatistics").invoke(recordService);
            
            if (statsData instanceof Map) {
                Map<?, ?> stats = (Map<?, ?>) statsData;
                log.info("系统访问记录性能统计数据收集完成，统计项数: {}", stats.size());
                
                // 保存统计数据
                saveStatsToCache(stats);
                
            } else {
                log.warn("收集性能统计数据返回结果异常: {}", statsData);
            }
            
        } catch (Exception e) {
            log.error("收集系统访问记录性能统计数据失败", e);
            throw new RuntimeException("收集性能统计失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 告警检查
     */
    public void alertCheck() {
        log.info("开始执行系统访问记录告警检查");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 检查错误率
            Object errorRateStats = recordService.getClass().getMethod("getErrorRateStats", int.class).invoke(recordService, 10);
            
            if (errorRateStats instanceof Map) {
                Map<?, ?> stats = (Map<?, ?>) errorRateStats;
                Object errorRate = stats.get("errorRate");
                
                if (errorRate instanceof Double) {
                    double rate = (Double) errorRate;
                    if (rate > 0.1) { // 错误率超过10%
                        String alertMessage = String.format("系统错误率过高: %.2f%%", rate * 100);
                        log.warn(alertMessage);
                        sendAlert("高错误率告警", alertMessage);
                    }
                }
            }
            
            // 检查慢请求
            Object slowRequestStats = recordService.getClass().getMethod("getSlowRequestStats", int.class).invoke(recordService, 60);
            
            if (slowRequestStats instanceof Map) {
                Map<?, ?> stats = (Map<?, ?>) slowRequestStats;
                Object slowCount = stats.get("slowCount");
                
                if (slowCount instanceof Long) {
                    long count = (Long) slowCount;
                    if (count > 100) { // 慢请求数量超过100
                        String alertMessage = String.format("慢请求数量过多: %d", count);
                        log.warn(alertMessage);
                        sendAlert("慢请求告警", alertMessage);
                    }
                }
            }
            
            log.info("系统访问记录告警检查完成");
            
        } catch (Exception e) {
            log.error("执行系统访问记录告警检查失败", e);
            throw new RuntimeException("告警检查失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 数据归档
     * 
     * @param archiveDays 归档天数，默认90天
     */
    public void archiveData(String archiveDays) {
        log.info("开始执行系统访问记录数据归档任务，归档天数: {}", archiveDays);
        
        try {
            int days = 90; // 默认归档90天前的数据
            if (archiveDays != null && !archiveDays.trim().isEmpty()) {
                try {
                    days = Integer.parseInt(archiveDays.trim());
                } catch (NumberFormatException e) {
                    log.warn("归档天数参数格式错误，使用默认值90天: {}", archiveDays);
                }
            }
            
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用归档方法
            Object result = recordService.getClass().getMethod("archiveRecords", int.class).invoke(recordService, days);
            
            if (result instanceof Integer) {
                int archivedCount = (Integer) result;
                log.info("系统访问记录数据归档任务完成，归档记录数: {}, 归档天数: {}", archivedCount, days);
            } else {
                log.warn("数据归档任务返回结果异常: {}", result);
            }
            
        } catch (Exception e) {
            log.error("执行系统访问记录数据归档任务失败", e);
            throw new RuntimeException("数据归档失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 索引优化
     */
    public void optimizeIndexes() {
        log.info("开始执行系统访问记录索引优化任务");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用索引优化方法
            recordService.getClass().getMethod("optimizeIndexes").invoke(recordService);
            
            log.info("系统访问记录索引优化任务完成");
            
        } catch (Exception e) {
            log.error("执行系统访问记录索引优化任务失败", e);
            throw new RuntimeException("索引优化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 缓存清理
     */
    public void cleanupCache() {
        log.info("开始执行系统访问记录缓存清理任务");
        
        try {
            // 获取系统访问记录服务
            Object recordService = SpringUtils.getBean("systemRequestRecordService");
            if (recordService == null) {
                log.error("无法获取 SystemRequestRecordService 实例");
                return;
            }
            
            // 调用缓存清理方法
            recordService.getClass().getMethod("cleanupCache").invoke(recordService);
            
            log.info("系统访问记录缓存清理任务完成");
            
        } catch (Exception e) {
            log.error("执行系统访问记录缓存清理任务失败", e);
            throw new RuntimeException("缓存清理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 保存报告到文件
     */
    private void saveReportToFile(Map<?, ?> report) {
        try {
            // 这里可以实现将报告保存到文件的逻辑
            log.debug("报告数据: {}", report);
        } catch (Exception e) {
            log.error("保存报告到文件失败", e);
        }
    }
    
    /**
     * 保存统计数据到缓存
     */
    private void saveStatsToCache(Map<?, ?> stats) {
        try {
            // 这里可以实现将统计数据保存到缓存的逻辑
            log.debug("统计数据: {}", stats);
        } catch (Exception e) {
            log.error("保存统计数据到缓存失败", e);
        }
    }
    
    /**
     * 发送健康检查告警
     */
    private void sendHealthAlert(String message) {
        try {
            // 这里可以实现发送告警的逻辑，比如发送邮件、短信或推送到监控系统
            log.warn("健康检查告警: {}", message);
        } catch (Exception e) {
            log.error("发送健康检查告警失败", e);
        }
    }
    
    /**
     * 发送告警
     */
    private void sendAlert(String alertType, String message) {
        try {
            // 这里可以实现发送告警的逻辑
            log.warn("系统告警 [{}]: {}", alertType, message);
        } catch (Exception e) {
            log.error("发送告警失败", e);
        }
    }
}
