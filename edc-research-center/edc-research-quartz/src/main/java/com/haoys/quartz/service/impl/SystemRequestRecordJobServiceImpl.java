package com.haoys.quartz.service.impl;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import com.haoys.quartz.mapper.SystemRequestRecordJobMapper;
import com.haoys.quartz.service.ISystemRequestRecordJobService;
import com.haoys.quartz.util.QuartzJobUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Service
public class SystemRequestRecordJobServiceImpl implements ISystemRequestRecordJobService {
    
    @Autowired
    private SystemRequestRecordJobMapper jobMapper;
    
    @Autowired
    private Scheduler scheduler;
    
    /**
     * 项目启动时，初始化定时器
     */
    @PostConstruct
    public void init() {
        try {
            initJobs();
        } catch (SchedulerException e) {
            log.error("初始化定时任务失败", e);
        }
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobList(SystemRequestRecordJob job) {
        return jobMapper.selectJobList(job);
    }
    
    @Override
    public SystemRequestRecordJob selectJobById(Long jobId) {
        return jobMapper.selectJobById(jobId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pauseJob(SystemRequestRecordJob job) throws SchedulerException {
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        job.setStatus("1");
        int rows = jobMapper.updateJob(job);
        if (rows > 0) {
            scheduler.pauseJob(QuartzJobUtil.getJobKey(jobId, jobGroup));
        }
        return rows;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resumeJob(SystemRequestRecordJob job) throws SchedulerException {
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        job.setStatus("0");
        int rows = jobMapper.updateJob(job);
        if (rows > 0) {
            scheduler.resumeJob(QuartzJobUtil.getJobKey(jobId, jobGroup));
        }
        return rows;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteJob(SystemRequestRecordJob job) throws SchedulerException {
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        int rows = jobMapper.deleteJobById(jobId);
        if (rows > 0) {
            scheduler.deleteJob(QuartzJobUtil.getJobKey(jobId, jobGroup));
        }
        return rows;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJobByIds(Long[] jobIds) throws SchedulerException {
        for (Long jobId : jobIds) {
            SystemRequestRecordJob job = selectJobById(jobId);
            if (job != null) {
                deleteJob(job);
            }
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(SystemRequestRecordJob job) throws SchedulerException {
        int rows = 0;
        String status = job.getStatus();
        if ("0".equals(status)) {
            rows = resumeJob(job);
        } else if ("1".equals(status)) {
            rows = pauseJob(job);
        }
        return rows;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean run(SystemRequestRecordJob job) throws SchedulerException {
        boolean result = false;
        Long jobId = job.getJobId();
        String jobGroup = job.getJobGroup();
        SystemRequestRecordJob properties = selectJobById(job.getJobId());
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put("JOB_PARAM_KEY", properties);
        JobKey jobKey = QuartzJobUtil.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            result = true;
            scheduler.triggerJob(jobKey, dataMap);
        }
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertJob(SystemRequestRecordJob job) throws SchedulerException {
        job.setStatus("1");
        int rows = jobMapper.insertJob(job);
        if (rows > 0) {
            QuartzJobUtil.createScheduleJob(scheduler, job);
        }
        return rows;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateJob(SystemRequestRecordJob job) throws SchedulerException {
        SystemRequestRecordJob properties = selectJobById(job.getJobId());
        int rows = jobMapper.updateJob(job);
        if (rows > 0) {
            updateSchedulerJob(job, properties.getJobGroup());
        }
        return rows;
    }
    
    /**
     * 更新任务
     *
     * @param job      任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(SystemRequestRecordJob job, String jobGroup) throws SchedulerException {
        Long jobId = job.getJobId();
        // 判断是否存在
        JobKey jobKey = QuartzJobUtil.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        QuartzJobUtil.createScheduleJob(scheduler, job);
    }
    
    @Override
    public boolean checkCronExpressionIsValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }
    
    @Override
    public void initJobs() throws SchedulerException {
        scheduler.clear();
        List<SystemRequestRecordJob> jobList = jobMapper.selectEnabledJobList();
        for (SystemRequestRecordJob job : jobList) {
            QuartzJobUtil.createScheduleJob(scheduler, job);
        }
    }
    
    @Override
    public String getNextValidTime(SystemRequestRecordJob job) {
        try {
            CronExpression cronExpression = new CronExpression(job.getCronExpression());
            java.util.Date nextValidTime = cronExpression.getNextValidTimeAfter(new java.util.Date());
            if (nextValidTime != null) {
                return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(nextValidTime);
            }
        } catch (Exception e) {
            log.error("获取下次执行时间失败", e);
        }
        return "";
    }
    
    @Override
    public String getJobStatus(SystemRequestRecordJob job) {
        try {
            JobKey jobKey = QuartzJobUtil.getJobKey(job.getJobId(), job.getJobGroup());
            if (scheduler.checkExists(jobKey)) {
                Trigger.TriggerState triggerState = scheduler.getTriggerState(QuartzJobUtil.getTriggerKey(job.getJobId(), job.getJobGroup()));
                return triggerState.name();
            }
        } catch (SchedulerException e) {
            log.error("获取任务状态失败", e);
        }
        return "NONE";
    }
    
    @Override
    public boolean checkJobNameUnique(SystemRequestRecordJob job) {
        Long jobId = job.getJobId() == null ? -1L : job.getJobId();
        SystemRequestRecordJob info = jobMapper.selectJobByNameAndGroup(job.getJobName(), job.getJobGroup());
        if (info != null && info.getJobId().longValue() != jobId.longValue()) {
            return false;
        }
        return true;
    }
    
    @Override
    public List<SystemRequestRecordJob> selectEnabledJobList() {
        return jobMapper.selectEnabledJobList();
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobListByType(Integer jobType) {
        return jobMapper.selectJobListByType(jobType);
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobListByCategory(String category) {
        return jobMapper.selectJobListByCategory(category);
    }
    
    @Override
    public List<SystemRequestRecordJob> selectUpcomingJobs(int minutes) {
        return jobMapper.selectUpcomingJobs(minutes);
    }
    
    @Override
    public List<SystemRequestRecordJob> selectLongTimeNoExecuteJobs(int hours) {
        return jobMapper.selectLongTimeNoExecuteJobs(hours);
    }
    
    @Override
    public List<SystemRequestRecordJob> selectFailedJobs() {
        return jobMapper.selectFailedJobs();
    }
    
    @Override
    public Map<String, Object> selectJobStatistics() {
        return jobMapper.selectJobStatistics();
    }
    
    @Override
    public List<Map<String, Object>> selectJobExecuteTrend(int days) {
        return jobMapper.selectJobExecuteTrend(days);
    }
    
    @Override
    public List<Map<String, Object>> selectJobPerformanceRanking(int limit) {
        return jobMapper.selectJobPerformanceRanking(limit);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchStartJobs(Long[] jobIds) throws SchedulerException {
        int count = 0;
        for (Long jobId : jobIds) {
            SystemRequestRecordJob job = selectJobById(jobId);
            if (job != null) {
                job.setStatus("0");
                count += resumeJob(job);
            }
        }
        return count;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchPauseJobs(Long[] jobIds) throws SchedulerException {
        int count = 0;
        for (Long jobId : jobIds) {
            SystemRequestRecordJob job = selectJobById(jobId);
            if (job != null) {
                job.setStatus("1");
                count += pauseJob(job);
            }
        }
        return count;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchResumeJobs(Long[] jobIds) throws SchedulerException {
        return batchStartJobs(jobIds);
    }
    
    @Override
    public int cleanExpiredJobs(int days) {
        return jobMapper.cleanExpiredJobs(days);
    }
    
    @Override
    public int resetJobStatistics(Long jobId) {
        return jobMapper.resetJobStatistics(jobId);
    }
    
    @Override
    public String exportJobConfig(Long[] jobIds) {
        // TODO: 实现配置导出逻辑
        return "{}";
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importJobConfig(String configJson) throws SchedulerException {
        // TODO: 实现配置导入逻辑
        return 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int copyJob(Long jobId, String newJobName) throws SchedulerException {
        SystemRequestRecordJob originalJob = selectJobById(jobId);
        if (originalJob == null) {
            return 0;
        }
        
        SystemRequestRecordJob newJob = new SystemRequestRecordJob();
        // 复制属性
        newJob.setJobName(newJobName);
        newJob.setJobGroup(originalJob.getJobGroup());
        newJob.setInvokeTarget(originalJob.getInvokeTarget());
        newJob.setCronExpression(originalJob.getCronExpression());
        newJob.setMisfirePolicy(originalJob.getMisfirePolicy());
        newJob.setConcurrent(originalJob.getConcurrent());
        newJob.setDescription(originalJob.getDescription() + " (复制)");
        newJob.setJobType(originalJob.getJobType());
        newJob.setJobParams(originalJob.getJobParams());
        newJob.setEnabled(false); // 复制的任务默认禁用
        newJob.setPriority(originalJob.getPriority());
        newJob.setTimeoutSeconds(originalJob.getTimeoutSeconds());
        newJob.setRetryCount(originalJob.getRetryCount());
        newJob.setRetryInterval(originalJob.getRetryInterval());
        newJob.setCategory(originalJob.getCategory());
        newJob.setTags(originalJob.getTags());
        newJob.setExecuteNode(originalJob.getExecuteNode());
        newJob.setSendNotification(originalJob.getSendNotification());
        newJob.setNotificationEmail(originalJob.getNotificationEmail());
        newJob.setNotificationPhone(originalJob.getNotificationPhone());
        newJob.setConfigJson(originalJob.getConfigJson());
        
        return insertJob(newJob);
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobDependencies(Long jobId) {
        // TODO: 实现依赖关系查询
        return null;
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobDependents(Long jobId) {
        // TODO: 实现依赖关系查询
        return null;
    }
    
    @Override
    public int setJobDependencies(Long jobId, String dependsOn) {
        // TODO: 实现依赖关系设置
        return 0;
    }
    
    @Override
    public boolean checkCircularDependency(Long jobId, String dependsOn) {
        // TODO: 实现循环依赖检查
        return false;
    }
    
    @Override
    public List<Map<String, Object>> selectJobNodeDistribution() {
        // TODO: 实现节点分布查询
        return null;
    }
    
    @Override
    public List<SystemRequestRecordJob> selectJobListByNode(String executeNode) {
        // TODO: 实现按节点查询
        return null;
    }
    
    @Override
    public int migrateJobsToNode(Long[] jobIds, String targetNode) {
        // TODO: 实现任务迁移
        return 0;
    }
    
    @Override
    public Map<String, Object> getSchedulerStatus() {
        try {
            Map<String, Object> status = new java.util.HashMap<>();
            status.put("started", scheduler.isStarted());
            status.put("shutdown", scheduler.isShutdown());
            status.put("standby", scheduler.isInStandbyMode());
            status.put("schedulerName", scheduler.getSchedulerName());
            status.put("schedulerInstanceId", scheduler.getSchedulerInstanceId());
            status.put("version", "2.3.2"); // Quartz版本
            
            SchedulerMetaData metaData = scheduler.getMetaData();
            status.put("runningSince", metaData.getRunningSince());
            status.put("numberOfJobsExecuted", metaData.getNumberOfJobsExecuted());
            status.put("schedulerClass", metaData.getSchedulerClass().getSimpleName());
            status.put("threadPoolSize", metaData.getThreadPoolSize());
            status.put("clustered", metaData.isJobStoreClustered());
            status.put("remote", metaData.isSchedulerRemote());
            status.put("jobStoreClass", metaData.getJobStoreClass().getSimpleName());
            status.put("persistent", metaData.isJobStoreSupportsPersistence());
            
            return status;
        } catch (SchedulerException e) {
            log.error("获取调度器状态失败", e);
            return new java.util.HashMap<>();
        }
    }
    
    @Override
    public boolean startScheduler() throws SchedulerException {
        if (!scheduler.isStarted()) {
            scheduler.start();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean shutdownScheduler() throws SchedulerException {
        if (!scheduler.isShutdown()) {
            scheduler.shutdown();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean pauseScheduler() throws SchedulerException {
        if (!scheduler.isInStandbyMode()) {
            scheduler.standby();
            return true;
        }
        return false;
    }
    
    @Override
    public boolean resumeScheduler() throws SchedulerException {
        if (scheduler.isInStandbyMode()) {
            scheduler.start();
            return true;
        }
        return false;
    }
}
