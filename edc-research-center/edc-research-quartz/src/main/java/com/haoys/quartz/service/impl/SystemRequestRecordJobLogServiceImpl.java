package com.haoys.quartz.service.impl;

import com.haoys.quartz.domain.SystemRequestRecordJobLog;
import com.haoys.quartz.mapper.SystemRequestRecordJobLogMapper;
import com.haoys.quartz.service.ISystemRequestRecordJobLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务执行日志服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Slf4j
@Service
public class SystemRequestRecordJobLogServiceImpl implements ISystemRequestRecordJobLogService {
    
    @Autowired
    private SystemRequestRecordJobLogMapper jobLogMapper;
    
    @Override
    public List<SystemRequestRecordJobLog> selectJobLogList(SystemRequestRecordJobLog jobLog) {
        return jobLogMapper.selectJobLogList(jobLog);
    }
    
    @Override
    public SystemRequestRecordJobLog selectJobLogById(Long logId) {
        return jobLogMapper.selectJobLogById(logId);
    }
    
    @Override
    public int insertJobLog(SystemRequestRecordJobLog jobLog) {
        return jobLogMapper.insertJobLog(jobLog);
    }
    
    @Override
    public int deleteJobLogByIds(Long[] logIds) {
        return jobLogMapper.deleteJobLogByIds(logIds);
    }
    
    @Override
    public int deleteJobLogById(Long logId) {
        return jobLogMapper.deleteJobLogById(logId);
    }
    
    @Override
    public int deleteJobLogByJobId(Long jobId) {
        return jobLogMapper.deleteJobLogByJobId(jobId);
    }
    
    @Override
    public int cleanJobLog() {
        return jobLogMapper.cleanJobLog();
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectJobLogListByJobId(Long jobId) {
        return jobLogMapper.selectJobLogListByJobId(jobId);
    }
    
    @Override
    public SystemRequestRecordJobLog selectLatestJobLogByJobId(Long jobId) {
        return jobLogMapper.selectLatestJobLogByJobId(jobId);
    }
    
    @Override
    public SystemRequestRecordJobLog selectLatestSuccessJobLogByJobId(Long jobId) {
        return jobLogMapper.selectLatestSuccessJobLogByJobId(jobId);
    }
    
    @Override
    public SystemRequestRecordJobLog selectLatestFailureJobLogByJobId(Long jobId) {
        return jobLogMapper.selectLatestFailureJobLogByJobId(jobId);
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectFailureJobLogList() {
        return jobLogMapper.selectFailureJobLogList();
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectSlowJobLogList(long threshold) {
        return jobLogMapper.selectSlowJobLogList(threshold);
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectJobLogListByTimeRange(Date startTime, Date endTime) {
        return jobLogMapper.selectJobLogListByTimeRange(startTime, endTime);
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectJobLogListByType(Integer jobType) {
        return jobLogMapper.selectJobLogListByType(jobType);
    }
    
    @Override
    public List<SystemRequestRecordJobLog> selectJobLogListByNode(String executeNode) {
        return jobLogMapper.selectJobLogListByNode(executeNode);
    }
    
    @Override
    public Map<String, Object> selectJobExecuteStatistics(Long jobId) {
        return jobLogMapper.selectJobExecuteStatistics(jobId);
    }
    
    @Override
    public Map<String, Object> selectAllJobExecuteStatistics() {
        return jobLogMapper.selectAllJobExecuteStatistics();
    }
    
    @Override
    public List<Map<String, Object>> selectJobExecuteTrend(Long jobId, int days) {
        return jobLogMapper.selectJobExecuteTrend(jobId, days);
    }
    
    @Override
    public List<Map<String, Object>> selectAllJobExecuteTrend(int days) {
        return jobLogMapper.selectAllJobExecuteTrend(days);
    }
    
    @Override
    public Map<String, Object> selectJobPerformanceStatistics(Long jobId) {
        return jobLogMapper.selectJobPerformanceStatistics(jobId);
    }
    
    @Override
    public List<Map<String, Object>> selectJobPerformanceRanking(int limit) {
        return jobLogMapper.selectJobPerformanceRanking(limit);
    }
    
    @Override
    public List<Map<String, Object>> selectJobErrorStatistics(int days) {
        return jobLogMapper.selectJobErrorStatistics(days);
    }
    
    @Override
    public List<Map<String, Object>> selectJobNodeStatistics() {
        return jobLogMapper.selectJobNodeStatistics();
    }
    
    @Override
    public List<Map<String, Object>> selectJobExecuteTimeDistribution(Long jobId) {
        return jobLogMapper.selectJobExecuteTimeDistribution(jobId);
    }
    
    @Override
    public List<Map<String, Object>> selectSystemResourceStatistics(int days) {
        return jobLogMapper.selectSystemResourceStatistics(days);
    }
    
    @Override
    public int cleanExpiredJobLog(int days) {
        return jobLogMapper.cleanExpiredJobLog(days);
    }
    
    @Override
    public int cleanExpiredJobLogByJobId(Long jobId, int days) {
        return jobLogMapper.cleanExpiredJobLogByJobId(jobId, days);
    }
    
    @Override
    public long selectJobLogCount() {
        return jobLogMapper.selectJobLogCount();
    }
    
    @Override
    public long selectJobLogCountByJobId(Long jobId) {
        return jobLogMapper.selectJobLogCountByJobId(jobId);
    }
    
    @Override
    public long selectJobLogCountByStatus(String status) {
        return jobLogMapper.selectJobLogCountByStatus(status);
    }
    
    @Override
    public long selectTodayJobLogCount() {
        return jobLogMapper.selectTodayJobLogCount();
    }
    
    @Override
    public long selectTodaySuccessJobLogCount() {
        return jobLogMapper.selectTodaySuccessJobLogCount();
    }
    
    @Override
    public long selectTodayFailureJobLogCount() {
        return jobLogMapper.selectTodayFailureJobLogCount();
    }
    
    @Override
    public Long selectAvgExecuteTime(Long jobId) {
        return jobLogMapper.selectAvgExecuteTime(jobId);
    }
    
    @Override
    public Long selectMaxExecuteTime(Long jobId) {
        return jobLogMapper.selectMaxExecuteTime(jobId);
    }
    
    @Override
    public Long selectMinExecuteTime(Long jobId) {
        return jobLogMapper.selectMinExecuteTime(jobId);
    }
    
    @Override
    public Double selectJobSuccessRate(Long jobId) {
        return jobLogMapper.selectJobSuccessRate(jobId);
    }
    
    @Override
    public List<String> selectRecentJobExecuteStatus(Long jobId, int limit) {
        return jobLogMapper.selectRecentJobExecuteStatus(jobId, limit);
    }
    
    @Override
    public int updateJobLogEndTime(Long logId, Date endTime, Long executeTime) {
        return jobLogMapper.updateJobLogEndTime(logId, endTime, executeTime);
    }
    
    @Override
    public int updateJobLogResult(Long logId, String status, String executeResult, String exceptionInfo) {
        return jobLogMapper.updateJobLogResult(logId, status, executeResult, exceptionInfo);
    }
    
    @Override
    public Long createJobStartLog(Long jobId, String jobName, String jobGroup, String invokeTarget, String jobParams) {
        SystemRequestRecordJobLog jobLog = new SystemRequestRecordJobLog();
        jobLog.setJobId(jobId);
        jobLog.setJobName(jobName);
        jobLog.setJobGroup(jobGroup);
        jobLog.setInvokeTarget(invokeTarget);
        jobLog.setJobParams(jobParams);
        jobLog.setStartTime(new Date());
        jobLog.setStatus("0"); // 默认成功，执行过程中如果失败会更新
        
        // 设置系统信息
        try {
            jobLog.setServerIp(java.net.InetAddress.getLocalHost().getHostAddress());
            jobLog.setServerName(java.net.InetAddress.getLocalHost().getHostName());
            jobLog.setProcessId(java.lang.management.ManagementFactory.getRuntimeMXBean().getName().split("@")[0]);
            jobLog.setThreadId(String.valueOf(Thread.currentThread().getId()));
            jobLog.setThreadName(Thread.currentThread().getName());
            
            // 获取内存使用情况
            java.lang.management.MemoryMXBean memoryBean = java.lang.management.ManagementFactory.getMemoryMXBean();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed() / 1024 / 1024; // MB
            jobLog.setMemoryUsage(usedMemory);
            
            // 获取CPU使用率（简单实现）
            com.sun.management.OperatingSystemMXBean osBean = 
                (com.sun.management.OperatingSystemMXBean) java.lang.management.ManagementFactory.getOperatingSystemMXBean();
            double cpuUsage = osBean.getProcessCpuLoad() * 100;
            jobLog.setCpuUsage(cpuUsage);
            
        } catch (Exception e) {
            log.warn("获取系统信息失败", e);
        }
        
        jobLog.setScheduledTime(new Date());
        jobLog.setActualTime(new Date());
        jobLog.setDelayTime(0L);
        
        insertJobLog(jobLog);
        return jobLog.getLogId();
    }
    
    @Override
    public int completeJobLog(Long logId, String status, String executeResult, String exceptionInfo,
                             Long processedRecords, Long successRecords, Long failureRecords) {
        SystemRequestRecordJobLog jobLog = selectJobLogById(logId);
        if (jobLog == null) {
            return 0;
        }
        
        Date endTime = new Date();
        Long executeTime = endTime.getTime() - jobLog.getStartTime().getTime();
        
        // 更新结束时间和执行时间
        updateJobLogEndTime(logId, endTime, executeTime);
        
        // 更新执行结果
        updateJobLogResult(logId, status, executeResult, exceptionInfo);
        
        // 更新处理记录数
        jobLog.setProcessedRecords(processedRecords);
        jobLog.setSuccessRecords(successRecords);
        jobLog.setFailureRecords(failureRecords);
        
        return 1;
    }
    
    @Override
    public String exportJobLog(Long[] logIds) {
        // TODO: 实现日志导出功能
        return "{}";
    }
    
    @Override
    public Map<String, Object> generateJobLogReport(Date startTime, Date endTime) {
        Map<String, Object> report = new java.util.HashMap<>();
        
        // 查询时间范围内的日志
        List<SystemRequestRecordJobLog> logs = selectJobLogListByTimeRange(startTime, endTime);
        
        // 统计信息
        long totalCount = logs.size();
        long successCount = logs.stream().mapToLong(log -> "0".equals(log.getStatus()) ? 1 : 0).sum();
        long failureCount = totalCount - successCount;
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
        
        // 平均执行时间
        double avgExecuteTime = logs.stream()
            .filter(log -> log.getExecuteTime() != null && log.getExecuteTime() > 0)
            .mapToLong(SystemRequestRecordJobLog::getExecuteTime)
            .average()
            .orElse(0);
        
        report.put("startTime", startTime);
        report.put("endTime", endTime);
        report.put("totalCount", totalCount);
        report.put("successCount", successCount);
        report.put("failureCount", failureCount);
        report.put("successRate", Math.round(successRate * 100.0) / 100.0);
        report.put("avgExecuteTime", Math.round(avgExecuteTime));
        
        return report;
    }
    
    @Override
    public int getJobHealthScore(Long jobId) {
        try {
            // 获取任务统计信息
            Map<String, Object> stats = selectJobExecuteStatistics(jobId);
            if (stats == null || stats.isEmpty()) {
                return 100; // 没有执行记录，默认健康
            }
            
            // 成功率权重 60%
            Double successRate = (Double) stats.get("successRate");
            int successScore = successRate != null ? (int) (successRate * 0.6) : 0;
            
            // 执行时间稳定性权重 30%
            Long avgTime = (Long) stats.get("avgExecuteTime");
            Long maxTime = (Long) stats.get("maxExecuteTime");
            int timeScore = 30;
            if (avgTime != null && maxTime != null && avgTime > 0) {
                double timeVariation = (double) maxTime / avgTime;
                if (timeVariation > 3) {
                    timeScore = 10; // 时间波动大
                } else if (timeVariation > 2) {
                    timeScore = 20;
                }
            }
            
            // 最近执行状态权重 10%
            List<String> recentStatus = selectRecentJobExecuteStatus(jobId, 5);
            int recentScore = 10;
            if (recentStatus != null && !recentStatus.isEmpty()) {
                long recentFailures = recentStatus.stream().mapToLong(s -> "1".equals(s) ? 1 : 0).sum();
                if (recentFailures >= 3) {
                    recentScore = 0; // 最近连续失败
                } else if (recentFailures >= 1) {
                    recentScore = 5;
                }
            }
            
            return Math.min(100, successScore + timeScore + recentScore);
        } catch (Exception e) {
            log.error("计算任务健康度失败", e);
            return 50; // 异常情况返回中等健康度
        }
    }
    
    @Override
    public int getSystemHealthScore() {
        try {
            Map<String, Object> stats = selectAllJobExecuteStatistics();
            if (stats == null || stats.isEmpty()) {
                return 100;
            }
            
            // 整体成功率
            Double successRate = (Double) stats.get("successRate");
            int score = successRate != null ? successRate.intValue() : 100;
            
            // 今日执行情况
            long todayTotal = selectTodayJobLogCount();
            long todaySuccess = selectTodaySuccessJobLogCount();
            if (todayTotal > 0) {
                double todaySuccessRate = (double) todaySuccess / todayTotal * 100;
                score = (int) ((score + todaySuccessRate) / 2);
            }
            
            return Math.min(100, Math.max(0, score));
        } catch (Exception e) {
            log.error("计算系统健康度失败", e);
            return 50;
        }
    }
    
    @Override
    public Long predictJobExecuteTime(Long jobId) {
        try {
            // 获取最近10次执行的平均时间
            List<SystemRequestRecordJobLog> recentLogs = selectJobLogListByJobId(jobId);
            if (recentLogs == null || recentLogs.isEmpty()) {
                return 0L;
            }
            
            // 取最近10次记录
            List<SystemRequestRecordJobLog> recent10 = recentLogs.stream()
                .filter(log -> log.getExecuteTime() != null && log.getExecuteTime() > 0)
                .limit(10)
                .collect(java.util.stream.Collectors.toList());
            
            if (recent10.isEmpty()) {
                return 0L;
            }
            
            // 计算加权平均（最近的记录权重更高）
            double weightedSum = 0;
            double totalWeight = 0;
            for (int i = 0; i < recent10.size(); i++) {
                double weight = recent10.size() - i; // 越新的记录权重越高
                weightedSum += recent10.get(i).getExecuteTime() * weight;
                totalWeight += weight;
            }
            
            return Math.round(weightedSum / totalWeight);
        } catch (Exception e) {
            log.error("预测任务执行时间失败", e);
            return 0L;
        }
    }
    
    @Override
    public Map<String, Object> analyzeJobExceptionPattern(Long jobId, int days) {
        Map<String, Object> analysis = new java.util.HashMap<>();
        
        try {
            // 获取指定天数内的失败日志
            Date startTime = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
            Date endTime = new Date();
            
            List<SystemRequestRecordJobLog> failureLogs = selectJobLogListByTimeRange(startTime, endTime)
                .stream()
                .filter(log -> log.getJobId().equals(jobId) && "1".equals(log.getStatus()))
                .collect(java.util.stream.Collectors.toList());
            
            analysis.put("totalFailures", failureLogs.size());
            
            if (failureLogs.isEmpty()) {
                analysis.put("pattern", "无异常");
                return analysis;
            }
            
            // 分析异常类型
            Map<String, Long> exceptionTypes = failureLogs.stream()
                .filter(log -> log.getExceptionInfo() != null)
                .collect(java.util.stream.Collectors.groupingBy(
                    log -> {
                        String exception = log.getExceptionInfo();
                        if (exception.contains("TimeoutException")) return "超时异常";
                        if (exception.contains("SQLException")) return "数据库异常";
                        if (exception.contains("ConnectException")) return "连接异常";
                        if (exception.contains("OutOfMemoryError")) return "内存异常";
                        return "其他异常";
                    },
                    java.util.stream.Collectors.counting()
                ));
            
            analysis.put("exceptionTypes", exceptionTypes);
            
            // 分析时间模式
            Map<Integer, Long> hourPattern = failureLogs.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    log -> {
                        java.util.Calendar cal = java.util.Calendar.getInstance();
                        cal.setTime(log.getStartTime());
                        return cal.get(java.util.Calendar.HOUR_OF_DAY);
                    },
                    java.util.stream.Collectors.counting()
                ));
            
            analysis.put("hourPattern", hourPattern);
            
            // 找出最常见的异常
            String mostCommonException = exceptionTypes.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("未知");
            
            analysis.put("mostCommonException", mostCommonException);
            
            // 建议
            String suggestion = generateSuggestion(mostCommonException, hourPattern);
            analysis.put("suggestion", suggestion);
            
        } catch (Exception e) {
            log.error("分析任务异常模式失败", e);
            analysis.put("error", e.getMessage());
        }
        
        return analysis;
    }
    
    private String generateSuggestion(String mostCommonException, Map<Integer, Long> hourPattern) {
        StringBuilder suggestion = new StringBuilder();
        
        switch (mostCommonException) {
            case "超时异常":
                suggestion.append("建议增加任务超时时间或优化任务执行逻辑。");
                break;
            case "数据库异常":
                suggestion.append("建议检查数据库连接配置和SQL语句优化。");
                break;
            case "连接异常":
                suggestion.append("建议检查网络连接和服务可用性。");
                break;
            case "内存异常":
                suggestion.append("建议增加JVM内存配置或优化内存使用。");
                break;
            default:
                suggestion.append("建议查看详细异常日志进行排查。");
        }
        
        // 分析时间模式
        if (!hourPattern.isEmpty()) {
            Integer peakHour = hourPattern.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
            
            if (peakHour != null) {
                suggestion.append(String.format(" 异常多发时间为%d点，建议避开高峰期执行。", peakHour));
            }
        }
        
        return suggestion.toString();
    }
}
