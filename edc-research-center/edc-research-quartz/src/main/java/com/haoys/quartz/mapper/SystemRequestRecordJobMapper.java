package com.haoys.quartz.mapper;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务 Mapper 接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Mapper
public interface SystemRequestRecordJobMapper {
    
    /**
     * 查询定时任务列表
     * 
     * @param job 定时任务信息
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobList(SystemRequestRecordJob job);
    
    /**
     * 查询所有定时任务
     * 
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobAll();
    
    /**
     * 通过定时任务ID查询定时任务信息
     * 
     * @param jobId 定时任务ID
     * @return 定时任务信息
     */
    SystemRequestRecordJob selectJobById(Long jobId);
    
    /**
     * 通过定时任务名称和组名查询定时任务信息
     * 
     * @param jobName 定时任务名称
     * @param jobGroup 定时任务组名
     * @return 定时任务信息
     */
    SystemRequestRecordJob selectJobByNameAndGroup(@Param("jobName") String jobName, @Param("jobGroup") String jobGroup);
    
    /**
     * 新增定时任务信息
     * 
     * @param job 定时任务信息
     * @return 结果
     */
    int insertJob(SystemRequestRecordJob job);
    
    /**
     * 修改定时任务信息
     * 
     * @param job 定时任务信息
     * @return 结果
     */
    int updateJob(SystemRequestRecordJob job);
    
    /**
     * 批量删除定时任务信息
     * 
     * @param jobIds 需要删除的数据ID
     * @return 结果
     */
    int deleteJobByIds(@Param("jobIds") Long[] jobIds);
    
    /**
     * 删除定时任务信息
     * 
     * @param jobId 定时任务ID
     * @return 结果
     */
    int deleteJobById(Long jobId);
    
    /**
     * 更新定时任务状态
     * 
     * @param jobId 任务ID
     * @param status 状态
     * @return 结果
     */
    int updateJobStatus(@Param("jobId") Long jobId, @Param("status") String status);
    
    /**
     * 更新定时任务执行状态
     * 
     * @param jobId 任务ID
     * @param executeStatus 执行状态
     * @param lastExecuteResult 最后执行结果
     * @return 结果
     */
    int updateJobExecuteStatus(@Param("jobId") Long jobId, 
                              @Param("executeStatus") String executeStatus, 
                              @Param("lastExecuteResult") String lastExecuteResult);
    
    /**
     * 更新定时任务执行统计信息
     * 
     * @param jobId 任务ID
     * @param executeCount 执行次数
     * @param failureCount 失败次数
     * @param avgExecuteTime 平均执行时间
     * @param maxExecuteTime 最大执行时间
     * @param minExecuteTime 最小执行时间
     * @return 结果
     */
    int updateJobStatistics(@Param("jobId") Long jobId,
                           @Param("executeCount") Long executeCount,
                           @Param("failureCount") Long failureCount,
                           @Param("avgExecuteTime") Long avgExecuteTime,
                           @Param("maxExecuteTime") Long maxExecuteTime,
                           @Param("minExecuteTime") Long minExecuteTime);
    
    /**
     * 更新定时任务下次执行时间
     * 
     * @param jobId 任务ID
     * @param nextValidTime 下次执行时间
     * @return 结果
     */
    int updateJobNextValidTime(@Param("jobId") Long jobId, @Param("nextValidTime") java.util.Date nextValidTime);
    
    /**
     * 更新定时任务上次执行时间
     * 
     * @param jobId 任务ID
     * @param previousFireTime 上次执行时间
     * @return 结果
     */
    int updateJobPreviousFireTime(@Param("jobId") Long jobId, @Param("previousFireTime") java.util.Date previousFireTime);
    
    /**
     * 查询启用的定时任务列表
     * 
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectEnabledJobList();
    
    /**
     * 查询指定类型的定时任务列表
     * 
     * @param jobType 任务类型
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByType(@Param("jobType") Integer jobType);
    
    /**
     * 查询指定分类的定时任务列表
     * 
     * @param category 任务分类
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByCategory(@Param("category") String category);
    
    /**
     * 查询即将执行的定时任务列表
     * 
     * @param minutes 未来几分钟内
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectUpcomingJobs(@Param("minutes") int minutes);
    
    /**
     * 查询长时间未执行的定时任务列表
     * 
     * @param hours 小时数
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectLongTimeNoExecuteJobs(@Param("hours") int hours);
    
    /**
     * 查询执行失败的定时任务列表
     * 
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectFailedJobs();
    
    /**
     * 查询定时任务统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectJobStatistics();
    
    /**
     * 查询定时任务执行趋势
     * 
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectJobExecuteTrend(@Param("days") int days);
    
    /**
     * 查询定时任务性能排行
     * 
     * @param limit 限制数量
     * @return 性能排行
     */
    List<Map<String, Object>> selectJobPerformanceRanking(@Param("limit") int limit);
    
    /**
     * 检查定时任务名称是否唯一
     * 
     * @param jobName 任务名称
     * @param jobGroup 任务组名
     * @param jobId 任务ID（排除自己）
     * @return 数量
     */
    int checkJobNameUnique(@Param("jobName") String jobName, 
                          @Param("jobGroup") String jobGroup, 
                          @Param("jobId") Long jobId);
    
    /**
     * 批量更新定时任务状态
     * 
     * @param jobIds 任务ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateJobStatus(@Param("jobIds") Long[] jobIds, @Param("status") String status);
    
    /**
     * 清理过期的定时任务
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredJobs(@Param("days") int days);
    
    /**
     * 重置定时任务统计信息
     * 
     * @param jobId 任务ID
     * @return 结果
     */
    int resetJobStatistics(@Param("jobId") Long jobId);
    
    /**
     * 查询定时任务配置快照
     * 
     * @param jobId 任务ID
     * @return 配置快照
     */
    String selectJobConfigSnapshot(@Param("jobId") Long jobId);
    
    /**
     * 更新定时任务配置快照
     * 
     * @param jobId 任务ID
     * @param configSnapshot 配置快照
     * @return 结果
     */
    int updateJobConfigSnapshot(@Param("jobId") Long jobId, @Param("configSnapshot") String configSnapshot);
    
    /**
     * 查询定时任务依赖关系
     * 
     * @param jobId 任务ID
     * @return 依赖任务列表
     */
    List<SystemRequestRecordJob> selectJobDependencies(@Param("jobId") Long jobId);
    
    /**
     * 查询依赖指定任务的任务列表
     * 
     * @param jobId 任务ID
     * @return 依赖任务列表
     */
    List<SystemRequestRecordJob> selectJobDependents(@Param("jobId") Long jobId);
    
    /**
     * 查询定时任务执行节点分布
     * 
     * @return 节点分布
     */
    List<Map<String, Object>> selectJobNodeDistribution();
    
    /**
     * 查询指定节点的定时任务列表
     * 
     * @param executeNode 执行节点
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByNode(@Param("executeNode") String executeNode);
}
