package com.haoys.quartz.service;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import org.quartz.SchedulerException;

import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public interface ISystemRequestRecordJobService {
    
    /**
     * 查询定时任务列表
     * 
     * @param job 定时任务信息
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobList(SystemRequestRecordJob job);
    
    /**
     * 通过定时任务ID查询定时任务信息
     * 
     * @param jobId 定时任务ID
     * @return 定时任务信息
     */
    SystemRequestRecordJob selectJobById(Long jobId);
    
    /**
     * 暂停任务
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int pauseJob(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 恢复任务
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int resumeJob(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 删除任务后，所对应的trigger也将被删除
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int deleteJob(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 批量删除调度信息
     * 
     * @param jobIds 需要删除的任务ID
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    void deleteJobByIds(Long[] jobIds) throws SchedulerException;
    
    /**
     * 任务调度状态修改
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int changeStatus(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 立即运行任务
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    boolean run(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 新增任务
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int insertJob(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 更新任务
     * 
     * @param job 定时任务信息
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int updateJob(SystemRequestRecordJob job) throws SchedulerException;
    
    /**
     * 校验cron表达式是否有效
     * 
     * @param cronExpression 表达式
     * @return 结果
     */
    boolean checkCronExpressionIsValid(String cronExpression);
    
    /**
     * 初始化定时任务
     * 
     * @throws SchedulerException 调度异常
     */
    void initJobs() throws SchedulerException;
    
    /**
     * 获取任务下次执行时间
     * 
     * @param job 定时任务信息
     * @return 下次执行时间
     */
    String getNextValidTime(SystemRequestRecordJob job);
    
    /**
     * 获取任务执行状态
     * 
     * @param job 定时任务信息
     * @return 执行状态
     */
    String getJobStatus(SystemRequestRecordJob job);
    
    /**
     * 检查任务名称是否唯一
     * 
     * @param job 定时任务信息
     * @return 结果
     */
    boolean checkJobNameUnique(SystemRequestRecordJob job);
    
    /**
     * 查询所有启用的定时任务
     * 
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectEnabledJobList();
    
    /**
     * 查询指定类型的定时任务列表
     * 
     * @param jobType 任务类型
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByType(Integer jobType);
    
    /**
     * 查询指定分类的定时任务列表
     * 
     * @param category 任务分类
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByCategory(String category);
    
    /**
     * 查询即将执行的定时任务列表
     * 
     * @param minutes 未来几分钟内
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectUpcomingJobs(int minutes);
    
    /**
     * 查询长时间未执行的定时任务列表
     * 
     * @param hours 小时数
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectLongTimeNoExecuteJobs(int hours);
    
    /**
     * 查询执行失败的定时任务列表
     * 
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectFailedJobs();
    
    /**
     * 查询定时任务统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectJobStatistics();
    
    /**
     * 查询定时任务执行趋势
     * 
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> selectJobExecuteTrend(int days);
    
    /**
     * 查询定时任务性能排行
     * 
     * @param limit 限制数量
     * @return 性能排行
     */
    List<Map<String, Object>> selectJobPerformanceRanking(int limit);
    
    /**
     * 批量启动任务
     * 
     * @param jobIds 任务ID数组
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int batchStartJobs(Long[] jobIds) throws SchedulerException;
    
    /**
     * 批量暂停任务
     * 
     * @param jobIds 任务ID数组
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int batchPauseJobs(Long[] jobIds) throws SchedulerException;
    
    /**
     * 批量恢复任务
     * 
     * @param jobIds 任务ID数组
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int batchResumeJobs(Long[] jobIds) throws SchedulerException;
    
    /**
     * 清理过期的定时任务
     * 
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredJobs(int days);
    
    /**
     * 重置定时任务统计信息
     * 
     * @param jobId 任务ID
     * @return 结果
     */
    int resetJobStatistics(Long jobId);
    
    /**
     * 导出定时任务配置
     * 
     * @param jobIds 任务ID数组
     * @return 配置JSON
     */
    String exportJobConfig(Long[] jobIds);
    
    /**
     * 导入定时任务配置
     * 
     * @param configJson 配置JSON
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int importJobConfig(String configJson) throws SchedulerException;
    
    /**
     * 复制定时任务
     * 
     * @param jobId 源任务ID
     * @param newJobName 新任务名称
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    int copyJob(Long jobId, String newJobName) throws SchedulerException;
    
    /**
     * 查询定时任务依赖关系
     * 
     * @param jobId 任务ID
     * @return 依赖任务列表
     */
    List<SystemRequestRecordJob> selectJobDependencies(Long jobId);
    
    /**
     * 查询依赖指定任务的任务列表
     * 
     * @param jobId 任务ID
     * @return 依赖任务列表
     */
    List<SystemRequestRecordJob> selectJobDependents(Long jobId);
    
    /**
     * 设置任务依赖关系
     * 
     * @param jobId 任务ID
     * @param dependsOn 依赖任务ID列表
     * @return 结果
     */
    int setJobDependencies(Long jobId, String dependsOn);
    
    /**
     * 检查任务依赖关系是否存在循环依赖
     * 
     * @param jobId 任务ID
     * @param dependsOn 依赖任务ID列表
     * @return 是否存在循环依赖
     */
    boolean checkCircularDependency(Long jobId, String dependsOn);
    
    /**
     * 查询定时任务执行节点分布
     * 
     * @return 节点分布
     */
    List<Map<String, Object>> selectJobNodeDistribution();
    
    /**
     * 查询指定节点的定时任务列表
     * 
     * @param executeNode 执行节点
     * @return 定时任务集合
     */
    List<SystemRequestRecordJob> selectJobListByNode(String executeNode);
    
    /**
     * 迁移任务到指定节点
     * 
     * @param jobIds 任务ID数组
     * @param targetNode 目标节点
     * @return 结果
     */
    int migrateJobsToNode(Long[] jobIds, String targetNode);
    
    /**
     * 获取调度器状态
     * 
     * @return 调度器状态
     */
    Map<String, Object> getSchedulerStatus();
    
    /**
     * 启动调度器
     * 
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    boolean startScheduler() throws SchedulerException;
    
    /**
     * 关闭调度器
     * 
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    boolean shutdownScheduler() throws SchedulerException;
    
    /**
     * 暂停调度器
     * 
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    boolean pauseScheduler() throws SchedulerException;
    
    /**
     * 恢复调度器
     * 
     * @return 结果
     * @throws SchedulerException 调度异常
     */
    boolean resumeScheduler() throws SchedulerException;
}
