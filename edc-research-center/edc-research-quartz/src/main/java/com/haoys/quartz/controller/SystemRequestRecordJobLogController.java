package com.haoys.quartz.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.quartz.domain.SystemRequestRecordJobLog;
import com.haoys.quartz.service.ISystemRequestRecordJobLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 系统访问记录定时任务执行日志控制器
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Api(tags = "系统访问记录定时任务执行日志管理")
@RestController
@RequestMapping("/quartz/jobLog")
@Slf4j
public class SystemRequestRecordJobLogController {
    
    @Autowired
    private ISystemRequestRecordJobLogService jobLogService;
    
    /**
     * 查询定时任务执行日志列表
     */
    @ApiOperation("查询定时任务执行日志列表")
    @GetMapping("/list")
    public CommonResult<List<SystemRequestRecordJobLog>> list(SystemRequestRecordJobLog jobLog) {
        try {
            List<SystemRequestRecordJobLog> list = jobLogService.selectJobLogList(jobLog);
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询定时任务执行日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取定时任务执行日志详细信息
     */
    @ApiOperation("获取定时任务执行日志详细信息")
    @GetMapping("/{logId}")
    public CommonResult<SystemRequestRecordJobLog> getInfo(@PathVariable("logId") Long logId) {
        try {
            SystemRequestRecordJobLog jobLog = jobLogService.selectJobLogById(logId);
            if (jobLog == null) {
                return CommonResult.failed("执行日志不存在");
            }
            return CommonResult.success(jobLog);
        } catch (Exception e) {
            log.error("获取定时任务执行日志详细信息失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除定时任务执行日志
     */
    @ApiOperation("删除定时任务执行日志")
    @DeleteMapping("/{logIds}")
    public CommonResult<Object> remove(@PathVariable Long[] logIds) {
        try {
            int result = jobLogService.deleteJobLogByIds(logIds);
            if (result > 0) {
                return CommonResult.success(null, "删除成功");
            } else {
                return CommonResult.failed("删除失败");
            }
        } catch (Exception e) {
            log.error("删除定时任务执行日志失败", e);
            return CommonResult.failed("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 清空定时任务执行日志
     */
    @ApiOperation("清空定时任务执行日志")
    @DeleteMapping("/clean")
    public CommonResult<Object> clean() {
        try {
            int result = jobLogService.cleanJobLog();
            return CommonResult.success(result, "清空成功");
        } catch (Exception e) {
            log.error("清空定时任务执行日志失败", e);
            return CommonResult.failed("清空失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询指定任务的执行日志列表
     */
    @ApiOperation("查询指定任务的执行日志列表")
    @GetMapping("/job/{jobId}")
    public CommonResult<List<SystemRequestRecordJobLog>> getJobLogsByJobId(@PathVariable("jobId") Long jobId) {
        try {
            List<SystemRequestRecordJobLog> list = jobLogService.selectJobLogListByJobId(jobId);
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询指定任务的执行日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询指定任务的最新执行日志
     */
    @ApiOperation("查询指定任务的最新执行日志")
    @GetMapping("/job/{jobId}/latest")
    public CommonResult<SystemRequestRecordJobLog> getLatestJobLog(@PathVariable("jobId") Long jobId) {
        try {
            SystemRequestRecordJobLog jobLog = jobLogService.selectLatestJobLogByJobId(jobId);
            return CommonResult.success(jobLog);
        } catch (Exception e) {
            log.error("查询指定任务的最新执行日志失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询执行失败的日志列表
     */
    @ApiOperation("查询执行失败的日志列表")
    @GetMapping("/failures")
    public CommonResult<List<SystemRequestRecordJobLog>> getFailureJobLogs() {
        try {
            List<SystemRequestRecordJobLog> list = jobLogService.selectFailureJobLogList();
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询执行失败的日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询执行时间超过阈值的日志列表
     */
    @ApiOperation("查询执行时间超过阈值的日志列表")
    @GetMapping("/slow")
    public CommonResult<List<SystemRequestRecordJobLog>> getSlowJobLogs(@RequestParam(defaultValue = "5000") long threshold) {
        try {
            List<SystemRequestRecordJobLog> list = jobLogService.selectSlowJobLogList(threshold);
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询慢执行日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询指定时间范围内的执行日志列表
     */
    @ApiOperation("查询指定时间范围内的执行日志列表")
    @GetMapping("/timeRange")
    public CommonResult<List<SystemRequestRecordJobLog>> getJobLogsByTimeRange(
            @RequestParam Date startTime, 
            @RequestParam Date endTime) {
        try {
            List<SystemRequestRecordJobLog> list = jobLogService.selectJobLogListByTimeRange(startTime, endTime);
            return CommonResult.success(list);
        } catch (Exception e) {
            log.error("查询指定时间范围内的执行日志列表失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 统计任务执行情况
     */
    @ApiOperation("统计任务执行情况")
    @GetMapping("/statistics/job/{jobId}")
    public CommonResult<Map<String, Object>> getJobExecuteStatistics(@PathVariable("jobId") Long jobId) {
        try {
            Map<String, Object> statistics = jobLogService.selectJobExecuteStatistics(jobId);
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("统计任务执行情况失败", e);
            return CommonResult.failed("统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 统计所有任务执行情况
     */
    @ApiOperation("统计所有任务执行情况")
    @GetMapping("/statistics/all")
    public CommonResult<Map<String, Object>> getAllJobExecuteStatistics() {
        try {
            Map<String, Object> statistics = jobLogService.selectAllJobExecuteStatistics();
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("统计所有任务执行情况失败", e);
            return CommonResult.failed("统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务执行趋势
     */
    @ApiOperation("查询任务执行趋势")
    @GetMapping("/trend/job/{jobId}")
    public CommonResult<List<Map<String, Object>>> getJobExecuteTrend(
            @PathVariable("jobId") Long jobId, 
            @RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> trend = jobLogService.selectJobExecuteTrend(jobId, days);
            return CommonResult.success(trend);
        } catch (Exception e) {
            log.error("查询任务执行趋势失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询所有任务执行趋势
     */
    @ApiOperation("查询所有任务执行趋势")
    @GetMapping("/trend/all")
    public CommonResult<List<Map<String, Object>>> getAllJobExecuteTrend(@RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> trend = jobLogService.selectAllJobExecuteTrend(days);
            return CommonResult.success(trend);
        } catch (Exception e) {
            log.error("查询所有任务执行趋势失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务执行性能统计
     */
    @ApiOperation("查询任务执行性能统计")
    @GetMapping("/performance/job/{jobId}")
    public CommonResult<Map<String, Object>> getJobPerformanceStatistics(@PathVariable("jobId") Long jobId) {
        try {
            Map<String, Object> performance = jobLogService.selectJobPerformanceStatistics(jobId);
            return CommonResult.success(performance);
        } catch (Exception e) {
            log.error("查询任务执行性能统计失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务执行性能排行
     */
    @ApiOperation("查询任务执行性能排行")
    @GetMapping("/performance/ranking")
    public CommonResult<List<Map<String, Object>>> getJobPerformanceRanking(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Map<String, Object>> ranking = jobLogService.selectJobPerformanceRanking(limit);
            return CommonResult.success(ranking);
        } catch (Exception e) {
            log.error("查询任务执行性能排行失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务执行错误统计
     */
    @ApiOperation("查询任务执行错误统计")
    @GetMapping("/errors")
    public CommonResult<List<Map<String, Object>>> getJobErrorStatistics(@RequestParam(defaultValue = "7") int days) {
        try {
            List<Map<String, Object>> errors = jobLogService.selectJobErrorStatistics(days);
            return CommonResult.success(errors);
        } catch (Exception e) {
            log.error("查询任务执行错误统计失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询任务执行节点统计
     */
    @ApiOperation("查询任务执行节点统计")
    @GetMapping("/nodes")
    public CommonResult<List<Map<String, Object>>> getJobNodeStatistics() {
        try {
            List<Map<String, Object>> nodes = jobLogService.selectJobNodeStatistics();
            return CommonResult.success(nodes);
        } catch (Exception e) {
            log.error("查询任务执行节点统计失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期的执行日志
     */
    @ApiOperation("清理过期的执行日志")
    @DeleteMapping("/cleanup")
    public CommonResult<Object> cleanupExpiredJobLog(@RequestParam(defaultValue = "30") int days) {
        try {
            int result = jobLogService.cleanExpiredJobLog(days);
            return CommonResult.success(result, "清理成功，删除记录数: " + result);
        } catch (Exception e) {
            log.error("清理过期的执行日志失败", e);
            return CommonResult.failed("清理失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询执行日志总数
     */
    @ApiOperation("查询执行日志总数")
    @GetMapping("/count")
    public CommonResult<Long> getJobLogCount() {
        try {
            long count = jobLogService.selectJobLogCount();
            return CommonResult.success(count);
        } catch (Exception e) {
            log.error("查询执行日志总数失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询今日执行日志统计
     */
    @ApiOperation("查询今日执行日志统计")
    @GetMapping("/today")
    public CommonResult<Map<String, Object>> getTodayJobLogStatistics() {
        try {
            Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalCount", jobLogService.selectTodayJobLogCount());
            statistics.put("successCount", jobLogService.selectTodaySuccessJobLogCount());
            statistics.put("failureCount", jobLogService.selectTodayFailureJobLogCount());
            
            long total = (Long) statistics.get("totalCount");
            long success = (Long) statistics.get("successCount");
            double successRate = total > 0 ? (double) success / total * 100 : 0;
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            return CommonResult.success(statistics);
        } catch (Exception e) {
            log.error("查询今日执行日志统计失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取任务执行健康度评分
     */
    @ApiOperation("获取任务执行健康度评分")
    @GetMapping("/health/job/{jobId}")
    public CommonResult<Integer> getJobHealthScore(@PathVariable("jobId") Long jobId) {
        try {
            int score = jobLogService.getJobHealthScore(jobId);
            return CommonResult.success(score);
        } catch (Exception e) {
            log.error("获取任务执行健康度评分失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统整体健康度评分
     */
    @ApiOperation("获取系统整体健康度评分")
    @GetMapping("/health/system")
    public CommonResult<Integer> getSystemHealthScore() {
        try {
            int score = jobLogService.getSystemHealthScore();
            return CommonResult.success(score);
        } catch (Exception e) {
            log.error("获取系统整体健康度评分失败", e);
            return CommonResult.failed("获取失败: " + e.getMessage());
        }
    }
    
    /**
     * 预测任务执行时间
     */
    @ApiOperation("预测任务执行时间")
    @GetMapping("/predict/job/{jobId}")
    public CommonResult<Long> predictJobExecuteTime(@PathVariable("jobId") Long jobId) {
        try {
            Long predictTime = jobLogService.predictJobExecuteTime(jobId);
            return CommonResult.success(predictTime);
        } catch (Exception e) {
            log.error("预测任务执行时间失败", e);
            return CommonResult.failed("预测失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析任务执行异常模式
     */
    @ApiOperation("分析任务执行异常模式")
    @GetMapping("/analyze/job/{jobId}")
    public CommonResult<Map<String, Object>> analyzeJobExceptionPattern(
            @PathVariable("jobId") Long jobId, 
            @RequestParam(defaultValue = "7") int days) {
        try {
            Map<String, Object> analysis = jobLogService.analyzeJobExceptionPattern(jobId, days);
            return CommonResult.success(analysis);
        } catch (Exception e) {
            log.error("分析任务执行异常模式失败", e);
            return CommonResult.failed("分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成执行日志报告
     */
    @ApiOperation("生成执行日志报告")
    @PostMapping("/report")
    public CommonResult<Map<String, Object>> generateJobLogReport(
            @RequestParam Date startTime, 
            @RequestParam Date endTime) {
        try {
            Map<String, Object> report = jobLogService.generateJobLogReport(startTime, endTime);
            return CommonResult.success(report);
        } catch (Exception e) {
            log.error("生成执行日志报告失败", e);
            return CommonResult.failed("生成失败: " + e.getMessage());
        }
    }
}
