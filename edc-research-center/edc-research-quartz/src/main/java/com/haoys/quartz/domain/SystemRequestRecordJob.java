package com.haoys.quartz.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统访问记录定时任务实体
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@Data
public class SystemRequestRecordJob implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 任务ID */
    private Long jobId;
    
    /** 任务名称 */
    private String jobName;
    
    /** 任务组名 */
    private String jobGroup;
    
    /** 调用目标字符串 */
    private String invokeTarget;
    
    /** cron执行表达式 */
    private String cronExpression;
    
    /** 计划执行错误策略（1立即执行 2执行一次 3放弃执行） */
    private String misfirePolicy;
    
    /** 是否并发执行（0允许 1禁止） */
    private String concurrent;
    
    /** 状态（0正常 1暂停） */
    private String status;
    
    /** 创建者 */
    private String createBy;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 更新者 */
    private String updateBy;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /** 备注信息 */
    private String remark;
    
    /** 任务描述 */
    private String description;
    
    /** 任务类型（1数据清理 2表优化 3统计报告 4健康检查 5性能统计 6告警检查 7数据归档 8索引优化 9缓存清理） */
    private Integer jobType;
    
    /** 执行参数 */
    private String jobParams;
    
    /** 下次执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextValidTime;
    
    /** 上次执行时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date previousFireTime;
    
    /** 任务执行状态（0正常 1异常） */
    private String executeStatus;
    
    /** 最后执行结果 */
    private String lastExecuteResult;
    
    /** 执行次数 */
    private Long executeCount;
    
    /** 失败次数 */
    private Long failureCount;
    
    /** 平均执行时间（毫秒） */
    private Long avgExecuteTime;
    
    /** 最大执行时间（毫秒） */
    private Long maxExecuteTime;
    
    /** 最小执行时间（毫秒） */
    private Long minExecuteTime;
    
    /** 是否启用 */
    private Boolean enabled;
    
    /** 优先级 */
    private Integer priority;
    
    /** 超时时间（秒） */
    private Integer timeoutSeconds;
    
    /** 重试次数 */
    private Integer retryCount;
    
    /** 重试间隔（秒） */
    private Integer retryInterval;
    
    /** 任务分类 */
    private String category;
    
    /** 任务标签 */
    private String tags;
    
    /** 依赖任务ID */
    private String dependsOn;
    
    /** 执行节点 */
    private String executeNode;
    
    /** 是否发送通知 */
    private Boolean sendNotification;
    
    /** 通知邮箱 */
    private String notificationEmail;
    
    /** 通知手机号 */
    private String notificationPhone;
    
    /** 任务配置JSON */
    private String configJson;
    
    /** 扩展字段1 */
    private String ext1;
    
    /** 扩展字段2 */
    private String ext2;
    
    /** 扩展字段3 */
    private String ext3;
    
    /** 是否删除 */
    private Boolean deleted;
    
    /** 删除时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deleteTime;
    
    /** 删除者 */
    private String deleteBy;
    
    /** 版本号 */
    private Integer version;
    
    /** 租户ID */
    private String tenantId;
    
    // 非数据库字段
    
    /** 任务状态名称 */
    private String statusName;
    
    /** 任务类型名称 */
    private String jobTypeName;
    
    /** 执行状态名称 */
    private String executeStatusName;
    
    /** 下次执行时间描述 */
    private String nextExecuteDesc;
    
    /** 是否正在运行 */
    private Boolean running;
    
    /** 运行状态描述 */
    private String runningDesc;
    
    /**
     * 获取任务状态名称
     */
    public String getStatusName() {
        if ("0".equals(this.status)) {
            return "正常";
        } else if ("1".equals(this.status)) {
            return "暂停";
        }
        return "未知";
    }
    
    /**
     * 获取任务类型名称
     */
    public String getJobTypeName() {
        if (this.jobType == null) {
            return "未知";
        }
        switch (this.jobType) {
            case 1: return "数据清理";
            case 2: return "表优化";
            case 3: return "统计报告";
            case 4: return "健康检查";
            case 5: return "性能统计";
            case 6: return "告警检查";
            case 7: return "数据归档";
            case 8: return "索引优化";
            case 9: return "缓存清理";
            default: return "其他";
        }
    }
    
    /**
     * 获取执行状态名称
     */
    public String getExecuteStatusName() {
        if ("0".equals(this.executeStatus)) {
            return "正常";
        } else if ("1".equals(this.executeStatus)) {
            return "异常";
        }
        return "未知";
    }
    
    /**
     * 获取并发执行描述
     */
    public String getConcurrentDesc() {
        if ("0".equals(this.concurrent)) {
            return "允许";
        } else if ("1".equals(this.concurrent)) {
            return "禁止";
        }
        return "未知";
    }
    
    /**
     * 获取错误策略描述
     */
    public String getMisfirePolicyDesc() {
        if ("1".equals(this.misfirePolicy)) {
            return "立即执行";
        } else if ("2".equals(this.misfirePolicy)) {
            return "执行一次";
        } else if ("3".equals(this.misfirePolicy)) {
            return "放弃执行";
        }
        return "未知";
    }
    
    /**
     * 检查任务是否有效
     */
    public boolean isValid() {
        return this.jobId != null && 
               this.jobName != null && !this.jobName.trim().isEmpty() &&
               this.cronExpression != null && !this.cronExpression.trim().isEmpty() &&
               this.invokeTarget != null && !this.invokeTarget.trim().isEmpty();
    }
    
    /**
     * 检查任务是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.enabled) && "0".equals(this.status);
    }
    
    /**
     * 检查任务是否暂停
     */
    public boolean isPaused() {
        return "1".equals(this.status);
    }
    
    /**
     * 检查是否允许并发执行
     */
    public boolean isConcurrentAllowed() {
        return "0".equals(this.concurrent);
    }
}
