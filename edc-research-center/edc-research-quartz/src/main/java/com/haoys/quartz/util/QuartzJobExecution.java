package com.haoys.quartz.util;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 定时任务处理（允许并发执行）
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public class QuartzJobExecution extends AbstractQuartzJob {
    
    private static final Logger log = LoggerFactory.getLogger(QuartzJobExecution.class);
    
    @Override
    protected void doExecute(JobExecutionContext context, SystemRequestRecordJob job) throws Exception {
        JobInvokeUtil.invokeMethod(job);
    }
}
