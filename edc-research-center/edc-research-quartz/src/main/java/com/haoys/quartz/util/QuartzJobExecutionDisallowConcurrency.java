package com.haoys.quartz.util;

import com.haoys.quartz.domain.SystemRequestRecordJob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
@DisallowConcurrentExecution
public class QuartzJobExecutionDisallowConcurrency extends AbstractQuartzJob {
    
    private static final Logger log = LoggerFactory.getLogger(QuartzJobExecutionDisallowConcurrency.class);
    
    @Override
    protected void doExecute(JobExecutionContext context, SystemRequestR<PERSON>ordJob job) throws Exception {
        JobInvokeUtil.invokeMethod(job);
    }
}
