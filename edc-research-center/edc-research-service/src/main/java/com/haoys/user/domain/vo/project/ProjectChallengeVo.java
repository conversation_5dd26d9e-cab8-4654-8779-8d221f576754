package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class ProjectChallengeVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "质疑编号")
    private String code;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "参与者编号")
    private String testeeCode;

    @ApiModelProperty(value = "参与者研究状态")
    private String testeeStatusValue;

    @ApiModelProperty(value = "参与者姓名")
    private String testeeRealName;

    @ApiModelProperty(value = "参与者所属中心名称")
    private String testeeOrgName;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @ApiModelProperty(value = "表单项分类id")
    private String groupLabelId;

    @ApiModelProperty(value = "表单项名称")
    private String formName;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "表单详情变量名称")
    private String label;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表格id")
    private Long formTableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "提交表单结果id")
    private Long formResultId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "提交表格结果id")
    private Long formResultTableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表格行记录编号")
    private Long formResultTableRowno;

    @ApiModelProperty(value = "变量名称")
    private String formResultLabel;

    @ApiModelProperty(value = "变量key")
    private String formResultKey;

    @ApiModelProperty(value = "变量值")
    private String formResultValue;

    @ApiModelProperty(value = "变量原始值")
    private String formResultOriginalValue;

    @ApiModelProperty(value = "质疑类型 参考字典项004")
    private String type;

    @ApiModelProperty(value = "是否系统生成质疑")
    private Boolean ifSystem;
    @ApiModelProperty(value = "质疑内容")
    private String content;

    @ApiModelProperty(value = "质疑人id")
    private String createUserId;
    @ApiModelProperty(value = "质疑人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建人所属中心")
    private String userOrgId;

    @ApiModelProperty(value = "质疑时间")
    private Date createTime;

    @ApiModelProperty(value = "回复或关闭状态Id")
    private String replyCloseStatus;

    @ApiModelProperty(value = "回复或关闭状态value")
    private String replyCloseStatusValue;

    @ApiModelProperty(value = "质疑关闭原因")
    private String closeReason;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "发起人角色标识 system、CRC、DM等")
    private String userRoleCode;

    @ApiModelProperty(value = "关闭时间")
    private Date closeTime;

    @ApiModelProperty(value = "关闭质疑操作人")
    private String closeUser;

    @ApiModelProperty(value = "关闭质疑操作人角色标识")
    private String closeUserRoleCode;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "质疑回复内容列表")
    private List<ProjectChallengeApplyVo> challengeApplyList = new ArrayList<>();
}
