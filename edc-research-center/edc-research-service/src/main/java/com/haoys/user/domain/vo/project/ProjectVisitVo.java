package com.haoys.user.domain.vo.project;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ProjectVisitVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "方案id")
    private Long planId;

    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @JsonFormat(shape= JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "上一次访视id")
    private Long preVisitId;

    @ApiModelProperty(value = "访视名称")
    private String visitName;

    @ApiModelProperty(value = "访视类型（1-单次录入 2-多次录入）")
    private String visitType;

    @ApiModelProperty(value = "访视归属阶段")
    private String ownerPeroid;

    @ApiModelProperty(value = "访视周期-统一换算为天为单位计算")
    private Integer followUpPeroid;

    @ApiModelProperty(value = "访视单位 单位:天 周 月 年")
    private String followUpUnit;

    @ApiModelProperty(value = "访视窗口期开始")
    private Integer visitWindowStart;

    @ApiModelProperty(value = "访视窗口期截止-下一个访视时间窗口")
    private Integer visitWindowEnd;

    @ApiModelProperty(value = "访视窗口单位 （单位:天 周 月 年）")
    private String visitWindowUnit;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "配置说明")
    private String configData;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    private Long createUser;

    @ApiModelProperty(value = "修改人")
    private Long updateUser;

    @ApiModelProperty(value = "访视状态 0-有效 1-无效")
    private String status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    @ApiModelProperty(value = "是否编辑")
    private boolean isEdit=false;
}
