package com.haoys.user.domain.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统访问日志VO
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "系统访问日志")
public class SystemAccessLogVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "会话ID")
    private String sessionId;

    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    @ApiModelProperty(value = "请求IP地址")
    private String requestIp;

    @ApiModelProperty(value = "访问位置")
    private String location;

    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    @ApiModelProperty(value = "浏览器")
    private String browser;

    @ApiModelProperty(value = "操作系统")
    private String os;

    @ApiModelProperty(value = "设备类型")
    private String deviceType;

    @ApiModelProperty(value = "访问时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accessTime;

    @ApiModelProperty(value = "响应时间(毫秒)")
    private Long responseTime;

    @ApiModelProperty(value = "响应状态码")
    private Integer responseStatus;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    @ApiModelProperty(value = "响应大小(字节)")
    private Long responseSize;

    @ApiModelProperty(value = "来源页面")
    private String referer;

    @ApiModelProperty(value = "访问类型")
    private String accessType;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "是否为登录请求")
    private Boolean isLoginRequest;

    @ApiModelProperty(value = "登录类型")
    private String loginType;

    @ApiModelProperty(value = "手机号")
    private String phoneNumber;

    @ApiModelProperty(value = "登录是否成功")
    private Boolean loginSuccess;

    @ApiModelProperty(value = "登录失败原因")
    private String loginFailureReason;
}
