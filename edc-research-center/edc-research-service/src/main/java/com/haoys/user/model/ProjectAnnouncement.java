package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class ProjectAnnouncement implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "附件ids")
    private String fileIds;

    @ApiModelProperty(value = "发布状态0-未发布/1-已发布")
    private String status;

    @ApiModelProperty(value = "类型 公告1/帮助2")
    private String type;

    @ApiModelProperty(value = "发布类型 SRC-EDC-1/SRC-ePRO-2")
    private String systemType;

    @ApiModelProperty(value = "项目研究中心id")
    private Long projectOrgId;

    @ApiModelProperty(value = "中心名称")
    private String projectOrgName;

    @ApiModelProperty(value = "数据状态 0/1")
    private Boolean sealFlag;

    @ApiModelProperty(value = "创建者")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateUser;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "企业租户id")
    private String tenantId;

    @ApiModelProperty(value = "平台id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getFileIds() {
        return fileIds;
    }

    public void setFileIds(String fileIds) {
        this.fileIds = fileIds;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSystemType() {
        return systemType;
    }

    public void setSystemType(String systemType) {
        this.systemType = systemType;
    }

    public Long getProjectOrgId() {
        return projectOrgId;
    }

    public void setProjectOrgId(Long projectOrgId) {
        this.projectOrgId = projectOrgId;
    }

    public String getProjectOrgName() {
        return projectOrgName;
    }

    public void setProjectOrgName(String projectOrgName) {
        this.projectOrgName = projectOrgName;
    }

    public Boolean getSealFlag() {
        return sealFlag;
    }

    public void setSealFlag(Boolean sealFlag) {
        this.sealFlag = sealFlag;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", title=").append(title);
        sb.append(", content=").append(content);
        sb.append(", fileIds=").append(fileIds);
        sb.append(", status=").append(status);
        sb.append(", type=").append(type);
        sb.append(", systemType=").append(systemType);
        sb.append(", projectOrgId=").append(projectOrgId);
        sb.append(", projectOrgName=").append(projectOrgName);
        sb.append(", sealFlag=").append(sealFlag);
        sb.append(", createUser=").append(createUser);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateUser=").append(updateUser);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}