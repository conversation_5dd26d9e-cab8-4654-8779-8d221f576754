package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class ProjectAnalysisCode implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "分析批次名称")
    private String title;

    @ApiModelProperty(value = "分析批次记录code")
    private String batchCode;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "数据更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "操作人")
    private String createUserId;

    @ApiModelProperty(value = "诊断分析结果")
    private String diagnosticResult;

    @ApiModelProperty(value = "药方分析结果")
    private String prescriptionResult;

    @ApiModelProperty(value = "数据状态 0-正常 1-封存")
    private Boolean sealFlag;

    @ApiModelProperty(value = "平台id-指向项目id")
    private String platformId;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBatchCode() {
        return batchCode;
    }

    public void setBatchCode(String batchCode) {
        this.batchCode = batchCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getDiagnosticResult() {
        return diagnosticResult;
    }

    public void setDiagnosticResult(String diagnosticResult) {
        this.diagnosticResult = diagnosticResult;
    }

    public String getPrescriptionResult() {
        return prescriptionResult;
    }

    public void setPrescriptionResult(String prescriptionResult) {
        this.prescriptionResult = prescriptionResult;
    }

    public Boolean getSealFlag() {
        return sealFlag;
    }

    public void setSealFlag(Boolean sealFlag) {
        this.sealFlag = sealFlag;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", title=").append(title);
        sb.append(", batchCode=").append(batchCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", diagnosticResult=").append(diagnosticResult);
        sb.append(", prescriptionResult=").append(prescriptionResult);
        sb.append(", sealFlag=").append(sealFlag);
        sb.append(", platformId=").append(platformId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}