package com.haoys.user.domain.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统访问统计VO
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "系统访问统计")
public class SystemAccessStatisticsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "统计日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statDate;

    @ApiModelProperty(value = "总访问次数")
    private Long totalVisits;

    @ApiModelProperty(value = "独立访客数")
    private Long uniqueVisitors;

    @ApiModelProperty(value = "登录次数")
    private Long loginCount;

    @ApiModelProperty(value = "独立登录用户数")
    private Long uniqueLoginUsers;

    @ApiModelProperty(value = "页面浏览量")
    private Long pageViews;

    @ApiModelProperty(value = "API调用次数")
    private Long apiCalls;

    @ApiModelProperty(value = "平均响应时间(毫秒)")
    private BigDecimal avgResponseTime;

    @ApiModelProperty(value = "最大在线用户数")
    private Integer maxOnlineUsers;

    @ApiModelProperty(value = "错误次数")
    private Long errorCount;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 统计概览VO
     */
    @Data
    @Accessors(chain = true)
    @ApiModel(description = "统计概览")
    public static class StatisticsOverview implements Serializable {
        
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "今日访问次数")
        private Long todayVisits;

        @ApiModelProperty(value = "今日独立访客")
        private Long todayUniqueVisitors;

        @ApiModelProperty(value = "今日登录次数")
        private Long todayLogins;

        @ApiModelProperty(value = "当前在线用户数")
        private Long currentOnlineUsers;

        @ApiModelProperty(value = "总访问次数")
        private Long totalVisits;

        @ApiModelProperty(value = "总用户数")
        private Long totalUsers;

        @ApiModelProperty(value = "本周访问次数")
        private Long weekVisits;

        @ApiModelProperty(value = "本月访问次数")
        private Long monthVisits;

        @ApiModelProperty(value = "平均响应时间(毫秒)")
        private BigDecimal avgResponseTime;

        @ApiModelProperty(value = "系统健康状态")
        private String systemHealth;
    }
}
