package com.haoys.user.domain.vo.project;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.auth.ProjectRoleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ProjectUserVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    private Long projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色标识")
    private String roleCode;

    @ApiModelProperty(value = "项目用户表的id")
    private String puId;

    @ApiModelProperty(value = "是否PA角色")
    private Boolean paRole;

    @ApiModelProperty(value = "项目研究中心id，多个使用逗号隔开")
    private String projectOrgIds;

    @ApiModelProperty(value = "项目研究中心角色id，多个使用逗号隔开")
    private String projectOrgRoleIds;

    @ApiModelProperty(value = "项目角色")
    private List<ProjectRoleVo> projectRoleList = new ArrayList<>();

    @ApiModelProperty(value = "项目研究角色")
    private List<ProjectOrgRoleInfo> projectOrgRoleList = new ArrayList<>();

    @ApiModelProperty(value = "项目角色列表")
    Map<Long, List<ProjectRoleVo>> projectRoleMap = new HashMap<>();

    @ApiModelProperty(value = "项目研究中心列表")
    Map<Long, List<ProjectOrgVo>> projectOrgMap = new HashMap<>();

    @Data
    public static class ProjectOrgRoleInfo {
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        private Long roleId;
        private String roleName;
        private String ename;
        @ApiModelProperty(value = "是否显示全部研究中心")
        private Boolean showAllOwnerOrgName = false;
        private Boolean ownerTotalAuth = false;

        private List<ProjectOrgInfo> projectOrgList = new ArrayList<>();
    }

    @Data
    public static class ProjectOrgInfo {
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        private Long orgId;
        @JsonFormat(shape=JsonFormat.Shape.STRING)
        private Long projectOrgId;
        private String orgName;
        private String projectOrgCode;
    }

    @ApiModelProperty(value = "所属部门")
    private String departmentId;

    private String departmentName;

    @ApiModelProperty(value = "职称ID")
    private String positionalId;

    @ApiModelProperty(value = "职称名称")
    private String positionalName;

    private Date createtime;

    private String status;

    private String createUserId;

    private Boolean activeStatus = false;

    private Boolean projectCreateUser = false;

    @ApiModelProperty(value = "注册方式和来源")
    private String registerType;

    @ApiModelProperty(value = "锁定状态0/1")
    private Boolean lockStatus;

}
