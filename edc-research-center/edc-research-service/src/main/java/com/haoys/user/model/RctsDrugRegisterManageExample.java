package com.haoys.user.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RctsDrugRegisterManageExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RctsDrugRegisterManageExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNull() {
            addCriterion("project_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectIdIsNotNull() {
            addCriterion("project_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectIdEqualTo(Long value) {
            addCriterion("project_id =", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotEqualTo(Long value) {
            addCriterion("project_id <>", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThan(Long value) {
            addCriterion("project_id >", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdGreaterThanOrEqualTo(Long value) {
            addCriterion("project_id >=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThan(Long value) {
            addCriterion("project_id <", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdLessThanOrEqualTo(Long value) {
            addCriterion("project_id <=", value, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdIn(List<Long> values) {
            addCriterion("project_id in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotIn(List<Long> values) {
            addCriterion("project_id not in", values, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdBetween(Long value1, Long value2) {
            addCriterion("project_id between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andProjectIdNotBetween(Long value1, Long value2) {
            addCriterion("project_id not between", value1, value2, "projectId");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNull() {
            addCriterion("Material is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIsNotNull() {
            addCriterion("Material is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialEqualTo(String value) {
            addCriterion("Material =", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotEqualTo(String value) {
            addCriterion("Material <>", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThan(String value) {
            addCriterion("Material >", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialGreaterThanOrEqualTo(String value) {
            addCriterion("Material >=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThan(String value) {
            addCriterion("Material <", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLessThanOrEqualTo(String value) {
            addCriterion("Material <=", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialLike(String value) {
            addCriterion("Material like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotLike(String value) {
            addCriterion("Material not like", value, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialIn(List<String> values) {
            addCriterion("Material in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotIn(List<String> values) {
            addCriterion("Material not in", values, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialBetween(String value1, String value2) {
            addCriterion("Material between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialNotBetween(String value1, String value2) {
            addCriterion("Material not between", value1, value2, "material");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("material_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("material_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("material_code =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("material_code <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("material_code >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_code >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("material_code <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("material_code <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("material_code like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("material_code not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("material_code in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("material_code not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("material_code between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("material_code not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameIsNull() {
            addCriterion("merchandise_name is null");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameIsNotNull() {
            addCriterion("merchandise_name is not null");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameEqualTo(String value) {
            addCriterion("merchandise_name =", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameNotEqualTo(String value) {
            addCriterion("merchandise_name <>", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameGreaterThan(String value) {
            addCriterion("merchandise_name >", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameGreaterThanOrEqualTo(String value) {
            addCriterion("merchandise_name >=", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameLessThan(String value) {
            addCriterion("merchandise_name <", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameLessThanOrEqualTo(String value) {
            addCriterion("merchandise_name <=", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameLike(String value) {
            addCriterion("merchandise_name like", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameNotLike(String value) {
            addCriterion("merchandise_name not like", value, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameIn(List<String> values) {
            addCriterion("merchandise_name in", values, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameNotIn(List<String> values) {
            addCriterion("merchandise_name not in", values, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameBetween(String value1, String value2) {
            addCriterion("merchandise_name between", value1, value2, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andMerchandiseNameNotBetween(String value1, String value2) {
            addCriterion("merchandise_name not between", value1, value2, "merchandiseName");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("manufacturer is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("manufacturer is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("manufacturer =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("manufacturer <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("manufacturer >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("manufacturer >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("manufacturer <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("manufacturer <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("manufacturer like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("manufacturer not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("manufacturer in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("manufacturer not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("manufacturer between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("manufacturer not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andCountryIsNull() {
            addCriterion("country is null");
            return (Criteria) this;
        }

        public Criteria andCountryIsNotNull() {
            addCriterion("country is not null");
            return (Criteria) this;
        }

        public Criteria andCountryEqualTo(String value) {
            addCriterion("country =", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotEqualTo(String value) {
            addCriterion("country <>", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThan(String value) {
            addCriterion("country >", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryGreaterThanOrEqualTo(String value) {
            addCriterion("country >=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThan(String value) {
            addCriterion("country <", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLessThanOrEqualTo(String value) {
            addCriterion("country <=", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryLike(String value) {
            addCriterion("country like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotLike(String value) {
            addCriterion("country not like", value, "country");
            return (Criteria) this;
        }

        public Criteria andCountryIn(List<String> values) {
            addCriterion("country in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotIn(List<String> values) {
            addCriterion("country not in", values, "country");
            return (Criteria) this;
        }

        public Criteria andCountryBetween(String value1, String value2) {
            addCriterion("country between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andCountryNotBetween(String value1, String value2) {
            addCriterion("country not between", value1, value2, "country");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusIsNull() {
            addCriterion("material_status is null");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusIsNotNull() {
            addCriterion("material_status is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusEqualTo(String value) {
            addCriterion("material_status =", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusNotEqualTo(String value) {
            addCriterion("material_status <>", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusGreaterThan(String value) {
            addCriterion("material_status >", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusGreaterThanOrEqualTo(String value) {
            addCriterion("material_status >=", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusLessThan(String value) {
            addCriterion("material_status <", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusLessThanOrEqualTo(String value) {
            addCriterion("material_status <=", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusLike(String value) {
            addCriterion("material_status like", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusNotLike(String value) {
            addCriterion("material_status not like", value, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusIn(List<String> values) {
            addCriterion("material_status in", values, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusNotIn(List<String> values) {
            addCriterion("material_status not in", values, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusBetween(String value1, String value2) {
            addCriterion("material_status between", value1, value2, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialStatusNotBetween(String value1, String value2) {
            addCriterion("material_status not between", value1, value2, "materialStatus");
            return (Criteria) this;
        }

        public Criteria andMaterialCountIsNull() {
            addCriterion("material_count is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCountIsNotNull() {
            addCriterion("material_count is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCountEqualTo(Integer value) {
            addCriterion("material_count =", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotEqualTo(Integer value) {
            addCriterion("material_count <>", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThan(Integer value) {
            addCriterion("material_count >", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_count >=", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThan(Integer value) {
            addCriterion("material_count <", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountLessThanOrEqualTo(Integer value) {
            addCriterion("material_count <=", value, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountIn(List<Integer> values) {
            addCriterion("material_count in", values, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotIn(List<Integer> values) {
            addCriterion("material_count not in", values, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountBetween(Integer value1, Integer value2) {
            addCriterion("material_count between", value1, value2, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialCountNotBetween(Integer value1, Integer value2) {
            addCriterion("material_count not between", value1, value2, "materialCount");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationIsNull() {
            addCriterion("material_specification is null");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationIsNotNull() {
            addCriterion("material_specification is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationEqualTo(String value) {
            addCriterion("material_specification =", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationNotEqualTo(String value) {
            addCriterion("material_specification <>", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationGreaterThan(String value) {
            addCriterion("material_specification >", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationGreaterThanOrEqualTo(String value) {
            addCriterion("material_specification >=", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationLessThan(String value) {
            addCriterion("material_specification <", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationLessThanOrEqualTo(String value) {
            addCriterion("material_specification <=", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationLike(String value) {
            addCriterion("material_specification like", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationNotLike(String value) {
            addCriterion("material_specification not like", value, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationIn(List<String> values) {
            addCriterion("material_specification in", values, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationNotIn(List<String> values) {
            addCriterion("material_specification not in", values, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationBetween(String value1, String value2) {
            addCriterion("material_specification between", value1, value2, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andMaterialSpecificationNotBetween(String value1, String value2) {
            addCriterion("material_specification not between", value1, value2, "materialSpecification");
            return (Criteria) this;
        }

        public Criteria andPackageUnitIsNull() {
            addCriterion("`package unit` is null");
            return (Criteria) this;
        }

        public Criteria andPackageUnitIsNotNull() {
            addCriterion("`package unit` is not null");
            return (Criteria) this;
        }

        public Criteria andPackageUnitEqualTo(String value) {
            addCriterion("`package unit` =", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitNotEqualTo(String value) {
            addCriterion("`package unit` <>", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitGreaterThan(String value) {
            addCriterion("`package unit` >", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitGreaterThanOrEqualTo(String value) {
            addCriterion("`package unit` >=", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitLessThan(String value) {
            addCriterion("`package unit` <", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitLessThanOrEqualTo(String value) {
            addCriterion("`package unit` <=", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitLike(String value) {
            addCriterion("`package unit` like", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitNotLike(String value) {
            addCriterion("`package unit` not like", value, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitIn(List<String> values) {
            addCriterion("`package unit` in", values, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitNotIn(List<String> values) {
            addCriterion("`package unit` not in", values, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitBetween(String value1, String value2) {
            addCriterion("`package unit` between", value1, value2, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andPackageUnitNotBetween(String value1, String value2) {
            addCriterion("`package unit` not between", value1, value2, "packageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitIsNull() {
            addCriterion("`min_package unit` is null");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitIsNotNull() {
            addCriterion("`min_package unit` is not null");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitEqualTo(Integer value) {
            addCriterion("`min_package unit` =", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitNotEqualTo(Integer value) {
            addCriterion("`min_package unit` <>", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitGreaterThan(Integer value) {
            addCriterion("`min_package unit` >", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitGreaterThanOrEqualTo(Integer value) {
            addCriterion("`min_package unit` >=", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitLessThan(Integer value) {
            addCriterion("`min_package unit` <", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitLessThanOrEqualTo(Integer value) {
            addCriterion("`min_package unit` <=", value, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitIn(List<Integer> values) {
            addCriterion("`min_package unit` in", values, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitNotIn(List<Integer> values) {
            addCriterion("`min_package unit` not in", values, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitBetween(Integer value1, Integer value2) {
            addCriterion("`min_package unit` between", value1, value2, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andMinPackageUnitNotBetween(Integer value1, Integer value2) {
            addCriterion("`min_package unit` not between", value1, value2, "minPackageUnit");
            return (Criteria) this;
        }

        public Criteria andWarningValueIsNull() {
            addCriterion("warning_value is null");
            return (Criteria) this;
        }

        public Criteria andWarningValueIsNotNull() {
            addCriterion("warning_value is not null");
            return (Criteria) this;
        }

        public Criteria andWarningValueEqualTo(Integer value) {
            addCriterion("warning_value =", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotEqualTo(Integer value) {
            addCriterion("warning_value <>", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueGreaterThan(Integer value) {
            addCriterion("warning_value >", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("warning_value >=", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueLessThan(Integer value) {
            addCriterion("warning_value <", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueLessThanOrEqualTo(Integer value) {
            addCriterion("warning_value <=", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueIn(List<Integer> values) {
            addCriterion("warning_value in", values, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotIn(List<Integer> values) {
            addCriterion("warning_value not in", values, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueBetween(Integer value1, Integer value2) {
            addCriterion("warning_value between", value1, value2, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotBetween(Integer value1, Integer value2) {
            addCriterion("warning_value not between", value1, value2, "warningValue");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementIsNull() {
            addCriterion("storage_requirement is null");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementIsNotNull() {
            addCriterion("storage_requirement is not null");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementEqualTo(String value) {
            addCriterion("storage_requirement =", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementNotEqualTo(String value) {
            addCriterion("storage_requirement <>", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementGreaterThan(String value) {
            addCriterion("storage_requirement >", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementGreaterThanOrEqualTo(String value) {
            addCriterion("storage_requirement >=", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementLessThan(String value) {
            addCriterion("storage_requirement <", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementLessThanOrEqualTo(String value) {
            addCriterion("storage_requirement <=", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementLike(String value) {
            addCriterion("storage_requirement like", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementNotLike(String value) {
            addCriterion("storage_requirement not like", value, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementIn(List<String> values) {
            addCriterion("storage_requirement in", values, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementNotIn(List<String> values) {
            addCriterion("storage_requirement not in", values, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementBetween(String value1, String value2) {
            addCriterion("storage_requirement between", value1, value2, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andStorageRequirementNotBetween(String value1, String value2) {
            addCriterion("storage_requirement not between", value1, value2, "storageRequirement");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateIsNull() {
            addCriterion("expiration_start_date is null");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateIsNotNull() {
            addCriterion("expiration_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateEqualTo(Date value) {
            addCriterion("expiration_start_date =", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateNotEqualTo(Date value) {
            addCriterion("expiration_start_date <>", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateGreaterThan(Date value) {
            addCriterion("expiration_start_date >", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("expiration_start_date >=", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateLessThan(Date value) {
            addCriterion("expiration_start_date <", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateLessThanOrEqualTo(Date value) {
            addCriterion("expiration_start_date <=", value, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateIn(List<Date> values) {
            addCriterion("expiration_start_date in", values, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateNotIn(List<Date> values) {
            addCriterion("expiration_start_date not in", values, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateBetween(Date value1, Date value2) {
            addCriterion("expiration_start_date between", value1, value2, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationStartDateNotBetween(Date value1, Date value2) {
            addCriterion("expiration_start_date not between", value1, value2, "expirationStartDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateIsNull() {
            addCriterion("expiration_end_date is null");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateIsNotNull() {
            addCriterion("expiration_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateEqualTo(Date value) {
            addCriterion("expiration_end_date =", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateNotEqualTo(Date value) {
            addCriterion("expiration_end_date <>", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateGreaterThan(Date value) {
            addCriterion("expiration_end_date >", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("expiration_end_date >=", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateLessThan(Date value) {
            addCriterion("expiration_end_date <", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateLessThanOrEqualTo(Date value) {
            addCriterion("expiration_end_date <=", value, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateIn(List<Date> values) {
            addCriterion("expiration_end_date in", values, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateNotIn(List<Date> values) {
            addCriterion("expiration_end_date not in", values, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateBetween(Date value1, Date value2) {
            addCriterion("expiration_end_date between", value1, value2, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationEndDateNotBetween(Date value1, Date value2) {
            addCriterion("expiration_end_date not between", value1, value2, "expirationEndDate");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueIsNull() {
            addCriterion("expiration_warning_value is null");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueIsNotNull() {
            addCriterion("expiration_warning_value is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueEqualTo(Integer value) {
            addCriterion("expiration_warning_value =", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueNotEqualTo(Integer value) {
            addCriterion("expiration_warning_value <>", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueGreaterThan(Integer value) {
            addCriterion("expiration_warning_value >", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueGreaterThanOrEqualTo(Integer value) {
            addCriterion("expiration_warning_value >=", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueLessThan(Integer value) {
            addCriterion("expiration_warning_value <", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueLessThanOrEqualTo(Integer value) {
            addCriterion("expiration_warning_value <=", value, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueIn(List<Integer> values) {
            addCriterion("expiration_warning_value in", values, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueNotIn(List<Integer> values) {
            addCriterion("expiration_warning_value not in", values, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueBetween(Integer value1, Integer value2) {
            addCriterion("expiration_warning_value between", value1, value2, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andExpirationWarningValueNotBetween(Integer value1, Integer value2) {
            addCriterion("expiration_warning_value not between", value1, value2, "expirationWarningValue");
            return (Criteria) this;
        }

        public Criteria andWarningStatusIsNull() {
            addCriterion("warning_status is null");
            return (Criteria) this;
        }

        public Criteria andWarningStatusIsNotNull() {
            addCriterion("warning_status is not null");
            return (Criteria) this;
        }

        public Criteria andWarningStatusEqualTo(String value) {
            addCriterion("warning_status =", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusNotEqualTo(String value) {
            addCriterion("warning_status <>", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusGreaterThan(String value) {
            addCriterion("warning_status >", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusGreaterThanOrEqualTo(String value) {
            addCriterion("warning_status >=", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusLessThan(String value) {
            addCriterion("warning_status <", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusLessThanOrEqualTo(String value) {
            addCriterion("warning_status <=", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusLike(String value) {
            addCriterion("warning_status like", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusNotLike(String value) {
            addCriterion("warning_status not like", value, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusIn(List<String> values) {
            addCriterion("warning_status in", values, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusNotIn(List<String> values) {
            addCriterion("warning_status not in", values, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusBetween(String value1, String value2) {
            addCriterion("warning_status between", value1, value2, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andWarningStatusNotBetween(String value1, String value2) {
            addCriterion("warning_status not between", value1, value2, "warningStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExpandIsNull() {
            addCriterion("expand is null");
            return (Criteria) this;
        }

        public Criteria andExpandIsNotNull() {
            addCriterion("expand is not null");
            return (Criteria) this;
        }

        public Criteria andExpandEqualTo(String value) {
            addCriterion("expand =", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotEqualTo(String value) {
            addCriterion("expand <>", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThan(String value) {
            addCriterion("expand >", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandGreaterThanOrEqualTo(String value) {
            addCriterion("expand >=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThan(String value) {
            addCriterion("expand <", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLessThanOrEqualTo(String value) {
            addCriterion("expand <=", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandLike(String value) {
            addCriterion("expand like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotLike(String value) {
            addCriterion("expand not like", value, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandIn(List<String> values) {
            addCriterion("expand in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotIn(List<String> values) {
            addCriterion("expand not in", values, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandBetween(String value1, String value2) {
            addCriterion("expand between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andExpandNotBetween(String value1, String value2) {
            addCriterion("expand not between", value1, value2, "expand");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(String value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(String value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(String value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(String value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(String value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLike(String value) {
            addCriterion("update_user_id like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotLike(String value) {
            addCriterion("update_user_id not like", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<String> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<String> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(String value1, String value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(String value1, String value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(String value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(String value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(String value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(String value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(String value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(String value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLike(String value) {
            addCriterion("platform_id like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotLike(String value) {
            addCriterion("platform_id not like", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<String> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<String> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(String value1, String value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(String value1, String value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}