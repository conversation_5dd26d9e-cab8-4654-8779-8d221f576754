package com.haoys.user.domain.vo.ecrf;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.domain.vo.testee.ProjectTesteeFormImageVo;
import com.haoys.user.model.TemplateFormVariableRule;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProjectTesteeTableVo {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表格行记录编号")
    private Long rowNumber;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表格行记录id")
    private Long id;

    @ApiModelProperty(value = "行记录valueId")
    private String testeeResultId = "";

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字段组id")
    private Long groupId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字段组表格变量id")
    private Long resourceVariableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单表格id")
    private Long formDetailId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项表格列id")
    private Long formTableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "字段组表格列id")
    private Long resourceTableId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "参与者id")
    private Long testeeId;

    @ApiModelProperty(value = "表单组件名称")
    private String type;

    @ApiModelProperty(value = "表单变量-下拉框数据集")
    private String options;

    @ApiModelProperty(value = "是否隐藏字段")
    private Boolean hidden;

    @ApiModelProperty(value = "字段名称")
    private String label;

    @ApiModelProperty(value = "组件是否必填")
    private Boolean required;

    @ApiModelProperty(value = "字段英文名称")
    private String langValue;

    @ApiModelProperty(value = "字段提示语")
    private String placeholder;

    @ApiModelProperty(value = "数据库字段code")
    private String fieldName = "";

    @ApiModelProperty(value = "表格行记录提交记录")
    private String fieldValue = "";
    
    @ApiModelProperty(value = "字段文本值")
    private String fieldText;

    @ApiModelProperty(value = "表格图片")
    List<ProjectTesteeFormImageVo> variableImageList = new ArrayList<>();

    @ApiModelProperty(value = "计量单位")
    private String unitValue = "";
    
    @ApiModelProperty(value = "单位文本值")
    private String unitText;

    @ApiModelProperty(value = "默认值")
    private String defaultValue;

    @ApiModelProperty(value = "必填类型1-非必填2-强制必填3-必填提示")
    private String requireType;

    @ApiModelProperty(value = "字典来源(1-系统字典2-项目表单字典 3-单位字典)")
    private String dicResource;

    @ApiModelProperty(value = "引用字典id")
    private String refDicId;

    @ApiModelProperty(value = "字典默认值")
    private String defaultDicValue;

    @ApiModelProperty(value = "扩展属性-格式化")
    private Object expandValue;

    @ApiModelProperty(value = "扩展属性")
    private String expand;

    @ApiModelProperty(value = "扩展字段1")
    private String extData1;

    @ApiModelProperty(value = "扩展字段2")
    private String extData2;

    @ApiModelProperty(value = "扩展字段3")
    private String extData3;

    @ApiModelProperty(value = "扩展字段4")
    private String extData4;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "所属变量id")
    private Long pointVariableId;

    @ApiModelProperty(value = "是否启用关联属性")
    private Boolean enableAssociate;

    @ApiModelProperty(value = "条件表达式")
    private String conditionExpression;

    @ApiModelProperty(value = "表格行记录变量质疑标识")
    private String challengeStatus;

    @ApiModelProperty(value = "表单详情发起质疑按钮")
    private String challengeButtonStatus;

    @ApiModelProperty(value = "变量录入完成状态")
    private String complateStatus;

    @ApiModelProperty(value = "变量逻辑核查规则")
    private List<TemplateFormDvpRuleVo> templateFormDvpRuleList = new ArrayList<>();

    //@ApiModelProperty(value = "系统质疑描述信息")
    //private List<ProjectChallengeVo> projectChallengeList = new ArrayList<>();

    private List<TemplateFormDictionaryVo> templateFormDictionaryList = new ArrayList<>();

    @ApiModelProperty(value = "公式计算信息")
    private TemplateFormVariableRule variableRule;

}
