package com.haoys.user.domain.vo.ecrf;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.haoys.user.model.ProjectVisitConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Data
public class TemplateFormLogicVo implements Serializable {

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "模板id")
    private Long templateId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "访视id")
    private Long visitId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项id")
    private Long formId;

    @JsonFormat(shape=JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "表单项详情id")
    private Long formDetailId;

    @ApiModelProperty(value = "标签名称")
    private String label;

    @ApiModelProperty(value = "选项内容")
    private String optionList;

    @ApiModelProperty(value = "条件选项")
    private String expressionId;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "修改人")
    private String updateUser;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "扩展字段")
    private String extands;

    @ApiModelProperty(value = "数据状态")
    private String status;

    @ApiModelProperty(value = "废弃")
    private String conditionList;

    @ApiModelProperty(value = "条件表达式")
    private List<TemplateFormLogicDataVo> dataList = new ArrayList<>();

    @ApiModelProperty(value = "访视-下拉框填充数据")
    private List<ProjectVisitConfig> visitOptionList = new ArrayList<>();
    @ApiModelProperty(value = "表单-下拉框填充数据")
    private List<TemplateFormConfigVo> formOptionList = new ArrayList<>();
    @ApiModelProperty(value = "变量-下拉框填充数据")
    private List<TemplateFormDetailVo> detailOptionList = new ArrayList<>();

    @Data
    public static class TemplateFormLogicDataVo implements Serializable {

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "逻辑显示条件记录id-编辑时设置此参数")
        private Long id;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "显示条件id")
        private Long logicId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "模板id")
        private Long templateId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "引用访视id")
        private Long targetVisitId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "引用表单id")
        private Long targetFormId;

        @JsonFormat(shape=JsonFormat.Shape.STRING)
        @ApiModelProperty(value = "引用变量id")
        private Long targetDetailId;

        @ApiModelProperty(value = "访视-下拉框填充数据")
        private List<ProjectVisitConfig> visitOptionList = new ArrayList<>();
        @ApiModelProperty(value = "表单-下拉框填充数据")
        private List<TemplateFormConfigVo> formOptionList = new ArrayList<>();
        @ApiModelProperty(value = "变量-下拉框填充数据")
        private List<TemplateFormDetailVo> detailOptionList = new ArrayList<>();

        @ApiModelProperty(value = "条件表达式 示例{\"expression\": \"eq\", \"optionValue\": 1}\"")
        private String conditionValue;

        @ApiModelProperty(value = "数据状态")
        private String status;

        @ApiModelProperty(value = "扩展字段")
        private String extands;

        @ApiModelProperty(value = "参与者提交记录值")
        private String testeeResultValue;

        @ApiModelProperty(value = "创建时间")
        private Date createTime;

        @ApiModelProperty(value = "修改时间")
        private Date updateTime;

        @ApiModelProperty(value = "创建人")
        private String createUserId;

        @ApiModelProperty(value = "修改人")
        private String updateUserId;
    }
}
