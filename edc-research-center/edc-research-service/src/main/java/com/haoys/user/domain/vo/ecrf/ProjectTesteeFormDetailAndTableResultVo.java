package com.haoys.user.domain.vo.ecrf;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
public class ProjectTesteeFormDetailAndTableResultVo implements Serializable {

    private String projectId;

    private String visitId;

    private String formId;

    private String formDetailId;

    private String testeeId;

    private String formResultId;

    private String fieldName;

    private String fieldValue;

    private List<ProjectTesteeTableResultVo> dataList = new ArrayList<>();

    @Data
    public static class ProjectTesteeTableResultVo{

        private String formTableId;

        private String formResultTableId;

        private String fieldName;

        private String fieldValue;

    }

}
