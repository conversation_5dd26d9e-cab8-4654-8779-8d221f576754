package com.haoys.user.model;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

public class ProjectPatientResult implements Serializable {
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "方案id")
    private Long planId;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "患者id")
    private Long testeeId;

    @ApiModelProperty(value = "任务日期")
    private Date taskDate;

    @ApiModelProperty(value = "任务触发频率 每天、每周、每月、每年")
    private String taskRate;

    @ApiModelProperty(value = "每日推送频率-冗余字段")
    private Integer pushRate;

    @ApiModelProperty(value = "任务表单类型 普通表单、不良事件、合并用药")
    private String taskType;

    @ApiModelProperty(value = "任务完成次数")
    private Integer complateCount;

    @ApiModelProperty(value = "今日任务完成时间")
    private Date complateTime;

    @ApiModelProperty(value = "任务完成状态 1-待录入、2-已录入、3-已完成")
    private String complateStatus;

    @ApiModelProperty(value = "扩展字段")
    private String expand;

    @ApiModelProperty(value = "任务表单提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "任务生成时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getTesteeId() {
        return testeeId;
    }

    public void setTesteeId(Long testeeId) {
        this.testeeId = testeeId;
    }

    public Date getTaskDate() {
        return taskDate;
    }

    public void setTaskDate(Date taskDate) {
        this.taskDate = taskDate;
    }

    public String getTaskRate() {
        return taskRate;
    }

    public void setTaskRate(String taskRate) {
        this.taskRate = taskRate;
    }

    public Integer getPushRate() {
        return pushRate;
    }

    public void setPushRate(Integer pushRate) {
        this.pushRate = pushRate;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getComplateCount() {
        return complateCount;
    }

    public void setComplateCount(Integer complateCount) {
        this.complateCount = complateCount;
    }

    public Date getComplateTime() {
        return complateTime;
    }

    public void setComplateTime(Date complateTime) {
        this.complateTime = complateTime;
    }

    public String getComplateStatus() {
        return complateStatus;
    }

    public void setComplateStatus(String complateStatus) {
        this.complateStatus = complateStatus;
    }

    public String getExpand() {
        return expand;
    }

    public void setExpand(String expand) {
        this.expand = expand;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", projectId=").append(projectId);
        sb.append(", planId=").append(planId);
        sb.append(", taskId=").append(taskId);
        sb.append(", taskName=").append(taskName);
        sb.append(", testeeId=").append(testeeId);
        sb.append(", taskDate=").append(taskDate);
        sb.append(", taskRate=").append(taskRate);
        sb.append(", pushRate=").append(pushRate);
        sb.append(", taskType=").append(taskType);
        sb.append(", complateCount=").append(complateCount);
        sb.append(", complateTime=").append(complateTime);
        sb.append(", complateStatus=").append(complateStatus);
        sb.append(", expand=").append(expand);
        sb.append(", submitTime=").append(submitTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}