<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定时任务管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .auth-section {
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
        }

        .auth-form {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            display: none;
        }

        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .main-content {
            display: none;
            padding: 40px;
        }

        .tabs {
            margin-bottom: 30px;
        }

        .tab-header {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 20px;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 25px;
            font-size: 16px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9fa;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .toolbar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .toolbar-right {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #138496;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .search-form {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .search-group {
            flex: 1;
            min-width: 200px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .search-group input,
        .search-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e5e9;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .status-normal {
            color: #28a745;
            font-weight: bold;
        }

        .status-paused {
            color: #ffc107;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .status-running {
            color: #17a2b8;
            font-weight: bold;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            display: none;
        }

        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .alert.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stats-card h3 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stats-card p {
            color: #666;
            font-size: 1.1em;
        }

        .checkbox-group {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 5px;
        }

        @media (max-width: 768px) {
            .search-row {
                flex-direction: column;
            }
            
            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .toolbar-left,
            .toolbar-right {
                justify-content: center;
            }
            
            .modal-content {
                width: 95%;
                margin: 10% auto;
            }
            
            .form-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>定时任务管理</h1>
            <p>智能调度 · 实时监控 · 高效管理</p>
        </div>

        <!-- 认证区域 -->
        <div class="auth-section" id="authSection">
            <div class="auth-form" id="secretKeyForm">
                <h3>第一步：秘钥验证</h3>
                <div class="alert" id="secretAlert"></div>
                <div class="form-group">
                    <label for="secretKey">请输入配置秘钥：</label>
                    <input type="password" id="secretKey" placeholder="请输入秘钥">
                </div>
                <button class="btn" onclick="verifySecretKey()">验证秘钥</button>
            </div>

            <div class="auth-form" id="tokenForm" style="display: none;">
                <h3>第二步：获取访问令牌</h3>
                <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 14px;">
                    <strong>🔑 访问令牌获取说明</strong><br>
                    请联系系统管理员获取有效的验证码和刷新码，或使用generateCode方法生成
                </div>
                <div class="alert" id="tokenAlert"></div>
                <div class="form-group">
                    <label for="code">验证码 (Code)：</label>
                    <input type="text" id="code" placeholder="请输入验证码">
                </div>
                <div class="form-group">
                    <label for="refreshCode">刷新码 (RefreshCode)：</label>
                    <input type="text" id="refreshCode" placeholder="请输入刷新码">
                </div>
                <button class="btn" onclick="getAccessToken()">获取访问令牌</button>
            </div>

            <div class="auth-form" id="validateForm" style="display: none;">
                <h3>第三步：验证访问令牌</h3>
                <div style="background: #f0f8ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 14px;">
                    <strong>🚀 访问令牌验证</strong><br>
                    系统正在验证您的访问令牌，验证成功后将自动跳转到定时任务管理页面
                </div>
                <div class="loading" id="loading" style="text-align: center; padding: 20px;">
                    <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <p style="margin-top: 10px;">正在验证访问令牌...</p>
                </div>
                <div id="success-message" style="display: none; text-align: center; color: #28a745;">
                    <p>✅ 验证成功！正在跳转到定时任务管理页面...</p>
                </div>
            </div>
        </div>

        <div class="main-content" id="mainContent">
            <div class="tabs">
                <div class="tab-header">
                    <button class="tab-btn active" onclick="switchTab('jobs')">任务管理</button>
                    <button class="tab-btn" onclick="switchTab('logs')">执行日志</button>
                    <button class="tab-btn" onclick="switchTab('statistics')">统计分析</button>
                    <button class="tab-btn" onclick="switchTab('scheduler')">调度器管理</button>
                </div>

                <!-- 任务管理标签页 -->
                <div class="tab-content active" id="jobs-tab">
                    <div class="alert" id="jobs-alert"></div>
                    
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <button class="btn btn-primary" onclick="showAddJobModal()">
                                <span>➕</span> 新增任务
                            </button>
                            <button class="btn btn-success" onclick="batchStartJobs()">
                                <span>▶️</span> 批量启动
                            </button>
                            <button class="btn btn-warning" onclick="batchPauseJobs()">
                                <span>⏸️</span> 批量暂停
                            </button>
                            <button class="btn btn-danger" onclick="batchDeleteJobs()">
                                <span>🗑️</span> 批量删除
                            </button>
                        </div>
                        <div class="toolbar-right">
                            <button class="btn btn-info" onclick="exportJobs()">
                                <span>📤</span> 导出配置
                            </button>
                            <button class="btn btn-secondary" onclick="showImportModal()">
                                <span>📥</span> 导入配置
                            </button>
                            <button class="btn btn-primary" onclick="refreshJobs()">
                                <span>🔄</span> 刷新
                            </button>
                        </div>
                    </div>
                    
                    <div class="search-form">
                        <div class="search-row">
                            <div class="search-group">
                                <label>任务名称</label>
                                <input type="text" id="jobName" placeholder="请输入任务名称">
                            </div>
                            <div class="search-group">
                                <label>任务组名</label>
                                <input type="text" id="jobGroup" placeholder="请输入任务组名">
                            </div>
                            <div class="search-group">
                                <label>任务状态</label>
                                <select id="jobStatus">
                                    <option value="">全部</option>
                                    <option value="0">正常</option>
                                    <option value="1">暂停</option>
                                </select>
                            </div>
                            <div class="search-group">
                                <label>任务类型</label>
                                <select id="jobType">
                                    <option value="">全部</option>
                                    <option value="1">数据清理</option>
                                    <option value="2">表优化</option>
                                    <option value="3">统计报告</option>
                                    <option value="4">健康检查</option>
                                    <option value="5">性能统计</option>
                                    <option value="6">告警检查</option>
                                    <option value="7">数据归档</option>
                                    <option value="8">索引优化</option>
                                    <option value="9">缓存清理</option>
                                </select>
                            </div>
                        </div>
                        <div class="search-actions">
                            <button class="btn btn-secondary" onclick="resetJobSearch()">重置</button>
                            <button class="btn btn-primary" onclick="searchJobs()">查询</button>
                        </div>
                    </div>

                    <div id="jobsContent">
                        <div class="loading">正在加载任务数据...</div>
                    </div>

                    <div class="pagination" id="jobsPagination" style="display: none;">
                        <!-- 分页控件将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 执行日志标签页 -->
                <div class="tab-content" id="logs-tab">
                    <div class="alert" id="logs-alert"></div>
                    <div id="logsContent">
                        <div class="loading">正在加载日志数据...</div>
                    </div>
                </div>

                <!-- 统计分析标签页 -->
                <div class="tab-content" id="statistics-tab">
                    <div class="alert" id="statistics-alert"></div>
                    <div id="statisticsContent">
                        <div class="loading">正在加载统计数据...</div>
                    </div>
                </div>

                <!-- 调度器管理标签页 -->
                <div class="tab-content" id="scheduler-tab">
                    <div class="alert" id="scheduler-alert"></div>
                    <div id="schedulerContent">
                        <div class="loading">正在加载调度器状态...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑任务模态框 -->
    <div id="jobModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="jobModalTitle">新增任务</h3>
                <span class="close" onclick="closeJobModal()">&times;</span>
            </div>
            <div class="alert" id="jobModal-alert"></div>
            <form id="jobForm">
                <input type="hidden" id="jobId" name="jobId">
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalJobName">任务名称 *</label>
                        <input type="text" id="modalJobName" name="jobName" required>
                    </div>
                    <div class="form-group">
                        <label for="modalJobGroup">任务组名 *</label>
                        <input type="text" id="modalJobGroup" name="jobGroup" value="DEFAULT" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalJobType">任务类型 *</label>
                        <select id="modalJobType" name="jobType" required>
                            <option value="">请选择任务类型</option>
                            <option value="1">数据清理</option>
                            <option value="2">表优化</option>
                            <option value="3">统计报告</option>
                            <option value="4">健康检查</option>
                            <option value="5">性能统计</option>
                            <option value="6">告警检查</option>
                            <option value="7">数据归档</option>
                            <option value="8">索引优化</option>
                            <option value="9">缓存清理</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="modalInvokeTarget">调用目标 *</label>
                        <input type="text" id="modalInvokeTarget" name="invokeTarget" placeholder="如: systemRequestRecordTask.cleanupExpiredRecords('30')" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="modalCronExpression">Cron表达式 *</label>
                    <input type="text" id="modalCronExpression" name="cronExpression" placeholder="如: 0 0 2 * * ?" required>
                    <small>格式: 秒 分 时 日 月 周 年(可选)</small>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="modalMisfirePolicy">错误策略</label>
                        <select id="modalMisfirePolicy" name="misfirePolicy">
                            <option value="1">立即执行</option>
                            <option value="2">执行一次</option>
                            <option value="3">放弃执行</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="modalConcurrent">并发执行</label>
                        <select id="modalConcurrent" name="concurrent">
                            <option value="0">允许</option>
                            <option value="1">禁止</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="modalDescription">任务描述</label>
                    <textarea id="modalDescription" name="description" placeholder="请输入任务描述"></textarea>
                </div>
                <div class="form-group">
                    <label for="modalRemark">备注信息</label>
                    <textarea id="modalRemark" name="remark" placeholder="请输入备注信息"></textarea>
                </div>
                <div class="form-row">
                    <button type="button" class="btn btn-secondary" onclick="closeJobModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 导入配置模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>导入任务配置</h3>
                <span class="close" onclick="closeImportModal()">&times;</span>
            </div>
            <div class="alert" id="importModal-alert"></div>
            <div class="form-group">
                <label for="configJson">配置JSON</label>
                <textarea id="configJson" placeholder="请粘贴任务配置JSON" style="height: 300px;"></textarea>
            </div>
            <div class="form-row">
                <button type="button" class="btn btn-secondary" onclick="closeImportModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="importConfig()">导入</button>
            </div>
        </div>
    </div>

    <script src="quartz-management.js"></script>
</body>
</html>
