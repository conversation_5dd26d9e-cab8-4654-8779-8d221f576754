/**
 * 定时任务管理 JavaScript
 * 提供完整的定时任务管理功能
 */

// 全局变量
let currentPage = 1;
let currentPageSize = 20;
let selectedJobIds = [];
let accessToken = '';
let isAuthenticated = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('定时任务管理页面初始化...');
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 检查是否已经认证
    const savedToken = sessionStorage.getItem('quartzToken');
    const tokenExpire = sessionStorage.getItem('quartzTokenExpire');

    if (savedToken && tokenExpire && Date.now() < parseInt(tokenExpire)) {
        accessToken = savedToken;
        isAuthenticated = true;
        showMainContent();
        loadJobs();
    }

    // 绑定回车键事件
    document.getElementById('secretKey').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            verifySecretKey();
        }
    });

    const codeInput = document.getElementById('code');
    const refreshCodeInput = document.getElementById('refreshCode');

    if (codeInput) {
        codeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }

    if (refreshCodeInput) {
        refreshCodeInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                getAccessToken();
            }
        });
    }

    // 绑定表单提交事件
    const jobForm = document.getElementById('jobForm');
    if (jobForm) {
        jobForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveJob();
        });
    }
}

/**
 * 获取API基础URL
 */
function getApiBaseUrl() {
    if (window.SERVER_CONFIG && window.SERVER_CONFIG.requestUrl) {
        return window.SERVER_CONFIG.requestUrl + '/api';
    }
    const protocol = window.location.protocol;
    const host = window.location.host;
    return `${protocol}//${host}/api`;
}

/**
 * 显示提示信息
 */
function showAlert(containerId, message, type = 'danger') {
    const alertContainer = document.getElementById(containerId);
    if (alertContainer) {
        alertContainer.className = `alert ${type}`;
        alertContainer.textContent = message;
        alertContainer.style.display = 'block';

        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                alertContainer.style.display = 'none';
            }, 3000);
        }
    }
}

/**
 * 验证秘钥
 */
async function verifySecretKey() {
    const secretKey = document.getElementById('secretKey').value.trim();

    if (!secretKey) {
        showAlert('secretAlert', '请输入秘钥');
        return;
    }

    try {
        const response = await fetch(`${getApiBaseUrl()}/api/quartz/job/auth/verify-secret`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ secretKey })
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('secretAlert', '秘钥验证成功，请获取访问令牌', 'success');
            document.getElementById('secretKeyForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        } else {
            showAlert('secretAlert', result.message || '秘钥验证失败');
        }
    } catch (error) {
        console.error('秘钥验证失败:', error);
        showAlert('secretAlert', '网络错误，请稍后重试');
    }
}

/**
 * 获取访问令牌
 */
async function getAccessToken() {
    const code = document.getElementById('code').value.trim();
    const refreshCode = document.getElementById('refreshCode').value.trim();

    if (!code || !refreshCode) {
        showAlert('tokenAlert', '请输入验证码和刷新码');
        return;
    }

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/getAccessToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: code,
                refreshCode: refreshCode
            })
        });

        const result = await response.json();

        if (result.code === 200 && result.data && result.data.accessToken) {
            accessToken = result.data.accessToken;
            console.log('AccessToken获取成功:', accessToken);

            // 保存token到sessionStorage
            sessionStorage.setItem('quartzToken', accessToken);
            if (result.data.expiresIn) {
                const expireTime = Date.now() + (result.data.expiresIn * 1000);
                sessionStorage.setItem('quartzTokenExpire', expireTime);
            }

            showAlert('tokenAlert', '访问令牌获取成功！', 'success');

            // 显示第三步验证界面
            document.getElementById('tokenForm').style.display = 'none';
            document.getElementById('validateForm').style.display = 'block';

            // 自动验证token
            setTimeout(validateAccessToken, 1000);
        } else {
            showAlert('tokenAlert', result.message || '获取访问令牌失败');
        }
    } catch (error) {
        console.error('获取访问令牌失败:', error);
        showAlert('tokenAlert', '网络错误，请稍后重试');
    }
}

/**
 * 验证访问令牌
 */
async function validateAccessToken() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('success-message').style.display = 'none';

    try {
        const response = await fetch(`${getApiBaseUrl()}/secure/token/validate?accessToken=${encodeURIComponent(accessToken)}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        document.getElementById('loading').style.display = 'none';

        if (result.code === 200 && result.data && result.data.valid) {
            console.log('Token验证成功，剩余时间:', result.data.remainingTime, '秒');

            // 显示成功消息
            document.getElementById('success-message').style.display = 'block';

            isAuthenticated = true;

            // 延迟跳转到管理页面
            setTimeout(() => {
                showMainContent();
                loadJobs();
            }, 2000);
        } else {
            showAlert('tokenAlert', result.message || 'Token验证失败');
            // 重新显示token获取表单
            document.getElementById('validateForm').style.display = 'none';
            document.getElementById('tokenForm').style.display = 'block';
        }
    } catch (error) {
        console.error('Token验证失败:', error);
        document.getElementById('loading').style.display = 'none';
        showAlert('tokenAlert', '网络错误，请稍后重试');
        // 重新显示token获取表单
        document.getElementById('validateForm').style.display = 'none';
        document.getElementById('tokenForm').style.display = 'block';
    }
}

/**
 * 显示主要内容
 */
function showMainContent() {
    document.getElementById('authSection').style.display = 'none';
    document.getElementById('mainContent').style.display = 'block';
}

/**
 * 切换标签页
 */
function switchTab(tabName) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // 激活当前标签
    event.target.classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // 根据标签页加载对应数据
    switch(tabName) {
        case 'jobs':
            loadJobs();
            break;
        case 'logs':
            loadJobLogs();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'scheduler':
            loadSchedulerStatus();
            break;
    }
}

/**
 * 加载定时任务数据
 */
async function loadJobs() {
    try {
        showLoading('jobsContent');
        
        const params = {
            pageNum: currentPage,
            pageSize: currentPageSize,
            jobName: document.getElementById('jobName').value.trim(),
            jobGroup: document.getElementById('jobGroup').value.trim(),
            status: document.getElementById('jobStatus').value,
            jobType: document.getElementById('jobType').value
        };
        
        // 移除空值参数
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key];
            }
        });
        
        const url = `${getApiBaseUrl()}/api/quartz/job/list`;
        const response = await makeApiRequest(url, {
            method: 'GET',
            data: params
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            displayJobs(result.data);
            showAlert('jobs-alert', '数据加载成功', 'success');
        } else {
            showAlert('jobs-alert', result.message || '加载数据失败');
        }
    } catch (error) {
        console.error('加载定时任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示加载状态
 */
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '<div class="loading">正在加载数据...</div>';
    }
}

/**
 * 发起API请求
 */
async function makeApiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        }
    };

    // 添加AccessToken头
    if (accessToken) {
        defaultOptions.headers['X-Access-Token'] = accessToken;
    }

    if (options.method === 'GET' && options.data) {
        const params = new URLSearchParams(options.data);
        url += '?' + params.toString();
        delete options.data;
    } else if (options.data) {
        defaultOptions.body = JSON.stringify(options.data);
    }

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    return fetch(url, finalOptions);
}

/**
 * 显示定时任务数据
 */
function displayJobs(data) {
    const container = document.getElementById('jobsContent');
    const pagination = document.getElementById('jobsPagination');
    
    if (!data || !data.records || data.records.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无数据</div>';
        pagination.style.display = 'none';
        return;
    }
    
    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th><input type="checkbox" onchange="toggleSelectAll(this)"></th>
                    <th>任务名称</th>
                    <th>任务组名</th>
                    <th>任务类型</th>
                    <th>调用目标</th>
                    <th>Cron表达式</th>
                    <th>状态</th>
                    <th>下次执行时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    data.records.forEach(job => {
        const statusClass = job.status === '0' ? 'status-normal' : 'status-paused';
        const statusText = job.status === '0' ? '正常' : '暂停';
        const nextValidTime = job.nextValidTime ? new Date(job.nextValidTime).toLocaleString() : '-';
        
        html += `
            <tr>
                <td><input type="checkbox" value="${job.jobId}" onchange="toggleSelectJob(this)"></td>
                <td>${job.jobName || '-'}</td>
                <td>${job.jobGroup || '-'}</td>
                <td>${job.jobTypeName || '-'}</td>
                <td title="${job.invokeTarget || '-'}">${truncateText(job.invokeTarget || '-', 30)}</td>
                <td>${job.cronExpression || '-'}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${nextValidTime}</td>
                <td>
                    <button class="btn btn-info" onclick="viewJobDetail('${job.jobId}')">详情</button>
                    <button class="btn btn-warning" onclick="editJob('${job.jobId}')">编辑</button>
                    ${job.status === '0' ? 
                        `<button class="btn btn-warning" onclick="pauseJob('${job.jobId}')">暂停</button>` :
                        `<button class="btn btn-success" onclick="resumeJob('${job.jobId}')">恢复</button>`
                    }
                    <button class="btn btn-primary" onclick="runJobOnce('${job.jobId}')">执行</button>
                    <button class="btn btn-danger" onclick="deleteJob('${job.jobId}')">删除</button>
                </td>
            </tr>
        `;
    });
    
    html += `
            </tbody>
        </table>
    `;
    
    container.innerHTML = html;
    
    // 显示分页控件
    if (data.totalPages > 1) {
        displayPagination(data);
        pagination.style.display = 'flex';
    } else {
        pagination.style.display = 'none';
    }
}

/**
 * 截断文本
 */
function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

/**
 * 显示分页控件
 */
function displayPagination(data) {
    const pagination = document.getElementById('jobsPagination');
    let html = '';
    
    // 上一页
    if (data.current > 1) {
        html += `<button onclick="changePage(${data.current - 1})">上一页</button>`;
    }
    
    // 页码
    const startPage = Math.max(1, data.current - 2);
    const endPage = Math.min(data.totalPages, data.current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === data.current ? 'active' : '';
        html += `<button class="${activeClass}" onclick="changePage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (data.current < data.totalPages) {
        html += `<button onclick="changePage(${data.current + 1})">下一页</button>`;
    }
    
    // 页面信息
    html += `<span style="margin-left: 20px;">第 ${data.current} 页，共 ${data.totalPages} 页，总计 ${data.total} 条</span>`;
    
    pagination.innerHTML = html;
}

/**
 * 切换页面
 */
function changePage(page) {
    currentPage = page;
    loadJobs();
}

/**
 * 全选/取消全选
 */
function toggleSelectAll(checkbox) {
    const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
    checkboxes.forEach(cb => {
        cb.checked = checkbox.checked;
        toggleSelectJob(cb);
    });
}

/**
 * 选择/取消选择任务
 */
function toggleSelectJob(checkbox) {
    const jobId = parseInt(checkbox.value);
    if (checkbox.checked) {
        if (!selectedJobIds.includes(jobId)) {
            selectedJobIds.push(jobId);
        }
    } else {
        const index = selectedJobIds.indexOf(jobId);
        if (index > -1) {
            selectedJobIds.splice(index, 1);
        }
    }
}

/**
 * 搜索任务
 */
function searchJobs() {
    currentPage = 1;
    loadJobs();
}

/**
 * 重置搜索
 */
function resetJobSearch() {
    document.getElementById('jobName').value = '';
    document.getElementById('jobGroup').value = '';
    document.getElementById('jobStatus').value = '';
    document.getElementById('jobType').value = '';
    
    currentPage = 1;
    loadJobs();
}

/**
 * 刷新任务列表
 */
function refreshJobs() {
    loadJobs();
}

/**
 * 显示新增任务模态框
 */
function showAddJobModal() {
    document.getElementById('jobModalTitle').textContent = '新增任务';
    document.getElementById('jobForm').reset();
    document.getElementById('jobId').value = '';
    document.getElementById('jobModal').style.display = 'block';
}

/**
 * 编辑任务
 */
async function editJob(jobId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/${jobId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            const job = result.data;
            document.getElementById('jobModalTitle').textContent = '编辑任务';
            document.getElementById('jobId').value = job.jobId;
            document.getElementById('modalJobName').value = job.jobName || '';
            document.getElementById('modalJobGroup').value = job.jobGroup || '';
            document.getElementById('modalJobType').value = job.jobType || '';
            document.getElementById('modalInvokeTarget').value = job.invokeTarget || '';
            document.getElementById('modalCronExpression').value = job.cronExpression || '';
            document.getElementById('modalMisfirePolicy').value = job.misfirePolicy || '1';
            document.getElementById('modalConcurrent').value = job.concurrent || '0';
            document.getElementById('modalDescription').value = job.description || '';
            document.getElementById('modalRemark').value = job.remark || '';
            
            document.getElementById('jobModal').style.display = 'block';
        } else {
            showAlert('jobs-alert', result.message || '获取任务信息失败');
        }
    } catch (error) {
        console.error('获取任务信息失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 保存任务
 */
async function saveJob() {
    try {
        const formData = new FormData(document.getElementById('jobForm'));
        const jobData = {};
        
        for (let [key, value] of formData.entries()) {
            jobData[key] = value;
        }
        
        // 验证必填字段
        if (!jobData.jobName || !jobData.jobGroup || !jobData.invokeTarget || !jobData.cronExpression) {
            showAlert('jobModal-alert', '请填写所有必填字段');
            return;
        }
        
        const isEdit = jobData.jobId && jobData.jobId !== '';
        const url = `${getApiBaseUrl()}/api/quartz/job`;
        const method = isEdit ? 'PUT' : 'POST';
        
        const response = await makeApiRequest(url, {
            method: method,
            data: jobData
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('jobModal-alert', isEdit ? '修改成功' : '新增成功', 'success');
            setTimeout(() => {
                closeJobModal();
                loadJobs();
            }, 1000);
        } else {
            showAlert('jobModal-alert', result.message || (isEdit ? '修改失败' : '新增失败'));
        }
    } catch (error) {
        console.error('保存任务失败:', error);
        showAlert('jobModal-alert', '网络错误，请稍后重试');
    }
}

/**
 * 关闭任务模态框
 */
function closeJobModal() {
    document.getElementById('jobModal').style.display = 'none';
    document.getElementById('jobModal-alert').style.display = 'none';
}

/**
 * 暂停任务
 */
async function pauseJob(jobId) {
    if (!confirm('确定要暂停这个任务吗？')) {
        return;
    }
    
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/changeStatus`, {
            method: 'PUT',
            data: { jobId: jobId, status: '1' }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('jobs-alert', '任务暂停成功', 'success');
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '任务暂停失败');
        }
    } catch (error) {
        console.error('暂停任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 恢复任务
 */
async function resumeJob(jobId) {
    if (!confirm('确定要恢复这个任务吗？')) {
        return;
    }
    
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/changeStatus`, {
            method: 'PUT',
            data: { jobId: jobId, status: '0' }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('jobs-alert', '任务恢复成功', 'success');
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '任务恢复失败');
        }
    } catch (error) {
        console.error('恢复任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 立即执行任务
 */
async function runJobOnce(jobId) {
    if (!confirm('确定要立即执行这个任务吗？')) {
        return;
    }
    
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/run`, {
            method: 'PUT',
            data: { jobId: jobId }
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            showAlert('jobs-alert', '任务执行成功', 'success');
        } else {
            showAlert('jobs-alert', result.message || '任务执行失败');
        }
    } catch (error) {
        console.error('执行任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 删除任务
 */
async function deleteJob(jobId) {
    if (!confirm('确定要删除这个任务吗？删除后无法恢复！')) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/${jobId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('jobs-alert', '任务删除成功', 'success');
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '任务删除失败');
        }
    } catch (error) {
        console.error('删除任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 批量启动任务
 */
async function batchStartJobs() {
    if (selectedJobIds.length === 0) {
        showAlert('jobs-alert', '请选择要启动的任务');
        return;
    }

    if (!confirm(`确定要启动选中的 ${selectedJobIds.length} 个任务吗？`)) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/batchStart`, {
            method: 'PUT',
            data: { jobIds: selectedJobIds }
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('jobs-alert', '批量启动成功', 'success');
            selectedJobIds = [];
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '批量启动失败');
        }
    } catch (error) {
        console.error('批量启动任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 批量暂停任务
 */
async function batchPauseJobs() {
    if (selectedJobIds.length === 0) {
        showAlert('jobs-alert', '请选择要暂停的任务');
        return;
    }

    if (!confirm(`确定要暂停选中的 ${selectedJobIds.length} 个任务吗？`)) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/batchPause`, {
            method: 'PUT',
            data: { jobIds: selectedJobIds }
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('jobs-alert', '批量暂停成功', 'success');
            selectedJobIds = [];
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '批量暂停失败');
        }
    } catch (error) {
        console.error('批量暂停任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 批量删除任务
 */
async function batchDeleteJobs() {
    if (selectedJobIds.length === 0) {
        showAlert('jobs-alert', '请选择要删除的任务');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedJobIds.length} 个任务吗？删除后无法恢复！`)) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/${selectedJobIds.join(',')}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('jobs-alert', '批量删除成功', 'success');
            selectedJobIds = [];
            loadJobs();
        } else {
            showAlert('jobs-alert', result.message || '批量删除失败');
        }
    } catch (error) {
        console.error('批量删除任务失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 导出任务配置
 */
async function exportJobs() {
    if (selectedJobIds.length === 0) {
        showAlert('jobs-alert', '请选择要导出的任务');
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/export`, {
            method: 'POST',
            data: { jobIds: selectedJobIds }
        });

        const result = await response.json();

        if (result.code === 200) {
            // 下载配置文件
            const blob = new Blob([result.data], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `job_config_${new Date().getTime()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showAlert('jobs-alert', '导出成功', 'success');
        } else {
            showAlert('jobs-alert', result.message || '导出失败');
        }
    } catch (error) {
        console.error('导出任务配置失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示导入模态框
 */
function showImportModal() {
    document.getElementById('configJson').value = '';
    document.getElementById('importModal').style.display = 'block';
}

/**
 * 关闭导入模态框
 */
function closeImportModal() {
    document.getElementById('importModal').style.display = 'none';
    document.getElementById('importModal-alert').style.display = 'none';
}

/**
 * 导入配置
 */
async function importConfig() {
    const configJson = document.getElementById('configJson').value.trim();

    if (!configJson) {
        showAlert('importModal-alert', '请输入配置JSON');
        return;
    }

    try {
        // 验证JSON格式
        JSON.parse(configJson);

        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/import`, {
            method: 'POST',
            data: { configJson: configJson }
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('importModal-alert', '导入成功', 'success');
            setTimeout(() => {
                closeImportModal();
                loadJobs();
            }, 1000);
        } else {
            showAlert('importModal-alert', result.message || '导入失败');
        }
    } catch (error) {
        if (error instanceof SyntaxError) {
            showAlert('importModal-alert', 'JSON格式错误，请检查配置内容');
        } else {
            console.error('导入配置失败:', error);
            showAlert('importModal-alert', '网络错误，请稍后重试');
        }
    }
}

/**
 * 查看任务详情
 */
async function viewJobDetail(jobId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/${jobId}`);
        const result = await response.json();

        if (result.code === 200) {
            const job = result.data;
            alert(`任务详情：\n\n任务名称：${job.jobName}\n任务组名：${job.jobGroup}\n任务类型：${job.jobTypeName}\n调用目标：${job.invokeTarget}\nCron表达式：${job.cronExpression}\n状态：${job.statusName}\n描述：${job.description || '无'}\n备注：${job.remark || '无'}`);
        } else {
            showAlert('jobs-alert', result.message || '获取任务详情失败');
        }
    } catch (error) {
        console.error('获取任务详情失败:', error);
        showAlert('jobs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 加载执行日志
 */
async function loadJobLogs() {
    try {
        showLoading('logsContent');

        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/jobLog/list`);
        const result = await response.json();

        if (result.code === 200) {
            displayJobLogs(result.data);
        } else {
            showAlert('logs-alert', result.message || '加载日志失败');
        }
    } catch (error) {
        console.error('加载执行日志失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示执行日志
 */
function displayJobLogs(data) {
    const container = document.getElementById('logsContent');

    if (!data || !data.records || data.records.length === 0) {
        container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无日志数据</div>';
        return;
    }

    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>任务名称</th>
                    <th>任务组名</th>
                    <th>调用目标</th>
                    <th>执行状态</th>
                    <th>执行时间</th>
                    <th>开始时间</th>
                    <th>结束时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.records.forEach(log => {
        const statusClass = log.status === '0' ? 'status-normal' : 'status-error';
        const statusText = log.status === '0' ? '成功' : '失败';
        const startTime = log.startTime ? new Date(log.startTime).toLocaleString() : '-';
        const endTime = log.endTime ? new Date(log.endTime).toLocaleString() : '-';
        const executeTime = log.executeTime ? log.executeTime + 'ms' : '-';

        html += `
            <tr>
                <td>${log.jobName || '-'}</td>
                <td>${log.jobGroup || '-'}</td>
                <td title="${log.invokeTarget || '-'}">${truncateText(log.invokeTarget || '-', 30)}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${executeTime}</td>
                <td>${startTime}</td>
                <td>${endTime}</td>
                <td>
                    <button class="btn btn-info" onclick="viewLogDetail('${log.logId}')">详情</button>
                </td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
    `;

    container.innerHTML = html;
}

/**
 * 查看日志详情
 */
async function viewLogDetail(logId) {
    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/jobLog/${logId}`);
        const result = await response.json();

        if (result.code === 200) {
            const log = result.data;
            const details = `日志详情：\n\n任务名称：${log.jobName}\n任务组名：${log.jobGroup}\n调用目标：${log.invokeTarget}\n执行状态：${log.statusName}\n执行时间：${log.executeTimeDesc}\n开始时间：${log.startTime ? new Date(log.startTime).toLocaleString() : '-'}\n结束时间：${log.endTime ? new Date(log.endTime).toLocaleString() : '-'}\n日志信息：${log.jobMessage || '无'}\n异常信息：${log.exceptionInfo || '无'}`;
            alert(details);
        } else {
            showAlert('logs-alert', result.message || '获取日志详情失败');
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        showAlert('logs-alert', '网络错误，请稍后重试');
    }
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        showLoading('statisticsContent');

        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/statistics`);
        const result = await response.json();

        if (result.code === 200) {
            displayStatistics(result.data);
        } else {
            showAlert('statistics-alert', result.message || '加载统计数据失败');
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showAlert('statistics-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示统计数据
 */
function displayStatistics(data) {
    const container = document.getElementById('statisticsContent');

    const html = `
        <div class="stats-grid">
            <div class="stats-card">
                <h3>${data.totalJobs || 0}</h3>
                <p>总任务数</p>
            </div>
            <div class="stats-card">
                <h3>${data.runningJobs || 0}</h3>
                <p>运行中任务</p>
            </div>
            <div class="stats-card">
                <h3>${data.pausedJobs || 0}</h3>
                <p>暂停任务</p>
            </div>
            <div class="stats-card">
                <h3>${data.todayExecutions || 0}</h3>
                <p>今日执行次数</p>
            </div>
            <div class="stats-card">
                <h3>${data.todaySuccessRate || '0%'}</h3>
                <p>今日成功率</p>
            </div>
            <div class="stats-card">
                <h3>${data.avgExecuteTime || 0}ms</h3>
                <p>平均执行时间</p>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>任务执行趋势</h3>
            <div id="trendChart" style="height: 300px; background: #f8f9fa; border-radius: 10px; display: flex; align-items: center; justify-content: center; color: #666;">
                趋势图表功能开发中...
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>性能排行</h3>
            <div id="performanceRanking" style="background: #f8f9fa; border-radius: 10px; padding: 20px;">
                <p style="color: #666; text-align: center;">性能排行功能开发中...</p>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * 加载调度器状态
 */
async function loadSchedulerStatus() {
    try {
        showLoading('schedulerContent');

        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/scheduler/status`);
        const result = await response.json();

        if (result.code === 200) {
            displaySchedulerStatus(result.data);
        } else {
            showAlert('scheduler-alert', result.message || '加载调度器状态失败');
        }
    } catch (error) {
        console.error('加载调度器状态失败:', error);
        showAlert('scheduler-alert', '网络错误，请稍后重试');
    }
}

/**
 * 显示调度器状态
 */
function displaySchedulerStatus(data) {
    const container = document.getElementById('schedulerContent');

    const isStarted = data.started || false;
    const statusClass = isStarted ? 'status-normal' : 'status-error';
    const statusText = isStarted ? '运行中' : '已停止';

    const html = `
        <div class="stats-grid">
            <div class="stats-card">
                <h3 class="${statusClass}">${statusText}</h3>
                <p>调度器状态</p>
            </div>
            <div class="stats-card">
                <h3>${data.schedulerName || '-'}</h3>
                <p>调度器名称</p>
            </div>
            <div class="stats-card">
                <h3>${data.schedulerInstanceId || '-'}</h3>
                <p>实例ID</p>
            </div>
            <div class="stats-card">
                <h3>${data.version || '-'}</h3>
                <p>版本信息</p>
            </div>
            <div class="stats-card">
                <h3>${data.runningSince ? new Date(data.runningSince).toLocaleString() : '-'}</h3>
                <p>启动时间</p>
            </div>
            <div class="stats-card">
                <h3>${data.numberOfJobsExecuted || 0}</h3>
                <p>已执行任务数</p>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <div class="toolbar">
                <div class="toolbar-left">
                    <h3>调度器控制</h3>
                </div>
                <div class="toolbar-right">
                    ${isStarted ?
                        `<button class="btn btn-warning" onclick="pauseScheduler()">暂停调度器</button>
                         <button class="btn btn-danger" onclick="shutdownScheduler()">关闭调度器</button>` :
                        `<button class="btn btn-success" onclick="startScheduler()">启动调度器</button>`
                    }
                    <button class="btn btn-primary" onclick="loadSchedulerStatus()">刷新状态</button>
                </div>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <h4>调度器详细信息</h4>
            <div style="background: #f8f9fa; border-radius: 10px; padding: 20px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                    <div>
                        <strong>调度器类型:</strong> ${data.schedulerClass || '-'}
                    </div>
                    <div>
                        <strong>线程池大小:</strong> ${data.threadPoolSize || '-'}
                    </div>
                    <div>
                        <strong>是否集群模式:</strong> ${data.clustered ? '是' : '否'}
                    </div>
                    <div>
                        <strong>是否远程调度:</strong> ${data.remote ? '是' : '否'}
                    </div>
                    <div>
                        <strong>作业存储类型:</strong> ${data.jobStoreClass || '-'}
                    </div>
                    <div>
                        <strong>是否持久化:</strong> ${data.persistent ? '是' : '否'}
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * 启动调度器
 */
async function startScheduler() {
    if (!confirm('确定要启动调度器吗？')) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/scheduler/start`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('scheduler-alert', '调度器启动成功', 'success');
            loadSchedulerStatus();
        } else {
            showAlert('scheduler-alert', result.message || '调度器启动失败');
        }
    } catch (error) {
        console.error('启动调度器失败:', error);
        showAlert('scheduler-alert', '网络错误，请稍后重试');
    }
}

/**
 * 关闭调度器
 */
async function shutdownScheduler() {
    if (!confirm('确定要关闭调度器吗？这将停止所有定时任务的执行！')) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/scheduler/shutdown`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('scheduler-alert', '调度器关闭成功', 'success');
            loadSchedulerStatus();
        } else {
            showAlert('scheduler-alert', result.message || '调度器关闭失败');
        }
    } catch (error) {
        console.error('关闭调度器失败:', error);
        showAlert('scheduler-alert', '网络错误，请稍后重试');
    }
}

/**
 * 暂停调度器
 */
async function pauseScheduler() {
    if (!confirm('确定要暂停调度器吗？这将暂停所有定时任务的执行！')) {
        return;
    }

    try {
        const response = await makeApiRequest(`${getApiBaseUrl()}/api/quartz/job/scheduler/pause`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.code === 200) {
            showAlert('scheduler-alert', '调度器暂停成功', 'success');
            loadSchedulerStatus();
        } else {
            showAlert('scheduler-alert', result.message || '调度器暂停失败');
        }
    } catch (error) {
        console.error('暂停调度器失败:', error);
        showAlert('scheduler-alert', '网络错误，请稍后重试');
    }
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const jobModal = document.getElementById('jobModal');
    const importModal = document.getElementById('importModal');

    if (event.target === jobModal) {
        closeJobModal();
    }

    if (event.target === importModal) {
        closeImportModal();
    }
}
