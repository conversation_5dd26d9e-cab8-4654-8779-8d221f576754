package com.haoys.user.controller;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.util.SecureTokenUtil;
import com.haoys.user.websocket.SpringLogWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志管理控制器
 *
 * <p>提供日志查看、下载、管理等功能的REST API，集成Spring WebSocket</p>
 * <p>支持实时日志流、文件下载、WebSocket连接管理等功能</p>
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-29
 */
@Slf4j
@RestController
@RequestMapping("/log-management")
@CrossOrigin(origins = "*")
public class LogManagementController {

    /**
     * 服务器地址配置（支持环境特定配置）
     */
    @Value("${server.address:localhost}")
    private String serverAddress;

    /**
     * WebSocket服务器地址配置（用于远程部署）
     */
    @Value("${websocket.server.address:${server.address:localhost}}")
    private String websocketServerAddress;

    /**
     * 服务器端口配置
     */
    @Value("${server.port}")
    private String serverPort;

    /**
     * 应用上下文路径
     */
    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 日志文件路径配置
     */
    @Value("${logging.file.name}")
    private String logFilePath;

    /**
     * 日志文件滚动路径配置
     */
    @Value("${logging.pattern.rolling-file-name}")
    private String rollingLogPath;

    /**
     * 日志查看器访问令牌
     */
    @Value("${log.viewer.access-token:edc-log-viewer-2025}")
    private String logViewerAccessToken;

    /**
     * 日志查看器令牌有效期（分钟）
     */
    @Value("${log.viewer.token-expire-minutes:60}")
    private int tokenExpireMinutes;

    @Autowired
    private SecureTokenUtil secureTokenUtil;

    /**
     * 获取日志查看器页面（支持accessToken访问）
     */
    @GetMapping("/viewer")
    public CommonResult<Map<String, Object>> getLogViewer(
            @RequestParam(required = false) String accessToken,
            @RequestParam(required = false) String redirect,
            HttpServletRequest request) {
        try {
            // 验证accessToken
            if (!isValidAccessToken(accessToken)) {
                return CommonResult.failed("访问被拒绝：无效的访问令牌");
            }

            Map<String, Object> data = new HashMap<>();
            data.put("message", "日志查看器访问授权成功");
            data.put("websocketUrl", buildWebSocketUrl(request));
            data.put("logViewerPage", "/log-management/log-viewer.html");
            data.put("logFilePath", logFilePath);
            data.put("serverInfo", buildServerInfo(request));
            data.put("accessGranted", true);

            // 获取token的实际过期时间
            String tokenExpireTimeStr = "--";
            try {
                Long remainingTime = secureTokenUtil.getRemainingTime(accessToken);
                if (remainingTime != null && remainingTime > 0) {
                    LocalDateTime expireTime = LocalDateTime.now().plusSeconds(remainingTime);
                    tokenExpireTimeStr = expireTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
            } catch (Exception e) {
                log.warn("获取token过期时间失败: {}", e.getMessage());
                // 使用默认过期时间
                LocalDateTime expireTime = LocalDateTime.now().plusMinutes(tokenExpireMinutes);
                tokenExpireTimeStr = expireTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            data.put("tokenExpireTime", tokenExpireTimeStr);

            // 如果请求重定向到日志查看器页面
            if ("page".equals(redirect)) {
                data.put("redirectUrl", "/log-management/log-viewer.html");
            }

            return CommonResult.success(data, "日志查看器访问授权成功");
        } catch (Exception e) {
            log.error("获取日志查看器信息失败", e);
            return CommonResult.failed("获取日志查看器信息失败: " + e.getMessage());
        }
    }

    /**
     * 日志登录页面
     */
    @GetMapping("/login")
    public void getLogLoginPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().write(generateLogLoginPage());
    }

    /**
     * 静态日志登录页面（直接访问）
     */
    @GetMapping("/log-login.html")
    public void getStaticLogLoginPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().write(generateStaticLogLoginPage());
    }

    /**
     * Token验证测试页面
     */
    @GetMapping("/test-token.html")
    public void getTokenTestPage(HttpServletResponse response) throws IOException {
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().write(generateTokenTestPage());
    }

    /**
     * 静态日志查看器页面（直接访问，需要token验证）
     */
    @GetMapping("/log-viewer.html")
    public void getStaticLogViewerPage(
            @RequestParam(required = false) String accessToken,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        // 验证accessToken
        if (!isValidAccessToken(accessToken)) {
            // Token无效，跳转到登录页面
            String loginUrl = request.getContextPath() + "/api/log-management/log-login.html";
            response.sendRedirect(loginUrl);
            return;
        }

        // Token有效，返回日志查看器页面
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().write(generateStaticLogViewerPage());
    }

    /**
     * 直接访问日志查看器页面（需要accessToken）
     */
    @GetMapping("/viewer/page")
    public void getLogViewerPage(
            @RequestParam(required = false) String accessToken,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {

        // 验证accessToken
        if (!isValidAccessToken(accessToken)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("text/html; charset=UTF-8");
            response.getWriter().write(generateAccessDeniedPage());
            return;
        }

        // 直接返回日志查看器页面内容
        response.setContentType("text/html; charset=UTF-8");
        response.getWriter().write(generateLogViewerPage(accessToken, request));
    }

    /**
     * 生成日志查看器页面内容
     */
    private String generateLogViewerPage(String accessToken, HttpServletRequest request) throws IOException {
        try {
            // 尝试从Spring Boot标准位置读取HTML文件内容
            String resourcePath = "/templates/log-management/log-viewer.html";
            try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
                if (inputStream != null) {
                    // Java 8兼容的方式读取InputStream
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    String content = new String(buffer.toByteArray(), "UTF-8");
                    log.info("成功从classpath读取HTML文件: {}, 长度: {}", resourcePath, content.length());

                    // 构建服务器配置JSON（使用动态获取的信息）
                    String wsUrl = buildWebSocketUrl(request);
                    Map<String, Object> serverInfo = buildServerInfo(request);

                    StringBuilder configJson = new StringBuilder();
                    configJson.append("{");
                    configJson.append("\"websocketUrl\":\"").append(wsUrl.replace("\"", "\\\"")).append("\",");
                    configJson.append("\"accessToken\":\"").append(accessToken.replace("\"", "\\\"")).append("\",");
                    configJson.append("\"logFilePath\":\"").append(logFilePath != null ? logFilePath.replace("\"", "\\\"") : "").append("\",");
                    configJson.append("\"serverAddress\":\"").append(serverInfo.get("address").toString().replace("\"", "\\\"")).append("\",");
                    configJson.append("\"serverPort\":\"").append(serverInfo.get("port").toString().replace("\"", "\\\"")).append("\",");
                    configJson.append("\"contextPath\":\"").append(serverInfo.get("contextPath").toString().replace("\"", "\\\"")).append("\",");
                    configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                    configJson.append("}");

                    // 注入配置到页面
                    String configScript = String.format(
                        "<script>window.SERVER_CONFIG = %s;</script>",
                        configJson.toString()
                    );

                    // 在</head>前插入配置脚本
                    content = content.replace("</head>", configScript + "\n</head>");
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/templates/log-management/log-viewer.html");
            log.info("尝试从文件系统读取HTML文件: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));

            if (Files.exists(htmlPath)) {
                String content = new String(readAllBytes(htmlPath), "UTF-8");
                log.info("成功从文件系统读取HTML文件，长度: {}", content.length());

                // 构建服务器配置JSON（使用动态获取的信息）
                String wsUrl = buildWebSocketUrl(request);
                Map<String, Object> serverInfo = buildServerInfo(request);

                StringBuilder configJson = new StringBuilder();
                configJson.append("{");
                configJson.append("\"websocketUrl\":\"").append(wsUrl.replace("\"", "\\\"")).append("\",");
                configJson.append("\"accessToken\":\"").append(accessToken.replace("\"", "\\\"")).append("\",");
                configJson.append("\"logFilePath\":\"").append(logFilePath != null ? logFilePath.replace("\"", "\\\"") : "").append("\",");
                configJson.append("\"serverAddress\":\"").append(serverInfo.get("address").toString().replace("\"", "\\\"")).append("\",");
                configJson.append("\"serverPort\":\"").append(serverInfo.get("port").toString().replace("\"", "\\\"")).append("\",");
                configJson.append("\"contextPath\":\"").append(serverInfo.get("contextPath").toString().replace("\"", "\\\"")).append("\",");
                configJson.append("\"requestUrl\":\"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"");
                configJson.append("}");

                // 注入配置到页面
                String configScript = String.format(
                    "<script>window.SERVER_CONFIG = %s;</script>",
                    configJson.toString()
                );

                // 在</head>前插入配置脚本
                content = content.replace("</head>", configScript + "\n</head>");
                return content;
            }
        } catch (Exception e) {
            log.error("读取日志查看器页面失败", e);
        }

        // 暂时强制使用简化版页面来测试配置注入
        log.info("使用简化版日志查看器页面");
        return generateSimpleLogViewerPage(accessToken, request);
    }

    /**
     * 生成日志登录页面内容
     */
    private String generateLogLoginPage() throws IOException {
        try {
            // 尝试从Spring Boot标准位置读取HTML文件内容
            String resourcePath = "/templates/log-management/log-login.html";
            try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
                if (inputStream != null) {
                    // Java 8兼容的方式读取InputStream
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    String content = new String(buffer.toByteArray(), "UTF-8");
                    log.info("成功从classpath读取登录页面: {}, 长度: {}", resourcePath, content.length());
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/templates/log-management/log-login.html");
            log.info("尝试从文件系统读取登录页面: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));

            if (Files.exists(htmlPath)) {
                String content = new String(readAllBytes(htmlPath), "UTF-8");
                log.info("成功从文件系统读取登录页面，长度: {}", content.length());
                return content;
            }
        } catch (Exception e) {
            log.error("读取日志登录页面失败", e);
        }

        // 如果都失败了，返回简化版登录页面
        log.warn("无法读取日志登录页面文件，使用简化版页面");
        return generateSimpleLogLoginPage();
    }

    /**
     * 生成静态日志查看器页面内容
     */
    private String generateStaticLogViewerPage() throws IOException {
        try {
            // 尝试从templates目录读取HTML文件内容
            String resourcePath = "/templates/log-management/log-viewer.html";
            try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
                if (inputStream != null) {
                    // Java 8兼容的方式读取InputStream
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    String content = new String(buffer.toByteArray(), "UTF-8");
                    log.info("成功从classpath读取日志查看器页面: {}, 长度: {}", resourcePath, content.length());
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/templates/log-management/log-viewer.html");
            log.info("尝试从文件系统读取日志查看器页面: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));

            if (Files.exists(htmlPath)) {
                String content = new String(readAllBytes(htmlPath), "UTF-8");
                log.info("成功从文件系统读取日志查看器页面，长度: {}", content.length());
                return content;
            }
        } catch (Exception e) {
            log.error("读取日志查看器页面失败", e);
        }

        // 如果都失败了，返回错误页面
        log.warn("无法读取日志查看器页面文件");
        return generateErrorPage("无法加载日志查看器页面");
    }

    /**
     * 生成静态日志登录页面内容
     */
    private String generateStaticLogLoginPage() throws IOException {
        try {
            // 尝试从templates目录读取HTML文件内容
            String resourcePath = "/templates/log-management/log-login.html";
            try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
                if (inputStream != null) {
                    // Java 8兼容的方式读取InputStream
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    String content = new String(buffer.toByteArray(), "UTF-8");
                    log.info("成功从classpath读取日志登录页面: {}, 长度: {}", resourcePath, content.length());
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/templates/log-management/log-login.html");
            log.info("尝试从文件系统读取日志登录页面: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));

            if (Files.exists(htmlPath)) {
                String content = new String(readAllBytes(htmlPath), "UTF-8");
                log.info("成功从文件系统读取日志登录页面，长度: {}", content.length());
                return content;
            }
        } catch (Exception e) {
            log.error("读取日志登录页面失败", e);
        }

        // 如果都失败了，返回简化版登录页面
        log.warn("无法读取日志登录页面文件，使用简化版页面");
        return generateSimpleLogLoginPage();
    }

    /**
     * 生成Token测试页面内容
     */
    private String generateTokenTestPage() throws IOException {
        try {
            // 尝试从templates目录读取HTML文件内容
            String resourcePath = "/templates/log-management/test-token.html";
            try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
                if (inputStream != null) {
                    // Java 8兼容的方式读取InputStream
                    ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                    int nRead;
                    byte[] data = new byte[1024];
                    while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                        buffer.write(data, 0, nRead);
                    }
                    String content = new String(buffer.toByteArray(), "UTF-8");
                    log.info("成功从classpath读取Token测试页面: {}, 长度: {}", resourcePath, content.length());
                    return content;
                }
            }

            // 回退：尝试从文件系统读取（兼容性）
            Path htmlPath = Paths.get("edc-research-center/edc-research-api/src/main/resources/templates/log-management/test-token.html");
            log.info("尝试从文件系统读取Token测试页面: {}, 存在: {}", htmlPath.toAbsolutePath(), Files.exists(htmlPath));

            if (Files.exists(htmlPath)) {
                String content = new String(readAllBytes(htmlPath), "UTF-8");
                log.info("成功从文件系统读取Token测试页面，长度: {}", content.length());
                return content;
            }
        } catch (Exception e) {
            log.error("读取Token测试页面失败", e);
        }

        // 如果都失败了，返回简化版页面
        log.warn("无法读取Token测试页面文件");
        return "<html><body><h1>Token测试页面加载失败</h1></body></html>";
    }

    /**
     * 获取WebSocket连接状态
     */
    @GetMapping("/status")
    public CommonResult<Map<String, Object>> getStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // WebSocket连接信息
            status.put("websocketConnections", SpringLogWebSocketHandler.getConnectionStatus());
            status.put("websocketType", "Spring WebSocket");
            status.put("websocketUrl", buildWebSocketUrl());

            // 系统信息
            status.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            status.put("serverTime", System.currentTimeMillis());
            status.put("logDirectory", getConfiguredLogDirectory());
            status.put("logFilePath", logFilePath);
            status.put("serverInfo", buildServerInfo());

            return CommonResult.success(status, "获取系统状态成功");
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return CommonResult.failed("获取系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 构建WebSocket URL（使用请求的域名和端口）
     */
    private String buildWebSocketUrl(HttpServletRequest request) {
        String protocol = "ws://";

        // 获取真实的客户端访问地址（处理Nginx代理）
        String host = getRealClientHost(request);
        int port = getRealClientPort(request);
        String requestContextPath = getRealContextPath(request);

        log.info("构建WebSocket URL: protocol={}, host={}, port={}, contextPath={}", protocol, host, port, requestContextPath);

        // 构建完整的WebSocket URL
        StringBuilder wsUrl = new StringBuilder();
        wsUrl.append(protocol).append(host);

        // 检查是否是代理环境并处理端口号
        boolean isProxyEnvironment = isProxyEnvironment(request, requestContextPath);

        // 特殊处理：如果检测到是8002这样的非标准代理端口，强制添加
        boolean shouldAddPort = shouldAddPortToUrl(port, isProxyEnvironment, request);

        if (shouldAddPort) {
            wsUrl.append(":").append(port);
        }

        wsUrl.append(requestContextPath).append("/websocket/logs/{clientId}");

        String finalUrl = wsUrl.toString();
        log.info("最终WebSocket URL: {}, 代理环境: {}", finalUrl, isProxyEnvironment);
        return finalUrl;
    }

    /**
     * 构建WebSocket URL（兼容旧方法）
     */
    private String buildWebSocketUrl() {
        String protocol = "ws://";
        // 优先使用WebSocket专用服务器地址，支持远程部署
        String host = websocketServerAddress;
        String port = serverPort;
        // WebSocket路径不需要额外的context path，因为已经在/api下

        log.debug("构建WebSocket URL: protocol={}, host={}, port={}", protocol, host, port);
        return String.format("%s%s:%s/api/websocket/logs/{clientId}",
                protocol, host, port);
    }

    /**
     * 构建服务器信息（使用请求的域名和端口）
     */
    private Map<String, Object> buildServerInfo(HttpServletRequest request) {
        Map<String, Object> serverInfo = new HashMap<>();

        // 获取真实的客户端访问地址（处理Nginx代理）
        String realHost = getRealClientHost(request);
        int realPort = getRealClientPort(request);
        String realContextPath = getRealContextPath(request);

        serverInfo.put("address", realHost);
        serverInfo.put("port", String.valueOf(realPort));
        serverInfo.put("contextPath", realContextPath);
        serverInfo.put("environment", getActiveProfile());

        // 构建正确的requestUrl
        String protocol = request.isSecure() ? "https" : "http";
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        if (forwardedProto != null && !forwardedProto.trim().isEmpty()) {
            protocol = forwardedProto.trim();
        }

        StringBuilder correctRequestUrl = new StringBuilder();
        correctRequestUrl.append(protocol).append("://").append(realHost);

        // 使用与WebSocket URL相同的端口号判断逻辑
        boolean isProxyEnv = isProxyEnvironment(request, realContextPath);
        boolean shouldAddPort = shouldAddPortToUrl(realPort, isProxyEnv, request);

        if (shouldAddPort) {
            correctRequestUrl.append(":").append(realPort);
        }

        correctRequestUrl.append(realContextPath).append(request.getServletPath());

        // 添加调试信息
        serverInfo.put("originalHost", request.getServerName());
        serverInfo.put("originalPort", String.valueOf(request.getServerPort()));
        serverInfo.put("originalContextPath", request.getContextPath() != null ? request.getContextPath() : "");
        serverInfo.put("requestUrl", correctRequestUrl.toString());
        serverInfo.put("originalRequestUrl", request.getRequestURL().toString());

        // 添加代理相关的调试信息
        serverInfo.put("isProxyEnvironment", isProxyEnv);
        serverInfo.put("shouldAddPort", shouldAddPort);
        serverInfo.put("detectedProtocol", protocol);
        serverInfo.put("hostHeader", request.getHeader("Host"));
        serverInfo.put("xForwardedHost", request.getHeader("X-Forwarded-Host"));
        serverInfo.put("xForwardedPort", request.getHeader("X-Forwarded-Port"));
        serverInfo.put("xForwardedProto", request.getHeader("X-Forwarded-Proto"));

        return serverInfo;
    }

    /**
     * 构建服务器信息（兼容旧方法）
     */
    private Map<String, Object> buildServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();
        serverInfo.put("address", serverAddress);
        serverInfo.put("port", serverPort);
        serverInfo.put("contextPath", contextPath);
        serverInfo.put("environment", getActiveProfile());
        return serverInfo;
    }

    /**
     * 获取真实的客户端访问主机（处理Nginx代理）
     */
    private String getRealClientHost(HttpServletRequest request) {
        String forwardedHost = request.getHeader("X-Forwarded-Host");
        String host = request.getHeader("Host");
        String serverName = request.getServerName();
        String xRealIp = request.getHeader("X-Real-IP");
        String xForwardedFor = request.getHeader("X-Forwarded-For");

        log.info("获取真实主机: X-Forwarded-Host={}, Host={}, ServerName={}, X-Real-IP={}, X-Forwarded-For={}",
                forwardedHost, host, serverName, xRealIp, xForwardedFor);

        // 优先使用Host头（包含完整的域名:端口信息）
        if (host != null && !host.trim().isEmpty()) {
            // Host头可能包含端口号，需要分离
            String[] hostParts = host.split(":");
            String realHost = hostParts[0].trim();
            log.info("使用Host头: {}", realHost);
            return realHost;
        }

        // 检查Nginx代理头
        if (forwardedHost != null && !forwardedHost.trim().isEmpty()) {
            // X-Forwarded-Host可能包含端口号，需要分离
            String[] hostParts = forwardedHost.split(":");
            String realHost = hostParts[0].trim();
            log.info("使用X-Forwarded-Host: {}", realHost);
            return realHost;
        }

        // 回退到原始方法
        log.info("使用ServerName: {}", serverName);
        return serverName;
    }

    /**
     * 获取真实的客户端访问端口（处理Nginx代理）
     */
    private int getRealClientPort(HttpServletRequest request) {
        String forwardedPort = request.getHeader("X-Forwarded-Port");
        String host = request.getHeader("Host");
        String forwardedHost = request.getHeader("X-Forwarded-Host");
        String forwardedProto = request.getHeader("X-Forwarded-Proto");
        int serverPort = request.getServerPort();

        log.info("获取真实端口: X-Forwarded-Port={}, Host={}, X-Forwarded-Host={}, X-Forwarded-Proto={}, ServerPort={}",
                forwardedPort, host, forwardedHost, forwardedProto, serverPort);

        // 优先使用Host头中的端口（最准确）
        if (host != null && host.contains(":")) {
            String[] hostParts = host.split(":");
            if (hostParts.length > 1) {
                try {
                    int port = Integer.parseInt(hostParts[1].trim());
                    log.info("使用Host头端口: {}", port);
                    return port;
                } catch (NumberFormatException e) {
                    log.warn("无法解析Host头中的端口: {}", host);
                }
            }
        }

        // 检查Nginx代理端口头
        if (forwardedPort != null && !forwardedPort.trim().isEmpty()) {
            try {
                int port = Integer.parseInt(forwardedPort.trim());
                log.info("使用X-Forwarded-Port: {}", port);
                return port;
            } catch (NumberFormatException e) {
                log.warn("无法解析X-Forwarded-Port: {}", forwardedPort);
            }
        }

        // 检查X-Forwarded-Host中的端口
        if (forwardedHost != null && forwardedHost.contains(":")) {
            String[] hostParts = forwardedHost.split(":");
            if (hostParts.length > 1) {
                try {
                    int port = Integer.parseInt(hostParts[1].trim());
                    log.info("使用X-Forwarded-Host端口: {}", port);
                    return port;
                } catch (NumberFormatException e) {
                    log.warn("无法解析X-Forwarded-Host中的端口: {}", forwardedHost);
                }
            }
        }

        // 根据协议推断默认端口
        if (forwardedProto != null) {
            if ("https".equalsIgnoreCase(forwardedProto.trim())) {
                log.info("使用HTTPS默认端口: 443");
                return 443;
            } else if ("http".equalsIgnoreCase(forwardedProto.trim())) {
                log.info("使用HTTP默认端口: 80");
                return 80;
            }
        }

        // 回退到原始方法
        log.info("使用ServerPort: {}", serverPort);
        return serverPort;
    }

    /**
     * 获取真实的上下文路径（处理Nginx代理）
     */
    private String getRealContextPath(HttpServletRequest request) {
        String requestUrl = request.getRequestURL().toString();
        String requestUri = request.getRequestURI();
        String originalContextPath = request.getContextPath();
        String forwardedPrefix = request.getHeader("X-Forwarded-Prefix");

        log.info("分析请求路径: requestUrl={}, requestUri={}, originalContextPath={}, X-Forwarded-Prefix={}",
                requestUrl, requestUri, originalContextPath, forwardedPrefix);

        // 优先使用X-Forwarded-Prefix头（Nginx代理专用）
        if (forwardedPrefix != null && !forwardedPrefix.trim().isEmpty()) {
            String contextPath = forwardedPrefix.trim();
            if (!contextPath.startsWith("/")) {
                contextPath = "/" + contextPath;
            }
            log.info("使用X-Forwarded-Prefix: {}", contextPath);
            return contextPath;
        }

        // 从请求URI中智能提取上下文路径
        if (requestUri != null && requestUri.startsWith("/")) {
            String[] pathSegments = requestUri.split("/");
            log.info("路径段: {}", java.util.Arrays.toString(pathSegments));

            // 检查是否是代理环境（路径段数量大于2且第一个段不是api）
            if (pathSegments.length > 2) {
                String firstSegment = pathSegments[1];

                // 如果第一个路径段不是"api"和"log-management"，很可能是代理的上下文路径
                if (!"api".equals(firstSegment) && !"log-management".equals(firstSegment)) {
                    String contextPath = "/" + firstSegment;
                    log.info("从URI提取的contextPath: {}", contextPath);
                    return contextPath;
                }

                // 特殊处理：如果是releaseApi、BoRuiApi等代理路径
                if ("releaseApi".equals(firstSegment) || "BoRuiApi".equals(firstSegment) ||
                    firstSegment.toLowerCase().contains("api") || firstSegment.toLowerCase().contains("release")) {
                    String contextPath = "/" + firstSegment;
                    log.info("检测到代理API路径: {}", contextPath);
                    return contextPath;
                }
            }
        }

        // 回退到原始方法
        log.info("使用原始contextPath: {}", originalContextPath);
        return originalContextPath != null ? originalContextPath : "";
    }

    /**
     * 检查是否是代理环境
     */
    private boolean isProxyEnvironment(HttpServletRequest request, String contextPath) {
        // 检查代理头
        String forwardedHost = request.getHeader("X-Forwarded-Host");
        String forwardedPort = request.getHeader("X-Forwarded-Port");
        String forwardedProto = request.getHeader("X-Forwarded-Proto");

        // 如果有任何代理头，认为是代理环境
        if (forwardedHost != null || forwardedPort != null || forwardedProto != null) {
            return true;
        }

        // 检查上下文路径是否不是标准的/api
        if (!"/api".equals(contextPath) && !contextPath.isEmpty()) {
            return true;
        }

        // 检查Host头是否与ServerName不同
        String host = request.getHeader("Host");
        String serverName = request.getServerName();
        if (host != null && !host.startsWith(serverName)) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否应该在URL中添加端口号
     */
    private boolean shouldAddPortToUrl(int port, boolean isProxyEnvironment, HttpServletRequest request) {
        // 标准端口不需要添加
        if (port == 80 || port == 443) {
            return false;
        }

        // 代理环境下的特殊处理
        if (isProxyEnvironment) {
            // 检查Host头中是否包含端口号
            String host = request.getHeader("Host");
            if (host != null && host.contains(":")) {
                return true; // Host头包含端口，说明外部访问需要端口号
            }

            // 检查是否有X-Forwarded-Port头
            String forwardedPort = request.getHeader("X-Forwarded-Port");
            if (forwardedPort != null && !forwardedPort.trim().isEmpty()) {
                return true; // 有代理端口头，说明需要端口号
            }
        }

        // 非标准端口总是添加
        return port != 80 && port != 443;
    }

    /**
     * 获取当前激活的配置文件
     */
    private String getActiveProfile() {
        String profiles = System.getProperty("spring.profiles.active");
        return profiles != null ? profiles : "default";
    }

    /**
     * 验证访问令牌
     */
    private boolean isValidAccessToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 优先使用新的安全Token验证
        if (secureTokenUtil.isValidAccessToken(token)) {
            return true;
        }

        // 兼容旧的配置Token验证
        return logViewerAccessToken.equals(token.trim());
    }

    /**
     * 生成访问拒绝页面
     */
    private String generateAccessDeniedPage() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>访问被拒绝 - 日志查看器</title>\n");
        html.append("    <style>\n");
        html.append("        body {\n");
        html.append("            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n");
        html.append("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
        html.append("            min-height: 100vh;\n");
        html.append("            display: flex;\n");
        html.append("            align-items: center;\n");
        html.append("            justify-content: center;\n");
        html.append("            margin: 0;\n");
        html.append("            padding: 20px;\n");
        html.append("        }\n");
        html.append("        .container {\n");
        html.append("            background: white;\n");
        html.append("            border-radius: 15px;\n");
        html.append("            padding: 40px;\n");
        html.append("            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n");
        html.append("            text-align: center;\n");
        html.append("            max-width: 500px;\n");
        html.append("            width: 100%;\n");
        html.append("        }\n");
        html.append("        .icon { font-size: 64px; color: #e74c3c; margin-bottom: 20px; }\n");
        html.append("        h1 { color: #2c3e50; margin-bottom: 20px; font-size: 28px; }\n");
        html.append("        p { color: #7f8c8d; margin-bottom: 30px; line-height: 1.6; }\n");
        html.append("        .access-info {\n");
        html.append("            background: #f8f9fa;\n");
        html.append("            border-radius: 8px;\n");
        html.append("            padding: 20px;\n");
        html.append("            margin: 20px 0;\n");
        html.append("            border-left: 4px solid #3498db;\n");
        html.append("        }\n");
        html.append("        .access-info h3 { color: #2c3e50; margin-bottom: 10px; }\n");
        html.append("        .access-info code {\n");
        html.append("            background: #e9ecef;\n");
        html.append("            padding: 2px 6px;\n");
        html.append("            border-radius: 4px;\n");
        html.append("            font-family: 'Consolas', 'Monaco', monospace;\n");
        html.append("        }\n");
        html.append("        .btn {\n");
        html.append("            display: inline-block;\n");
        html.append("            padding: 12px 24px;\n");
        html.append("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
        html.append("            color: white;\n");
        html.append("            text-decoration: none;\n");
        html.append("            border-radius: 6px;\n");
        html.append("            font-weight: 500;\n");
        html.append("            transition: transform 0.3s;\n");
        html.append("        }\n");
        html.append("        .btn:hover { transform: translateY(-2px); }\n");
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <div class=\"container\">\n");
        html.append("        <div class=\"icon\">🔒</div>\n");
        html.append("        <h1>访问被拒绝</h1>\n");
        html.append("        <p>您需要提供有效的访问令牌才能访问日志查看器。</p>\n");
        html.append("        <div class=\"access-info\">\n");
        html.append("            <h3>📋 访问方法</h3>\n");
        html.append("            <p>请在URL中添加 <code>accessToken</code> 参数：</p>\n");
        html.append("            <p><code>/api/log-management/viewer?accessToken=YOUR_TOKEN</code></p>\n");
        html.append("            <p><code>/api/log-management/viewer/page?accessToken=YOUR_TOKEN</code></p>\n");
        html.append("        </div>\n");
        html.append("        <div class=\"access-info\">\n");
        html.append("            <h3>🔧 管理员说明</h3>\n");
        html.append("            <p>访问令牌可在配置文件中设置：</p>\n");
        html.append("            <p><code>log.viewer.access-token=your-secure-token</code></p>\n");
        html.append("        </div>\n");
        html.append("        <a href=\"javascript:history.back()\" class=\"btn\">返回上一页</a>\n");
        html.append("    </div>\n");
        html.append("</body>\n");
        html.append("</html>\n");
        return html.toString();
    }

    /**
     * 生成简化版日志查看器页面
     */
    private String generateSimpleLogViewerPage(String accessToken, HttpServletRequest request) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>WebSocket实时日志查看器</title>\n");
        html.append("    <style>\n");
        html.append("        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n");
        html.append("        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n");
        html.append("        .header { background: #2c3e50; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }\n");
        html.append("        .controls { margin-bottom: 20px; }\n");
        html.append("        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }\n");
        html.append("        .btn-primary { background: #3498db; color: white; }\n");
        html.append("        .btn-danger { background: #e74c3c; color: white; }\n");
        html.append("        .btn-success { background: #27ae60; color: white; }\n");
        html.append("        .status { padding: 10px; border-radius: 4px; margin-bottom: 10px; }\n");
        html.append("        .status.connected { background: #d4edda; color: #155724; }\n");
        html.append("        .status.disconnected { background: #f8d7da; color: #721c24; }\n");
        html.append("        .log-display { background: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; height: 400px; overflow-y: auto; font-family: monospace; font-size: 13px; }\n");
        html.append("        .log-entry { margin-bottom: 5px; padding: 5px; border-radius: 3px; }\n");
        html.append("        .log-entry.INFO { background: rgba(52, 152, 219, 0.1); }\n");
        html.append("        .log-entry.WARN { background: rgba(241, 196, 15, 0.1); color: #f39c12; }\n");
        html.append("        .log-entry.ERROR { background: rgba(231, 76, 60, 0.1); color: #e74c3c; }\n");
        html.append("        .log-entry.WELCOME { background: rgba(46, 204, 113, 0.1); color: #2ecc71; }\n");
        html.append("        input[type='text'] { padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin: 5px; }\n");
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <div class=\"container\">\n");
        // 使用请求信息构建WebSocket URL
        String wsUrl = buildWebSocketUrl(request);
        Map<String, Object> serverInfo = buildServerInfo(request);

        html.append("        <div class=\"header\">\n");
        html.append("            <h1>🔍 WebSocket实时日志查看器</h1>\n");
        html.append("            <p>访问令牌已验证 ✅ | 服务器: ").append(wsUrl).append("</p>\n");
        html.append("        </div>\n");

        // 注入服务器配置
        html.append("        <script>\n");
        html.append("            window.SERVER_CONFIG = {\n");
        html.append("                \"websocketUrl\": \"").append(wsUrl.replace("\"", "\\\"")).append("\",\n");
        html.append("                \"accessToken\": \"").append(accessToken.replace("\"", "\\\"")).append("\",\n");
        html.append("                \"serverAddress\": \"").append(serverInfo.get("address").toString().replace("\"", "\\\"")).append("\",\n");
        html.append("                \"serverPort\": \"").append(serverInfo.get("port").toString().replace("\"", "\\\"")).append("\",\n");
        html.append("                \"contextPath\": \"").append(serverInfo.get("contextPath").toString().replace("\"", "\\\"")).append("\",\n");
        html.append("                \"requestUrl\": \"").append(serverInfo.get("requestUrl").toString().replace("\"", "\\\"")).append("\"\n");
        html.append("            };\n");
        html.append("            console.log('✅ 服务器配置已注入:', window.SERVER_CONFIG);\n");
        html.append("        </script>\n");
        html.append("        <div class=\"controls\">\n");
        html.append("            <input type=\"text\" id=\"clientId\" placeholder=\"客户端ID\" value=\"log-viewer-").append(System.currentTimeMillis() % 1000).append("\">\n");
        html.append("            <button id=\"connectBtn\" class=\"btn btn-primary\">连接</button>\n");
        html.append("            <button id=\"clearBtn\" class=\"btn btn-success\">清空日志</button>\n");
        html.append("            <span>消息数: <span id=\"messageCount\">0</span></span>\n");
        html.append("        </div>\n");
        html.append("        <div id=\"status\" class=\"status disconnected\">未连接</div>\n");
        html.append("        <div id=\"logDisplay\" class=\"log-display\"></div>\n");
        html.append("    </div>\n");
        html.append("    <script>\n");
        html.append("        let ws = null;\n");
        html.append("        let isConnected = false;\n");
        html.append("        let messageCount = 0;\n");
        html.append("        const connectBtn = document.getElementById('connectBtn');\n");
        html.append("        const clearBtn = document.getElementById('clearBtn');\n");
        html.append("        const clientIdInput = document.getElementById('clientId');\n");
        html.append("        const statusDiv = document.getElementById('status');\n");
        html.append("        const logDisplay = document.getElementById('logDisplay');\n");
        html.append("        const messageCountSpan = document.getElementById('messageCount');\n");
        html.append("        \n");
        html.append("        connectBtn.addEventListener('click', toggleConnection);\n");
        html.append("        clearBtn.addEventListener('click', clearLogs);\n");
        html.append("        \n");
        html.append("        function toggleConnection() {\n");
        html.append("            if (isConnected) {\n");
        html.append("                disconnect();\n");
        html.append("            } else {\n");
        html.append("                connect();\n");
        html.append("            }\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function connect() {\n");
        html.append("            const clientId = clientIdInput.value.trim();\n");
        html.append("            if (!clientId) {\n");
        html.append("                alert('请输入客户端ID');\n");
        html.append("                return;\n");
        html.append("            }\n");
        html.append("            \n");
        html.append("            const wsUrl = '").append(wsUrl.replace("{clientId}", "' + clientId + '")).append("';\n");
        html.append("            console.log('🔗 连接WebSocket:', wsUrl);\n");
        html.append("            console.log('🔧 WebSocket URL构建完成');\n");
        html.append("            \n");
        html.append("            try {\n");
        html.append("                ws = new WebSocket(wsUrl);\n");
        html.append("                \n");
        html.append("                ws.onopen = function() {\n");
        html.append("                    isConnected = true;\n");
        html.append("                    updateStatus('connected', '已连接到: ' + wsUrl);\n");
        html.append("                    connectBtn.textContent = '断开';\n");
        html.append("                    connectBtn.className = 'btn btn-danger';\n");
        html.append("                };\n");
        html.append("                \n");
        html.append("                ws.onmessage = function(event) {\n");
        html.append("                    try {\n");
        html.append("                        const message = JSON.parse(event.data);\n");
        html.append("                        addLogEntry(message);\n");
        html.append("                        messageCount++;\n");
        html.append("                        messageCountSpan.textContent = messageCount;\n");
        html.append("                    } catch (e) {\n");
        html.append("                        console.error('解析消息失败:', e);\n");
        html.append("                    }\n");
        html.append("                };\n");
        html.append("                \n");
        html.append("                ws.onclose = function() {\n");
        html.append("                    isConnected = false;\n");
        html.append("                    updateStatus('disconnected', '连接已关闭');\n");
        html.append("                    connectBtn.textContent = '连接';\n");
        html.append("                    connectBtn.className = 'btn btn-primary';\n");
        html.append("                };\n");
        html.append("                \n");
        html.append("                ws.onerror = function(error) {\n");
        html.append("                    console.error('WebSocket错误:', error);\n");
        html.append("                    updateStatus('disconnected', '连接错误');\n");
        html.append("                };\n");
        html.append("                \n");
        html.append("            } catch (error) {\n");
        html.append("                alert('连接失败: ' + error.message);\n");
        html.append("            }\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function disconnect() {\n");
        html.append("            if (ws) {\n");
        html.append("                ws.close();\n");
        html.append("            }\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function addLogEntry(message) {\n");
        html.append("            const time = new Date(message.timestamp).toLocaleTimeString();\n");
        html.append("            const level = message.level || 'INFO';\n");
        html.append("            const body = message.body || '';\n");
        html.append("            \n");
        html.append("            const logEntry = document.createElement('div');\n");
        html.append("            logEntry.className = 'log-entry ' + level;\n");
        html.append("            logEntry.innerHTML = '<span style=\"color: #7f8c8d;\">' + time + '</span> <strong>' + level + '</strong> ' + escapeHtml(body);\n");
        html.append("            \n");
        html.append("            logDisplay.appendChild(logEntry);\n");
        html.append("            logDisplay.scrollTop = logDisplay.scrollHeight;\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function clearLogs() {\n");
        html.append("            logDisplay.innerHTML = '';\n");
        html.append("            messageCount = 0;\n");
        html.append("            messageCountSpan.textContent = messageCount;\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function updateStatus(type, message) {\n");
        html.append("            statusDiv.className = 'status ' + type;\n");
        html.append("            statusDiv.textContent = message;\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        function escapeHtml(text) {\n");
        html.append("            const div = document.createElement('div');\n");
        html.append("            div.textContent = text;\n");
        html.append("            return div.innerHTML;\n");
        html.append("        }\n");
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>\n");
        return html.toString();
    }

    /**
     * 获取配置的日志目录
     */
    private String getConfiguredLogDirectory() {
        if (logFilePath != null && !logFilePath.isEmpty()) {
            File logFile = new File(logFilePath);
            return logFile.getParent();
        }
        return getLogDirectory(); // 回退到原来的逻辑
    }

    /**
     * 获取配置的日志路径数组
     */
    private String[] getConfiguredLogPaths() {
        List<String> paths = new ArrayList<>();

        // 优先使用配置的日志路径
        if (logFilePath != null && !logFilePath.isEmpty()) {
            File logFile = new File(logFilePath);
            String logDir = logFile.getParent();
            if (logDir != null) {
                paths.add(logDir);
            }
        }

        // 添加滚动日志路径
        if (rollingLogPath != null && !rollingLogPath.isEmpty()) {
            File rollingFile = new File(rollingLogPath);
            String rollingDir = rollingFile.getParent();
            if (rollingDir != null && !paths.contains(rollingDir)) {
                paths.add(rollingDir);
            }
        }

        // 回退到默认路径
        String[] defaultPaths = {
            "logs",
            "edc-research-center/edc-research-api/logs",
            "target/logs",
            "edc-research-center/edc-research-api/target/logs"
        };

        for (String defaultPath : defaultPaths) {
            if (!paths.contains(defaultPath)) {
                paths.add(defaultPath);
            }
        }

        return paths.toArray(new String[0]);
    }

    /**
     * 发送测试日志到WebSocket
     */
    @PostMapping("/test")
    public CommonResult<Map<String, String>> sendTestLog(
            @RequestParam(defaultValue = "INFO") String level,
            @RequestParam(defaultValue = "这是一条测试日志") String message) {
        
        try {
            // 这里可以通过SpringLogWebSocketHandler发送测试消息
            // 目前SpringLogWebSocketHandler已经有模拟日志推送功能

            Map<String, String> data = new HashMap<>();
            data.put("message", "测试日志功能已集成在WebSocket中");
            data.put("level", level);
            data.put("content", message);
            data.put("note", "WebSocket会自动推送模拟日志，每3秒一条");
            data.put("websocketUrl", buildWebSocketUrl());

            return CommonResult.success(data, "测试日志发送成功");
        } catch (Exception e) {
            log.error("发送测试日志失败", e);
            return CommonResult.failed("发送测试日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志文件列表
     */
    @GetMapping("/files")
    public CommonResult<Map<String, Object>> getLogFiles(
            @RequestParam(required = false) String accessToken) {

        // 验证accessToken（如果提供）
        if (accessToken != null && !isValidAccessToken(accessToken)) {
            return CommonResult.failed("访问被拒绝：无效的访问令牌");
        }
        try {
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> fileList = new ArrayList<>();

            // 优先使用配置的日志路径
            String[] logPaths = getConfiguredLogPaths();
            
            for (String logPath : logPaths) {
                File logsDir = new File(logPath);
                if (logsDir.exists() && logsDir.isDirectory()) {
                    File[] files = logsDir.listFiles((dir, name) ->
                        name.endsWith(".log") || name.endsWith(".out") || name.endsWith(".gz"));

                    if (files != null) {
                        for (File file : files) {
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("name", file.getName());
                            fileInfo.put("path", logPath);
                            fileInfo.put("size", file.length());
                            fileInfo.put("lastModified", new Date(file.lastModified()));
                            fileInfo.put("readable", file.canRead());
                            fileList.add(fileInfo);
                        }
                    }
                }
            }

            // 按文件名倒序排序（数字编号大的在前面）
            fileList.sort((a, b) -> {
                String nameA = (String) a.get("name");
                String nameB = (String) b.get("name");

                // 提取文件名中的数字编号进行比较
                String numberA = extractFileNumber(nameA);
                String numberB = extractFileNumber(nameB);

                if (numberA != null && numberB != null) {
                    try {
                        int numA = Integer.parseInt(numberA);
                        int numB = Integer.parseInt(numberB);
                        return Integer.compare(numB, numA); // 倒序：大的在前
                    } catch (NumberFormatException e) {
                        // 如果数字解析失败，按字符串倒序
                        return nameB.compareTo(nameA);
                    }
                } else {
                    // 没有数字编号的文件按修改时间倒序
                    Date dateA = (Date) a.get("lastModified");
                    Date dateB = (Date) b.get("lastModified");
                    return dateB.compareTo(dateA);
                }
            });

            data.put("files", fileList);
            data.put("count", fileList.size());
            data.put("searchPaths", Arrays.asList(logPaths));
            data.put("configuredLogPath", logFilePath);
            data.put("logDirectory", getConfiguredLogDirectory());

            return CommonResult.success(data, "获取日志文件列表成功");
        } catch (Exception e) {
            log.error("获取日志文件列表失败", e);
            return CommonResult.failed("获取日志文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 下载日志文件（修复字节数组内容类型转换问题）
     */
    @GetMapping("/download/{filename}")
    public void downloadLogFile(
            @PathVariable String filename,
            @RequestParam(required = false) String accessToken,
            HttpServletResponse response) {

        // 验证accessToken（如果提供）
        if (accessToken != null && !isValidAccessToken(accessToken)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            return;
        }

        try {
            // 安全检查，防止路径遍历攻击
            if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            File logFile = findLogFile(filename);
            if (logFile == null || !logFile.exists() || !logFile.isFile()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setContentLengthLong(logFile.length());

            // 直接将文件内容写入响应流
            try (InputStream inputStream = Files.newInputStream(logFile.toPath());
                 OutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }

            log.info("成功下载日志文件: {}, 大小: {} bytes", filename, logFile.length());

        } catch (IOException e) {
            log.error("下载日志文件失败: {}", filename, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 读取日志文件内容（分页）
     */
    @GetMapping("/content/{filename}")
    public CommonResult<Map<String, Object>> getLogFileContent(
            @PathVariable String filename,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "100") int size,
            @RequestParam(required = false) String search) {
        
        try {
            // 安全检查
            if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
                return CommonResult.failed("文件名包含非法字符");
            }

            File logFile = findLogFile(filename);
            if (logFile == null || !logFile.exists() || !logFile.isFile()) {
                return CommonResult.failed("日志文件不存在: " + filename);
            }
            
            List<String> lines = Files.readAllLines(Paths.get(logFile.getPath()));
            
            // 搜索过滤
            if (search != null && !search.trim().isEmpty()) {
                lines = lines.stream()
                        .filter(line -> line.toLowerCase().contains(search.toLowerCase()))
                        .collect(Collectors.toList());
            }
            
            // 分页
            int totalLines = lines.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, totalLines);
            
            if (startIndex >= totalLines) {
                startIndex = 0;
                endIndex = Math.min(size, totalLines);
            }
            
            List<String> pageLines = lines.subList(startIndex, endIndex);
            
            Map<String, Object> data = new HashMap<>();
            data.put("filename", filename);
            data.put("lines", pageLines);
            data.put("page", page);
            data.put("size", size);
            data.put("totalLines", totalLines);
            data.put("totalPages", (int) Math.ceil((double) totalLines / size));
            data.put("hasNext", endIndex < totalLines);
            data.put("hasPrevious", startIndex > 0);
            data.put("fileSize", logFile.length());
            data.put("lastModified", new Date(logFile.lastModified()));
            data.put("searchKeyword", search);

            return CommonResult.success(data, "读取日志文件内容成功");

        } catch (IOException e) {
            log.error("读取日志文件内容失败: {}", filename, e);
            return CommonResult.failed("读取日志文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取日志文件的最新内容（实时日志）
     */
    @GetMapping("/tail/{filename}")
    public CommonResult<Map<String, Object>> tailLogFile(
            @PathVariable String filename,
            @RequestParam(defaultValue = "50") int lines,
            @RequestParam(required = false) String accessToken) {

        // 验证accessToken（如果提供）
        if (accessToken != null && !isValidAccessToken(accessToken)) {
            return CommonResult.failed("访问被拒绝：无效的访问令牌");
        }

        try {
            File logFile = findLogFile(filename);
            if (logFile == null || !logFile.exists() || !logFile.isFile()) {
                return CommonResult.failed("日志文件不存在: " + filename);
            }
            
            List<String> allLines = Files.readAllLines(Paths.get(logFile.getPath()));
            
            // 获取最后N行
            int totalLines = allLines.size();
            int startIndex = Math.max(0, totalLines - lines);
            List<String> tailLines = allLines.subList(startIndex, totalLines);
            
            Map<String, Object> data = new HashMap<>();
            data.put("filename", filename);
            data.put("lines", tailLines);
            data.put("totalLines", totalLines);
            data.put("requestedLines", lines);
            data.put("actualLines", tailLines.size());
            data.put("fileSize", logFile.length());
            data.put("lastModified", new Date(logFile.lastModified()));
            data.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return CommonResult.success(data, "获取日志文件尾部内容成功");

        } catch (IOException e) {
            log.error("读取日志文件尾部失败: {}", filename, e);
            return CommonResult.failed("读取日志文件失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public CommonResult<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            health.put("websocketStatus", SpringLogWebSocketHandler.getConnectionStatus());
            health.put("logDirectory", getConfiguredLogDirectory());
            health.put("configuredLogPath", logFilePath);
            health.put("availableLogFiles", getAvailableLogFiles());
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            health.put("websocketUrl", buildWebSocketUrl());
            health.put("serverInfo", buildServerInfo());

            return CommonResult.success(health, "系统健康检查通过");
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return CommonResult.failed("系统健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 查找日志文件
     */
    private File findLogFile(String filename) {
        // 使用配置的日志路径
        String[] logPaths = getConfiguredLogPaths();

        for (String logPath : logPaths) {
            File logFile = new File(logPath, filename);
            if (logFile.exists() && logFile.isFile()) {
                log.debug("找到日志文件: {} 在路径: {}", filename, logPath);
                return logFile;
            }
        }

        log.warn("未找到日志文件: {} 在任何配置的路径中", filename);
        return null;
    }

    /**
     * 获取日志目录
     */
    private String getLogDirectory() {
        String[] logPaths = {
            "logs",
            "edc-research-center/edc-research-api/logs",
            "target/logs",
            "edc-research-center/edc-research-api/target/logs"
        };
        
        for (String logPath : logPaths) {
            File dir = new File(logPath);
            if (dir.exists() && dir.isDirectory()) {
                return dir.getAbsolutePath();
            }
        }
        
        return "未找到日志目录";
    }

    /**
     * 获取可用的日志文件数量
     */
    private int getAvailableLogFiles() {
        int count = 0;
        // 使用配置的日志路径
        String[] logPaths = getConfiguredLogPaths();

        for (String logPath : logPaths) {
            File logsDir = new File(logPath);
            if (logsDir.exists() && logsDir.isDirectory()) {
                File[] files = logsDir.listFiles((dir, name) ->
                    name.endsWith(".log") || name.endsWith(".out") || name.endsWith(".gz"));
                if (files != null) {
                    count += files.length;
                }
            }
        }

        return count;
    }

    /**
     * 提取文件名中的数字编号
     * 例如：edc-research-project.2025-07-23.1.log.gz -> "1"
     */
    private String extractFileNumber(String filename) {
        try {
            // 匹配文件名中的数字编号模式
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\.(\\d+)\\.(log|out)(\\.gz)?$");
            java.util.regex.Matcher matcher = pattern.matcher(filename);
            if (matcher.find()) {
                return matcher.group(1);
            }

            // 如果没有找到标准模式，尝试提取任何数字
            pattern = java.util.regex.Pattern.compile("(\\d+)");
            matcher = pattern.matcher(filename);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.debug("提取文件编号失败: {}", filename, e);
        }
        return null;
    }

    /**
     * 生成简化版日志登录页面
     */
    private String generateSimpleLogLoginPage() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>EDC 日志系统登录</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0; }\n" +
                "        .login-container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; max-width: 400px; }\n" +
                "        .form-group { margin-bottom: 15px; }\n" +
                "        label { display: block; margin-bottom: 5px; font-weight: bold; }\n" +
                "        input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }\n" +
                "        .btn { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }\n" +
                "        .btn:hover { background: #0056b3; }\n" +
                "        .error { color: red; font-size: 14px; margin-top: 10px; display: none; }\n" +
                "        h1 { text-align: center; color: #333; margin-bottom: 20px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"login-container\">\n" +
                "        <h1>EDC 日志系统登录</h1>\n" +
                "        <div class=\"form-group\">\n" +
                "            <label for=\"accessToken\">访问令牌:</label>\n" +
                "            <input type=\"text\" id=\"accessToken\" placeholder=\"请输入访问令牌\">\n" +
                "        </div>\n" +
                "        <div class=\"form-group\">\n" +
                "            <label for=\"code\">验证码:</label>\n" +
                "            <input type=\"text\" id=\"code\" placeholder=\"请输入验证码\">\n" +
                "        </div>\n" +
                "        <div class=\"form-group\">\n" +
                "            <label for=\"refreshCode\">刷新码:</label>\n" +
                "            <input type=\"text\" id=\"refreshCode\" placeholder=\"请输入刷新码\">\n" +
                "        </div>\n" +
                "        <button class=\"btn\" onclick=\"login()\">登录</button>\n" +
                "        <div id=\"error\" class=\"error\"></div>\n" +
                "    </div>\n" +
                "    <script>\n" +
                "        function login() {\n" +
                "            const accessToken = document.getElementById('accessToken').value;\n" +
                "            const code = document.getElementById('code').value;\n" +
                "            const refreshCode = document.getElementById('refreshCode').value;\n" +
                "            \n" +
                "            if (!accessToken || !code || !refreshCode) {\n" +
                "                showError('请填写所有字段');\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            // 验证访问令牌并跳转\n" +
                "            fetch('/api/log-management/viewer?accessToken=' + encodeURIComponent(accessToken))\n" +
                "                .then(response => response.json())\n" +
                "                .then(data => {\n" +
                "                    if (data.code === 200) {\n" +
                "                        window.location.href = '/api/log-management/log-viewer.html?accessToken=' + encodeURIComponent(accessToken);\n" +
                "                    } else {\n" +
                "                        showError(data.message || '登录失败');\n" +
                "                    }\n" +
                "                })\n" +
                "                .catch(error => {\n" +
                "                    showError('网络错误，请稍后重试');\n" +
                "                });\n" +
                "        }\n" +
                "        \n" +
                "        function showError(message) {\n" +
                "            const errorElement = document.getElementById('error');\n" +
                "            errorElement.textContent = message;\n" +
                "            errorElement.style.display = 'block';\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String errorMessage) {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>EDC 日志系统 - 错误</title>\n" +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; background: #f5f5f5; display: flex; justify-content: center; align-items: center; min-height: 100vh; margin: 0; }\n" +
                "        .error-container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; max-width: 500px; }\n" +
                "        .error-icon { font-size: 48px; color: #dc3545; margin-bottom: 20px; }\n" +
                "        h1 { color: #333; margin-bottom: 15px; }\n" +
                "        p { color: #666; margin-bottom: 20px; }\n" +
                "        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }\n" +
                "        .btn:hover { background: #0056b3; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"error-container\">\n" +
                "        <div class=\"error-icon\">⚠️</div>\n" +
                "        <h1>页面加载失败</h1>\n" +
                "        <p>" + errorMessage + "</p>\n" +
                "        <a href=\"/api/log-management/login\" class=\"btn\">返回登录页面</a>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * Java 8兼容的readAllBytes方法
     * 替代Java 11的Files.readAllBytes()
     */
    private byte[] readAllBytes(Path path) throws IOException {
        try (InputStream inputStream = Files.newInputStream(path);
             ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

            byte[] data = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }

            return buffer.toByteArray();
        }
    }
}
