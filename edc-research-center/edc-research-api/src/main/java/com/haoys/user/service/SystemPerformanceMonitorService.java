package com.haoys.user.service;

import com.haoys.user.domain.vo.monitor.SystemPerformanceVo;

import java.util.Map;

/**
 * 系统性能监控服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-06
 */
public interface SystemPerformanceMonitorService {

    /**
     * 获取系统综合性能信息
     * 
     * @return 系统性能信息
     */
    SystemPerformanceVo getSystemPerformanceOverview();

    /**
     * 获取CPU使用情况
     * 
     * @return CPU信息
     */
    Map<String, Object> getCpuInfo();

    /**
     * 获取内存使用情况
     * 
     * @return 内存信息
     */
    Map<String, Object> getMemoryInfo();

    /**
     * 获取磁盘使用情况
     * 
     * @return 磁盘信息
     */
    Map<String, Object> getDiskInfo();

    /**
     * 获取JVM详细信息
     * 
     * @return JVM信息
     */
    Map<String, Object> getJvmInfo();

    /**
     * 获取数据库连接池状态
     * 
     * @return 数据库信息
     */
    Map<String, Object> getDatabaseInfo();

    /**
     * 获取线程池状态
     * 
     * @return 线程池信息
     */
    Map<String, Object> getThreadPoolInfo();

    /**
     * 获取系统进程信息
     * 
     * @return 进程信息
     */
    Map<String, Object> getProcessInfo();

    /**
     * 检测数据库死锁
     * 
     * @return 死锁信息
     */
    Map<String, Object> getDeadlockInfo();

    /**
     * 获取缓存状态信息
     * 
     * @return 缓存信息
     */
    Map<String, Object> getCacheInfo();

    /**
     * 获取应用健康状态
     * 
     * @return 健康状态信息
     */
    Map<String, Object> getHealthInfo();

    /**
     * 获取系统实时指标
     * 
     * @return 系统指标
     */
    Map<String, Object> getSystemMetrics();

    /**
     * 强制执行垃圾回收
     */
    void forceGarbageCollection();

    /**
     * 清理系统缓存
     * 
     * @param cacheName 缓存名称，为空则清理所有缓存
     */
    void clearSystemCache(String cacheName);
}
