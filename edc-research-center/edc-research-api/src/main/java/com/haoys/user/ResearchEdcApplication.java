package com.haoys.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.util.StringUtils;


/**
 * 应用启动入口
 */
//@EnableRetry
@EnableScheduling
@ComponentScan(basePackages={"com.haoys.user","com.haoys.quartz","com.haoys.disease","com.haoys.xinjiang","com.haoys.rdr","com.haoys.health"},nameGenerator = ResearchEdcApplication.SpringBeanNameGenerator.class)
@MapperScan(basePackages = {"com.haoys.user.mapper","com.haoys.quartz.mapper","com.haoys.disease.mapper","com.haoys.xinjiang.mapper","com.haoys.rdr.mapper"}, nameGenerator = ResearchEdcApplication.SpringBeanNameGenerator.class)
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class, ElasticsearchDataAutoConfiguration.class})
public class ResearchEdcApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(ResearchEdcApplication.class, args);
    }

    // WebSocket配置已移至SpringWebSocketConfig
    
    public static class SpringBeanNameGenerator extends AnnotationBeanNameGenerator {
        @Override
        protected String buildDefaultBeanName(BeanDefinition definition) {
            if (definition instanceof AnnotatedBeanDefinition) {
                String beanName = determineBeanNameFromAnnotation((AnnotatedBeanDefinition) definition);
                if (StringUtils.hasText(beanName)) {
                    // Explicit bean name found.
                    return beanName;
                }
            }
            return definition.getBeanClassName();
        }
        
    }
}