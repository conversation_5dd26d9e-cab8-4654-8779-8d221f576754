package com.haoys.user.project.controller;

import com.haoys.user.common.annotation.Log;
import com.haoys.user.common.api.CommonPage;
import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.core.base.BaseController;
import com.haoys.user.enums.system.BusinessType;
import com.haoys.user.domain.param.project.ProjectAnnouncementParam;
import com.haoys.user.domain.vo.UploadFileResultVo;
import com.haoys.user.domain.vo.project.ProjectAnnouncementVo;
import com.haoys.user.domain.vo.system.OrganizationVo;
import com.haoys.user.domain.vo.testee.UploadRichTextFileVo;
import com.haoys.user.model.ProjectAnnouncement;
import com.haoys.user.service.ProjectAnnouncementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Api(tags = "公告发布")
@RestController
@RequestMapping("/projectAnnouncement")
public class ProjectAnnouncementController extends BaseController{

    @Autowired
    private ProjectAnnouncementService projectAnnouncementService;


    @ApiOperation("获取公告列表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = "string"),
            @ApiImplicitParam(name = "projectOrgName", value = "项目中心id", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "类型 公告1/帮助2", dataType = "string"),
            @ApiImplicitParam(name = "systemType", value = "发布类型 SRC-EDC-1/SRC-ePRO-2", dataType = "string"),
            @ApiImplicitParam(name = "status", value = "发布状态0-未发布/1-已发布", dataType = "string"),
    })
    @GetMapping("/list")
    public CommonResult list(@RequestParam(value = "projectId") String projectId,
                             @RequestParam(value = "projectOrgName", required = false) String projectOrgName,
                             @RequestParam(value = "type", required = false) String type,
                             @RequestParam(value = "systemType", required = false) String systemType,
                             @RequestParam(value = "status", required = false) String status,
                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ProjectAnnouncement projectAnnouncement = new ProjectAnnouncement();
        projectAnnouncement.setProjectId(Long.valueOf(projectId));
        projectAnnouncement.setProjectOrgName(projectOrgName);
        projectAnnouncement.setType(type);
        projectAnnouncement.setSystemType(systemType);
        projectAnnouncement.setStatus(status);
        CommonPage<ProjectAnnouncementVo> list = projectAnnouncementService.selectProjectAnnouncementList(projectAnnouncement, pageNum, pageSize);
        return CommonResult.success(list);
    }

    @ApiOperation("查询公告信息")
    @GetMapping("/getProjectAnnouncementById")
    public CommonResult<ProjectAnnouncementVo> selectProjectAnnouncementById(Long id) {
        return CommonResult.success(projectAnnouncementService.selectProjectAnnouncementById(id));
    }
    @ApiOperation("获取中心列表")
    @GetMapping("/getProjectOrgList")
    public CommonResult<List<OrganizationVo>> getProjectOrgList(String projectId) {
        return CommonResult.success(projectAnnouncementService.getProjectOrgList(projectId));
    }

    @ApiOperation("添加公告")
    @Log(title = "添加公告", businessType = BusinessType.INSERT)
    @PostMapping("/addProjectAnnouncement")
    public CommonResult add(@RequestBody ProjectAnnouncementParam projectAnnouncementParam) {
        return returnResult(projectAnnouncementService.addProjectAnnouncement(getUserId(), projectAnnouncementParam));
    }

    @ApiOperation("发布/撤销公告")
    @Log(title = "发布/撤销公告", businessType = BusinessType.UPDATE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectOrgId", value = "项目中心id", dataType = "string"),
            @ApiImplicitParam(name = "type", value = "类型 公告1/帮助2", dataType = "string"),
            @ApiImplicitParam(name = "systemType", value = "发布类型 SRC-EDC-1/SRC-ePRO-2", dataType = "string"),
            @ApiImplicitParam(name = "status", value = "发布状态0-未发布/1-已发布", dataType = "string"),
    })
    @PostMapping("/publish")
    public CommonResult editPublish(@RequestParam(value = "id") Long id,
                                @RequestParam(value = "status", required = false) String status,
                                @RequestParam(value = "projectOrgId", required = false) Long projectOrgId,
                                @RequestParam(value = "type") String type,
                                @RequestParam(value = "systemType") String systemType) {
        ProjectAnnouncement projectAnnouncement = new ProjectAnnouncement();
        projectAnnouncement.setId(id);
        projectAnnouncement.setStatus(status);
        projectAnnouncement.setType(type);
        projectAnnouncement.setSystemType(systemType);
        projectAnnouncement.setProjectOrgId(projectOrgId);
        projectAnnouncement.setUpdateUser(getUserId());
        return returnResult(projectAnnouncementService.editPublish(projectAnnouncement));
    }

    @ApiOperation("编辑公告")
    @Log(title = "编辑公告", businessType = BusinessType.UPDATE)
    @PostMapping("/edidProjectAnnouncement")
    public CommonResult edit(@RequestBody ProjectAnnouncementParam projectAnnouncementParam) {
        return returnResult(projectAnnouncementService.editProjectAnnouncement(getUserId(), projectAnnouncementParam));
    }

    @ApiOperation("删除公告")
    @Log(title = "删除公告", businessType = BusinessType.DELETE)
    @PostMapping("/delProjectAnnouncement")
    public CommonResult deleteProjectAnnouncement(@RequestParam(value = "id") Long id) {
        return returnResult(projectAnnouncementService.deleteProjectAnnouncement(id));
    }

    @ResponseBody
    @ApiOperation("附件上传")
    @Log(title = "附件上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadTenantLogoFile")
    public CommonResult uploadProjectFormFile(@RequestParam("file") MultipartFile file) throws IOException {
        List<UploadFileResultVo> uploadFileResultVo = projectAnnouncementService.saveUpload(file);
        return CommonResult.success(uploadFileResultVo);
    }

    @ResponseBody
    @ApiOperation("富文本附件上传")
    @Log(title = "附件上传", businessType = BusinessType.INSERT)
    @PostMapping(value = "/uploadRichTextFile")
    public CommonResult uploadRichTextFile(@RequestParam("file") MultipartFile file) throws IOException {
        UploadRichTextFileVo uploadRichTextFileVo = projectAnnouncementService.uploadRichTextFile(file);
        UploadRichTextFileVo[] arr = new UploadRichTextFileVo[1];
        arr[0] = uploadRichTextFileVo;
        Map map = new HashMap();
        map.put("errno", 0);
        map.put("data", arr);

        return CommonResult.success(map);
    }

}
