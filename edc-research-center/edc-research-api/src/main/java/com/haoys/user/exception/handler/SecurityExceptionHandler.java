package com.haoys.user.exception.handler;

import com.haoys.user.common.api.CommonResult;
import com.haoys.user.common.api.ResultCode;
import com.haoys.user.exception.user.UserPasswordNotMatchException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 安全认证异常处理器
 *
 * <p>专门处理Spring Security和自定义安全相关的异常</p>
 * <p>包括认证失败、权限不足、凭据错误等安全相关错误</p>
 * <p>提供详细的安全审计日志和用户友好的错误提示</p>
 * <p>保留原始GlobalExceptionHandler的所有安全审计功能</p>
 *
 * <h3>处理的异常类型：</h3>
 * <ul>
 *   <li><b>访问拒绝异常</b>：AccessDeniedException</li>
 *   <li><b>认证异常</b>：AuthenticationException</li>
 *   <li><b>凭据错误异常</b>：BadCredentialsException</li>
 *   <li><b>认证凭据未找到</b>：AuthenticationCredentialsNotFoundException</li>
 *   <li><b>认证不足异常</b>：InsufficientAuthenticationException</li>
 *   <li><b>用户密码不匹配</b>：UserPasswordNotMatchException</li>
 * </ul>
 *
 * <h3>安全特性：</h3>
 * <ul>
 *   <li>详细的安全审计日志记录</li>
 *   <li>敏感信息自动过滤，防止信息泄露</li>
 *   <li>异常行为监控和告警</li>
 *   <li>客户端IP和用户代理记录</li>
 *   <li>安全事件时间戳记录</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Slf4j
@RestControllerAdvice
@Order(150) // 较高优先级，优先处理安全异常
public class SecurityExceptionHandler extends BaseExceptionHandler {

    /**
     * 处理访问拒绝异常
     *
     * <p>当用户没有足够权限访问资源时抛出此异常</p>
     * <p>需要记录安全审计日志，但不暴露权限配置信息</p>
     *
     * @param exception 访问拒绝异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AccessDeniedException.class)
    public CommonResult<String> handleAccessDeniedException(AccessDeniedException exception, 
                                                           HttpServletRequest request) {
        String userName = safeGetUserName();
        String userAgent = request.getHeader("User-Agent");
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("访问拒绝: 用户[%s] 使用[%s] 访问[%s] 被拒绝", 
                         userName, userAgent, request.getRequestURI()));
        
        // 记录安全事件
        recordSecurityEvent(request, "ACCESS_DENIED", userName, 
                          com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request));
        
        return createFailedResponseWithTraceId(request, ResultCode.FORBIDDEN, "权限不足，无法访问该资源");
    }

    /**
     * 处理认证异常
     *
     * <p>处理各种认证相关的异常</p>
     *
     * @param exception 认证异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AuthenticationException.class)
    public CommonResult<String> handleAuthenticationException(AuthenticationException exception, 
                                                             HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("认证异常: IP[%s] 访问[%s] 认证失败, 异常类型=%s", 
                         clientIp, request.getRequestURI(), exception.getClass().getSimpleName()));
        
        // 记录安全事件
        recordSecurityEvent(request, "AUTHENTICATION_FAILED", "anonymous", clientIp);
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, "认证失败，请重新登录");
    }

    /**
     * 处理凭据错误异常
     *
     * <p>当用户名或密码错误时抛出此异常</p>
     *
     * @param exception 凭据错误异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(BadCredentialsException.class)
    public CommonResult<String> handleBadCredentialsException(BadCredentialsException exception, 
                                                             HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("凭据错误: IP[%s] 登录凭据错误", clientIp));
        
        // 记录安全事件
        recordSecurityEvent(request, "BAD_CREDENTIALS", "unknown", clientIp);
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, "用户名或密码错误");
    }

    /**
     * 处理认证凭据未找到异常
     *
     * <p>当请求中缺少认证凭据时抛出此异常</p>
     *
     * @param exception 认证凭据未找到异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(AuthenticationCredentialsNotFoundException.class)
    public CommonResult<String> handleCredentialsNotFoundException(AuthenticationCredentialsNotFoundException exception, 
                                                                  HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("认证凭据未找到: IP[%s] 访问[%s] 缺少认证信息", clientIp, request.getRequestURI()));
        
        // 记录安全事件
        recordSecurityEvent(request, "CREDENTIALS_NOT_FOUND", "anonymous", clientIp);
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, "缺少认证信息，请先登录");
    }

    /**
     * 处理认证不足异常
     *
     * <p>当认证级别不足以访问资源时抛出此异常</p>
     *
     * @param exception 认证不足异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(InsufficientAuthenticationException.class)
    public CommonResult<String> handleInsufficientAuthentication(InsufficientAuthenticationException exception, 
                                                                HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        String userName = safeGetUserName();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("认证不足: 用户[%s] IP[%s] 认证级别不足", userName, clientIp));
        
        // 记录安全事件
        recordSecurityEvent(request, "INSUFFICIENT_AUTHENTICATION", userName, clientIp);
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, "认证级别不足，请重新认证");
    }

    /**
     * 处理用户密码不匹配异常
     *
     * <p>处理用户密码验证失败的异常，出于安全考虑不返回详细信息</p>
     *
     * @param exception 用户密码不匹配异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(UserPasswordNotMatchException.class)
    public CommonResult<String> handleUserPasswordNotMatchException(UserPasswordNotMatchException exception, 
                                                                   HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        String userName = safeGetUserName();
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("用户密码验证失败: 用户=%s, 客户端IP=%s", userName, clientIp));

        // 记录安全事件
        recordSecurityEvent(request, "PASSWORD_MISMATCH", userName, clientIp);

        return createFailedResponseWithTraceId(request, ResultCode.REQUEST_BUSSINESS_PARAM_FAIL, "密码验证失败");
    }

    /**
     * 处理JWT令牌异常
     *
     * <p>处理JWT令牌相关的异常</p>
     *
     * @param exception JWT异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(io.jsonwebtoken.JwtException.class)
    public CommonResult<String> handleJwtException(io.jsonwebtoken.JwtException exception, 
                                                  HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        
        String traceId = logExceptionWithTraceId(request, exception, "WARN", 
            String.format("JWT令牌异常: IP[%s] 令牌无效或过期", clientIp));
        
        // 记录安全事件
        recordSecurityEvent(request, "INVALID_JWT_TOKEN", safeGetUserName(), clientIp);
        
        String message = "令牌无效或已过期，请重新登录";
        
        // 根据具体的JWT异常类型提供更详细的信息
        if (exception instanceof io.jsonwebtoken.ExpiredJwtException) {
            message = "令牌已过期，请重新登录";
        } else if (exception instanceof io.jsonwebtoken.MalformedJwtException) {
            message = "令牌格式错误，请重新登录";
        } else if (exception instanceof io.jsonwebtoken.SignatureException) {
            message = "令牌签名验证失败，请重新登录";
        }
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, message);
    }

    /**
     * 处理会话认证异常
     *
     * <p>处理会话过期或无效的异常</p>
     *
     * @param exception 会话认证异常对象
     * @param request HTTP请求对象
     * @return 统一响应结果
     */
    @ExceptionHandler(org.springframework.security.web.authentication.session.SessionAuthenticationException.class)
    public CommonResult<String> handleSessionAuthenticationException(
            org.springframework.security.web.authentication.session.SessionAuthenticationException exception, 
            HttpServletRequest request) {
        String clientIp = com.haoys.user.common.ip.RequestIpUtils.getIpAddress(request);
        
        String traceId = logExceptionWithTraceId(request, exception, "INFO", 
            String.format("会话认证异常: IP[%s] 会话过期或无效", clientIp));
        
        // 记录安全事件
        recordSecurityEvent(request, "SESSION_EXPIRED", safeGetUserName(), clientIp);
        
        return createFailedResponseWithTraceId(request, ResultCode.UNAUTHORIZED, "会话已过期，请重新登录");
    }
}
