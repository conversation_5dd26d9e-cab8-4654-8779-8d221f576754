package com.haoys.user.security.config;

import com.haoys.user.common.filter.RepeatableFilter;
import com.haoys.user.security.filter.EnhancedXssFilter;
import com.haoys.user.common.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.DispatcherType;
import java.util.HashMap;
import java.util.Map;

/**
 * 过滤器配置
 *
 * <p>配置系统中使用的各种过滤器，包括XSS防护、请求重复读取等</p>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 1.0.0
 */
@Configuration
public class FilterConfig {

    @Value("${xss.excludes:/api/upload/**,/api/file/**}")
    private String excludes;

    @Value("${xss.urlPatterns:/api/**}")
    private String urlPatterns;

    @Autowired
    private EnhancedXssFilter enhancedXssFilter;

    /**
     * 注册增强版XSS过滤器
     *
     * <p>提供更强大的XSS攻击防护功能，包括：</p>
     * <ul>
     *   <li>HTML标签过滤</li>
     *   <li>JavaScript代码检测</li>
     *   <li>事件处理器过滤</li>
     *   <li>URL编码检测</li>
     *   <li>可配置的白名单</li>
     * </ul>
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    @ConditionalOnProperty(value = "xss.enabled", havingValue = "true", matchIfMissing = true)
    public FilterRegistrationBean enhancedXssFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(enhancedXssFilter);

        // 解析URL模式
        String[] patterns = StringUtils.split(urlPatterns, ",");
        registration.addUrlPatterns(patterns);

        registration.setName("enhancedXssFilter");
        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);

        // 设置初始化参数
        Map<String, String> initParameters = new HashMap<>();
        initParameters.put("excludes", excludes);
        registration.setInitParameters(initParameters);

        return registration;
    }

    /**
     * 注册可重复读取请求体过滤器
     *
     * <p>允许多次读取HTTP请求体，用于日志记录、参数验证等场景</p>
     * <p>注意：该过滤器会排除文件上传请求，避免流被意外消费</p>
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Bean
    public FilterRegistrationBean repeatableFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/*");
        registration.setName("repeatableFilter");
        // 设置较低优先级，确保在文件上传处理之后执行
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE - 1);
        return registration;
    }
}
