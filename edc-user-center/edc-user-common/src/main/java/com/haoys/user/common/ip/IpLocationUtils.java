package com.haoys.user.common.ip;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * IP地址位置工具类
 * 
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
public class IpLocationUtils {
    
    /**
     * 内网IP段
     */
    private static final String[] INTERNAL_IP_PREFIXES = {
        "127.", "192.168.", "10.", "172.16.", "172.17.", "172.18.", "172.19.",
        "172.20.", "172.21.", "172.22.", "172.23.", "172.24.", "172.25.",
        "172.26.", "172.27.", "172.28.", "172.29.", "172.30.", "172.31."
    };
    
    /**
     * 常见城市IP段映射（简化版本）
     */
    private static final Map<String, String> IP_LOCATION_MAP = new HashMap<>();
    
    static {
        // 这里可以添加一些常见的IP段到城市的映射
        // 实际项目中建议使用专业的IP地址库如GeoIP2
        IP_LOCATION_MAP.put("127.0.0.1", "本地");
        IP_LOCATION_MAP.put("localhost", "本地");
    }
    
    /**
     * 根据IP地址获取位置信息
     * 
     * @param ip IP地址
     * @return 位置信息
     */
    public static String getLocationByIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return "未知位置";
        }
        
        // 处理特殊IP
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "本地";
        }
        
        // 检查是否为内网IP
        if (isInternalIp(ip)) {
            return "内网";
        }
        
        // 检查预定义的IP映射
        String location = IP_LOCATION_MAP.get(ip);
        if (StrUtil.isNotBlank(location)) {
            return location;
        }
        
        // 尝试通过第三方服务获取位置信息
        try {
            return getLocationFromThirdParty(ip);
        } catch (Exception e) {
            log.warn("获取IP位置信息失败: ip={}, error={}", ip, e.getMessage());
            return "未知位置";
        }
    }
    
    /**
     * 判断是否为内网IP
     * 
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (StrUtil.isBlank(ip)) {
            return false;
        }
        
        for (String prefix : INTERNAL_IP_PREFIXES) {
            if (ip.startsWith(prefix)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 通过第三方服务获取位置信息
     * 这里提供一个简化的实现，实际项目中可以集成专业的IP地址库
     * 
     * @param ip IP地址
     * @return 位置信息
     */
    private static String getLocationFromThirdParty(String ip) {
        // 这里可以集成第三方IP地址查询服务
        // 例如：百度地图API、高德地图API、或者使用GeoIP2数据库
        
        // 简化实现：根据IP段返回大概位置
        String[] parts = ip.split("\\.");
        if (parts.length >= 2) {
            String prefix = parts[0] + "." + parts[1];
            
            // 这里可以根据IP段返回对应的地区
            // 实际项目中建议使用专业的IP地址库
            switch (prefix) {
                case "114.114":
                    return "北京";
                case "8.8":
                    return "美国";
                case "223.5":
                    return "杭州";
                default:
                    return parseLocationByIpRange(ip);
            }
        }
        
        return "未知位置";
    }
    
    /**
     * 根据IP段解析位置（简化版本）
     * 
     * @param ip IP地址
     * @return 位置信息
     */
    private static String parseLocationByIpRange(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length >= 1) {
                int firstOctet = Integer.parseInt(parts[0]);
                
                // 根据第一个八位组大致判断地区（这只是示例，不准确）
                if (firstOctet >= 1 && firstOctet <= 126) {
                    return "亚洲地区";
                } else if (firstOctet >= 128 && firstOctet <= 191) {
                    return "欧美地区";
                } else if (firstOctet >= 192 && firstOctet <= 223) {
                    return "其他地区";
                }
            }
        } catch (Exception e) {
            log.debug("解析IP位置失败: ip={}, error={}", ip, e.getMessage());
        }
        
        return "未知位置";
    }
    
    /**
     * 获取IP地址的详细信息
     * 
     * @param ip IP地址
     * @return IP详细信息
     */
    public static Map<String, String> getIpDetails(String ip) {
        Map<String, String> details = new HashMap<>();
        details.put("ip", ip);
        details.put("location", getLocationByIp(ip));
        details.put("type", isInternalIp(ip) ? "内网" : "外网");
        
        return details;
    }
}
