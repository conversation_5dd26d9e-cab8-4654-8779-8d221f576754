package com.haoys.user.enums.system;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @description: Oss类型
 **/
@Getter
public enum OssTypeEnum {
    /**
     * 阿里云
     */
    ALIYUN(0),

    /**
     * 七牛云
     */
    QINIU(1),
    /**
     * 又拍云
     */
    UPYUN(2),

    /**
     * 本地存储
     */
    LOCAL(3),

    /**
     * minio
     */
    MINIO(4);

    @JsonValue
    private final int value;

    @JsonCreator
    OssTypeEnum(int value) {
        this.value = value;
    }

}
