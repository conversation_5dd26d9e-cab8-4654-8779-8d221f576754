package com.haoys.user.common.bussiness;

import com.haoys.user.common.api.ResultCode;

public class BusinessConfig {

    /**业务状态定义*/
    public static final String RETURN_MESSAGE_DEFAULT = ResultCode.SUCCESS.getMessage();
    public static final String RETURN_MESSAGE_FAIL = "fail";

    /** 系统数据状态定义 0-有效数据 1-无效数据*/
    public static final String VALID_STATUS = "0";
    public static final String NO_VALID_STATUS = "1";
    public static final String QUERY_ALL_STATUS = "000";
    public static final String EMPTY_VALUE = "";

    /** 项目方案发布状态 0-未发布 1-已发布*/
    public static final Integer NOT_PUBLISH_STATUS = 0;
    public static final Integer PUBLISH_STATUS = 1;


    /** system相关数据状态定义 0-启用 1-停用  -1删除  2临时状态，需要修改密码*/
    public static final Integer ENABLED_STATUS = 0;
    public static final Integer DISABLED_STATUS = 1;
    public static final Integer DELETE_STATUS = -1;
    public static final Integer TEMP_STATUS = 2;

    public static final String DEFAULT_INPUT_VALUE_STRING = "0";
    public static final Integer DEFAULT_INPUT_VALUE_INTEGER = 0;

    /**科研项目创建方式定义说明*/
    public static final String PROJECT_CREATE_METHOD_1 = "1";
    public static final String PROJECT_CREATE_METHOD_2 = "2";
    public static final String PROJECT_CREATE_METHOD_3 = "3";


    /**访视表单类型定义说明*/
    public static final String PROJECT_VISIT_CRF_FORM = "form";
    public static final String PROJECT_VISIT_CRF_TABLE = "table";
    public static final String PROJECT_PATIENT_FORM = "patient";

    /**访视表单变量类型定义说明*/
    public static final String PROJECT_VISIT_CRF_FORM_INPUT = "input";
    public static final String PROJECT_VISIT_CRF_FORM_RADIO = "radio";
    public static final String PROJECT_VISIT_CRF_FORM_CHECKBOX = "checkbox";
    public static final String PROJECT_VISIT_CRF_FORM_SELECT = "select";
    public static final String PROJECT_VISIT_CRF_FORM_TEXTAREA = "textarea";
    public static final String PROJECT_VISIT_CRF_FORM_NUMBER = "number";
    public static final String PROJECT_VISIT_CRF_FORM_DATE = "date";
    public static final String PROJECT_VISIT_CRF_FORM_SLIDER = "slider";
    public static final String PROJECT_VISIT_CRF_FORM_FILE = "uploadFile";
    public static final String PROJECT_VISIT_CRF_FORM_IMAGE = "uploadImg";
    public static final String PROJECT_VISIT_CRF_FORM_IMAGE_VIEW = "imgDisplay";
    public static final String PROJECT_VISIT_CRF_FORM_TEXT_VIEW = "textDisplay";
    public static final String PROJECT_VISIT_CRF_FORM_TABLE = "table";
    public static final String PROJECT_VISIT_CRF_FORM_GROUP = "group";
    public static final String PROJECT_VISIT_CRF_FORM_DYNAMIC_GROUP = "dynamic_group";
    public static final String PROJECT_VISIT_CRF_FORM_TABLE_COLUMN = "tabColumn";

    /**参与者表单变量类型定义说明*/
    public static final String PROJECT_VISIT_CRF_FORM_INPUT_TEXTAREA = "input|textarea";
    public static final String PROJECT_VISIT_CRF_FORM_SELECT_RADIO = "select|radio";
    public static final String PROJECT_VISIT_CRF_FORM_SELECT_CHECKBOX = "checkbox";

    /**参与者列表字段查询定义说明*/
    public static final String PROJECT_TESTEE_QUERY_FIELD_GENDER = "gender";
    public static final String PROJECT_TESTEE_QUERY_FIELD_REAL_NAME = "real_name";
    public static final String PROJECT_TESTEE_QUERY_FIELD_ACRONYM = "acronym";
    public static final String PROJECT_TESTEE_QUERY_FIELD_VISIT_CARD_NO = "visit_card_no";
    public static final String PROJECT_TESTEE_QUERY_FIELD_INFORMED_DATE = "informed_date";

    public static final String QUERY_FIELD_GENDER = "gender";
    public static final String QUERY_FIELD_REAL_NAME = "realName";
    public static final String QUERY_FIELD_ACRONYM = "acronym";
    public static final String QUERY_FIELD_VISIT_CARD_NO = "visitCardNo";
    
    public static final String PROJECT_TESTEE_QUERY_FIELD_INPUT_VALUE_1 = "input_value1";
    public static final String PROJECT_TESTEE_QUERY_FIELD_INPUT_VALUE_2 = "input_value2";
    public static final String PROJECT_TESTEE_QUERY_FIELD_INPUT_VALUE_3 = "input_value3";
    public static final String PROJECT_TESTEE_QUERY_FIELD_INPUT_VALUE_4 = "input_value4";
    
    public static final String QUERY_FIELD_INPUT_VALUE_1 = "inheritor";
    public static final String QUERY_FIELD_INPUT_VALUE_2 = "Instructor";
    public static final String QUERY_FIELD_INPUT_VALUE_3 = "Batchnumber";
    public static final String QUERY_FIELD_INPUT_VALUE_4 = "REDATE";
    
    
    
    
    /**全局知情同意书*/
    public static final String PROJECT_TESTE_INFORMED_CONSENT_FORMS = "ICF";
    /**全局知情同意书签署日期*/
    public static final String PROJECT_TESTE_INFORMED_CONSENT_FORMS_DATE = "ICFDATE";
    public static final String PROJECT_TESTE_INFORMED_CONSENT_FORMS_VISIT_NAME = "筛选期访视(V0)";

    /**参与者表单变量类型定义说明 1-非必填 2-强制必填 3-必填提示*/
    public static final String VAR_REQUIRE_TYPE_1 = "1";
    public static final String VAR_REQUIRE_TYPE_2 = "2";
    public static final String VAR_REQUIRE_TYPE_3 = "3";

    /**参与者表单录入类型定义说明 1-单次录入 2-多次录入*/
    public static final String TEMPLATE_CONFIG_INPUT_TYPE_1 = "1";
    public static final String TEMPLATE_CONFIG_INPUT_TYPE_2 = "2";


    /**访视表单字典类型定义说明 1-系统字典  2-项目表单 3-计量单位字典*/
    public static final String PROJECT_VISIT_CRF_SYSTEM_FORM = "1";
    public static final String PROJECT_VISIT_CRF_PRO_FORM = "2";
    public static final String PROJECT_VISIT_CRF_NUMBER_FORM = "3";

    /**表单总计完成状态 1-未录入 2-录入中 3-已完成*/
    public static final String FORM_FILL_NOT = "1";
    public static final String FORM_FILL_ING = "2";
    public static final String FORM_FILL_OVE = "3";
    
    /**表单数据来源*/
    public static final String TESTEE_INPUT_DATA_FROM_1 = "testee-input";
    public static final String TESTEE_INPUT_DATA_FROM_2 = "system-mapping-input";
    public static final String TESTEE_INPUT_DATA_FROM_3 = "researcher-input";
    public static final String TESTEE_INPUT_DATA_FROM_4 = "custom-mapping-input";

    /** 实验室配置类型*/
    public static final String LAB_TYPE_EXPERIMENT_1 = "1";
    public static final String LAB_TYPE_EXPERIMENT_2 = "2";

    /** 实验室配置查询类型*/
    public static final String LAB_QUERY_VARIABLE_TYPE = "1";
    public static final String LAB_QUERY_TABLE_VARIABLE_TYPE = "2";
    public static final String LAB_QUERY_GROUP_VARIABLE_TYPE = "3";
    public static final String LAB_QUERY_GROUP_TABLE_VARIABLE_TYPE = "4";

    /** 实验室配置指定范围*/
    public static final String LAB_CONFIG_SCOPE_FORM = "form";
    public static final String LAB_CONFIG_SCOPE_VISIT = "visit";

    /** 实验室配置定量和定性分析*/
    public static final String LAB_CONFIG_TYPE_01 = "1";
    public static final String LAB_CONFIG_TYPE_02 = "2";

    /** 实验室配置定量和定性分析*/
    public static final String LAB_CONFIG_GENDER_COMMON = "通用";
    public static final String LAB_CONFIG_GENDER_MALE = "男";
    public static final String LAB_CONFIG_GENDER_FEMALE = "女";

    /**项目表单和单位字典层级定义说明 0-无层级 1-一级 2-二级 3-三级*/
    public static final String PARENT_LEVEL_ROOT_NODE = "0";

    /**项目字典来源 系统初始化 项目新增*/
    public static final String PROJECT_DICTIONARY_SYSTEM_INIT = "system-create-init";
    public static final String PROJECT_DICTIONARY_PROJECT_ADD = "system-project-add";

    /**数据稽查AOP参数类型定义说明*/
    public static final String PARAMTER_TYPE_01 = "RequestParam";
    public static final String PARAMTER_TYPE_02 = "RequestBody";

    /**数据稽查AOP参数对象路径定义说明*/
    public static final String PARAMTER_OBJECT_TYPE_01 = "com.haoys.user.domain.param.project.ProjectTesteeTableParam";
    public static final String PARAMTER_OBJECT_TYPE_02 = "com.haoys.user.domain.param.project.ProjectTesteeResultParam";
    public static final String PARAMTER_OBJECT_TYPE_03 = "com.haoys.user.domain.param.project.ProjectTesteeParam";
    public static final String PARAMTER_OBJECT_TYPE_04 = "com.haoys.user.domain.param.participant.ProjectParticipantViewConfigParam";
    public static final String PARAMTER_OBJECT_TYPE_05 = "com.haoys.user.domain.param.system.RegisterUserParam";
    public static final String PARAMTER_OBJECT_TYPE_06 = "com.haoys.user.domain.param.system.ActiveUserParam";
    public static final String PARAMTER_OBJECT_TYPE_07 = "com.haoys.user.domain.param.system.FindSystemUserPasswordParam";


    /**超管定义说明*/
    public static final String ADMIN_USER_ID = "1";
    /**1-修改密码 2-重置密码 3-患者端找回密码 */
    public static final String UPDATE_PASSWORD_TYPE_01 = "1";
    public static final String UPDATE_PASSWORD_TYPE_02 = "2";
    public static final String UPDATE_PASSWORD_TYPE_03 = "3";


    /**系统用户定义说明*/
    public static final String SYSTEM_TENANT_ID_NOT_FOUND = "企业租户未设置";
    public static final String SYSTEM_PLATFORM_ID_NOT_FOUND = "系统平台未设置";
    public static final String SYSTEM_USER_ID_NOT_FOUND = "查询系统用户信息无效";
    public static final String SYSTEM_USER_NAME_EXIST = "当前用户名已经注册";
    public static final String SYSTEM_USER_MOBILE_EXIST = "当前用户手机号已经注册";
    public static final String SYSTEM_USER_MOBILE_NOT_EXIST = "当前用户手机号不存在";
    public static final String SYSTEM_USER_ACCOUNT_FORBIDEN = "当前用户账号暂时不可用,请联系管理员设置";


    /**系统相关业务常量定义说明*/
    public static final String USER_ORG_RECORD_NOT_FOUND = "当前登录用户所属中心不存在";

    /**系统用户相关业务常量定义说明*/
    public static final String USER_LOGIN_PASSWORD_MESSAGE_01 = "密码错误";
    public static final String USER_LOGIN_PASSWORD_MESSAGE_02 = "新密码与旧密码重复";

    public static final String USER_LOGIN_PASSWORD_HIS_REPEAT = "密码与以往密码已重复多次";

    public static final String USER_ACTIVE_ERROR = "用户未激活";

    /**系统和项目中心相关业务常量定义说明*/
    public static final String RETURN_MESSAGE_RECORD_NOT_FOUND = "当前数据记录不存在";
    public static final String RETURN_MESSAGE_RECORD_PARAMS_ERROR = "参数设置错误";

    public static final String PROJECT_ORG_RECORD_NOT_FOUND = "填写中心不存在";
    public static final String PROJECT_ORG_RECORD_FOUND = "填写项目中心已存在";
    public static final String PROJECT_DEPARTMENT_RECORD_FOUND = "填写中心科室已存在";
    public static final String SYSTEM_ORG_RECORD_FOUND = "填写系统中心已存在";
    public static final String SYSTEM_ORG_RECORD_EMPTY = "填写系统中心不能为空";
    public static final String SYSTEM_ORG_TYPE_EMPTY = "医院等级不能为空";
    public static final String SYSTEM_ORG_TYP_DICE_EMPTY = "该医院等级类型不存在";
    public static final String SYSTEM_ORG_DATA_EMPTY = "录入数据为空";


    public static final String RETURN_ORG_TEMPLATE_NOT_FOUND = "数据行格式不符合要求，请使用模板导入数据";

    public static final String ORG_RELATION_TESTEE_RECORD_FOUND = "中心已录入参与者信息，不能删除";

    public static final String SYS_ORG_PROVINCE_FOUND = "填写省份不存在";
    public static final String SYS_ORG_CITY_FOUND = "填写地市不存在";
    public static final String SYS_ORG_AREA_FOUND = "填写区县不存在";

    /**项目成员相关业务常量定义说明*/
    public static final String PROJECT_USER_REALNAME_NOT_AGREEMENT = "当前用户在系统中已存在，输入姓名不正确";
    public static final String PROJECT_USER_DELETE_RELATION_TESTEE = "当前项目成员已设置为参与者主管医生，不能删除";


    /**项目表单项类型*/
    public static final String PROJECT_LABEL_FORM_TYPE = "1";//计划访视类型
    public static final String PROJECT_LABEL_VISIT_TYPE = "2";//非计划访视类型
    public static final String PROJECT_LABEL_VISIT_NAME = "访视时间";
    public static final String PROJECT_LABEL_VISIT_REG_NAME = "访视";

    /**项目中心定义*/
    public static final String RETURN_MESSAGE_PROJECT_ORG_01 = "当前机构作为主中心已存在";
    public static final String RETURN_MESSAGE_PROJECT_ORG_02 = "当前项目中心已存在";
    public static final String RETURN_MESSAGE_PROJECT_ORG_03 = "此项目已设置当前用户为负责人";
    public static final String RETURN_MESSAGE_PROJECT_ORG_04 = "当前用户未完成设置项目所属中心";

    public static final String RETURN_MESSAGE_PROJECT_ORG_05 = "当前项目用户未设置中心，或者请先在项目中心添加对应的中心";


    /**项目角色类型*/
    public static final String PROJECT_ROLE_MESSAGE_01 = "当前项目默认初始化角色不正确";


    /**模板状态定义*/
    public static final String SYSTEM_TEMPLATE_RECORD_FOUND = "表单模板名称已存在";


    /**项目状态定义*/

    /**项目绑定状态定义*/
    public static final String PROJECT_TESTEE_BIND_RECORD_FOUND = "当前项目未设置患者端绑定操作人";



    /**项目访视状态定义*/
    public static final String RETURN_MESSAGE_PROJECT_VISIT_RECORD_NOT_FOUND = "当前项目访视数据记录不存在";
    public static final String RETURN_MESSAGE_PROJECT_VISIT_FAIL_01 = "调整顺序会影响访视表单的计划访视时间";
    public static final String RETURN_MESSAGE_PROJECT_VISIT_FAIL_02 = "此项目已有CRF，无法使用模板";


    /**项目参与者状态定义*/
    public static final String PROJECT_TESTEE_CODE_FOUND = "参与者编号已存在";
    public static final String PROJECT_TESTEE_MOBILE_FOUND = "参与者录入的手机号码已存在";
    public static final String PROJECT_TESTEE_ORG_LIST_NOT_FOUND = "参与者所属中心参数未设置，请核对项目中心是否添加了中心";


    /**项目质疑定义*/
    public static final String PROJECT_CHALLENGE_ROLE_01 = "当前操作人角色不能创建或回复质疑";

    /**项目表单变量显示条件定义*/
    public static final String PROJECT_FORM_LOGIN_MESSAGE_NOT_NULL = "表单变量设置显示条件不能为空";


    /**项目CRF表单定义*/
    public static final String PROJECT_FORM_NUMBER_KEY = "text_1646621841530";
    public static final String PROJECT_FORM_NUMBER_LABEL = "编号";

    public static final String PROJECT_FORM_OPERATE_KEY = "text_1646621841529";
    public static final String PROJECT_FORM_OPERATE_LABEL = "操作";
    public static final String PROJECT_FORM_OPERATE_VALUE = "查看详情";

    public static final String PROJECT_FORM_VAR_BUTTON = "保存/提交";

    public static final String TEMPLATE_TABLE_NO_FOUND = "当前访视表格变量不存在";

    public static final String TEMPLATE_FROM_USER_NO_DELETE = "当前表单已被使用,禁止删除";

    /**项目访视表单状态定义*/
    public static final String RETURN_MESSAGE_FORM_FORBIDDEN_01 = "表单已发布,不可删除";
    public static final String RETURN_MESSAGE_FORM_LABEL_FORBIDDEN = "当前分类存在表单项，禁止删除";

    public static final String RETURN_MESSAGE_TABLE_FORBIDDEN_01 = "当前表格变量已录入参与者信息，禁止删除";
    public static final String RETURN_MESSAGE_TABLE_FORBIDDEN_02 = "当前表单变量已录入参与者信息，禁止删除";


    /**项目参与者配置状态定义*/
    public static final String PROJECT_TESTEE_CONFIG_FOUND = "当前项目参与者配置已存在";
    public static final String PROJECT_TESTEE_CONFIG_NOT_FOUND = "当前项目未设置参与者绑定配置";


    /**项目任务和方案状态定义*/
    public static final String PROJECT_TASK_RECORD_FOUND = "项目任务已使用，不能删除了";



    /**项目参与者逻辑核查状态定义*/
    public static final String PROJECT_TESTEE_DVP_FOUND = "当前项目表单变量规则已存在";
    public static final String PROJECT_TESTEE_DVP_NOT_FOUND = "当前逻辑核查记录不存在";


    /** 导入访视相关数据验证*/
    public static final String PROJECT_TESTEE_VISIT_DATA_EXCEPTION_MESSAGE = "参与者部分字段导入异常，请下载文件查看";



    /** 项目文件 原图-original 裁剪-crop 拼接-montage */
    public static final String PROJECT_TESTEE_IMAGE_01 = "original";
    public static final String PROJECT_TESTEE_IMAGE_02 = "crop";
    public static final String PROJECT_TESTEE_IMAGE_03 = "montage";
    
    /** 项目文件目录 */
    public static final String PROJECT_TESTEE_FILE_PATH = "project";
    public static final String PROJECT_TESTEE_FILE_PATH_01 = "visit";
    public static final String PROJECT_TESTEE_FILE_PATH_02 = "testee";
    public static final String PROJECT_TESTEE_FILE_PATH_03 = "form";
    public static final String PROJECT_TESTEE_FILE_PATH_04 = "resource";
    public static final String PROJECT_TESTEE_FILE_PATH_05 = "default";


    public static final String OPERATOR_VALUE_01 = "AND";
    public static final String OPERATOR_VALUE_02 = "OR";

    /** 系统企业唯一标识 */
    public static final Integer SYSTEM_ORG_INFO_IS_AUTH = 1;
    public static final String SYSTEM_USER_AES_KEY = "aEsva0zDHECg47P8SuPzmw==";


    /** 字典来源 : 1-系统字典 2-表单字典 3-数据单位 */
    public static final String SYSTEM_DICT = "1";
    public static final String PROJECT_DICT = "2";
    public static final String UNIT_DICT = "3";


    /** 项目字典类型 : 1-表单字典 2-数据单位 */
    public static final String PROJECT_DICT_TYPE = "1";
    public static final String UNIT_DICT_TYPE = "2";

    /** 项目患者来源 1-医生端创建 2-患者自建 3-其他来源 */

    public static final String PROJECT_VISIT_SOURCE_1 = "1";
    public static final String PROJECT_VISIT_SOURCE_2 = "2";
    public static final String PROJECT_VISIT_SOURCE_3 = "3";


    /**  参与者管理导出状态  0: 已完成，1.生成中，2，导出失败 */
    public static final Integer TESTEE_EXPORT_STATUS_0 = 0;
    public static final Integer TESTEE_EXPORT_STATUS_1 = 1;
    public static final Integer TESTEE_EXPORT_STATUS_2 = 2;

    /**  申请加入项目 审核状态 0-待申请 1-已申请待审核 2-审核通过 3-审核不通过 */
    public static final String PROJECT_APPLY_STATUS_0 = "0";
    public static final String PROJECT_APPLY_STATUS_1 = "1";
    public static final String PROJECT_APPLY_STATUS_2 = "2";
    public static final String PROJECT_APPLY_STATUS_3 = "3";

    public static final String RANDOM_METHOD_STATIC_TRIALS = "staticRandomized";
    public static final String RANDOM_METHOD_DYNAMIC_TRIALS = "dynamicRandomized";
    
    
    /**患者嵌套文档在es中索引*/
    public static String DATABASE_INDEX="dateBase_";

    /** 待审核 */
    public static String AUDIT_SUBMIT="0";
    /** 审核成功 */
    public static String AUDIT_SUCCESS="1";
    /** 驳回 */
    public static String AUDIT_REJECT="-1";
}
