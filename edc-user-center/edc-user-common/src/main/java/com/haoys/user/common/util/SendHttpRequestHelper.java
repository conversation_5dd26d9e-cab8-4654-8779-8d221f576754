package com.haoys.user.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

/**
 * http请求类
 * <AUTHOR>
 */
public class SendHttpRequestHelper {
    private static final Log log = LogFactory.getLog(SendHttpRequestHelper.class);

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param param 请求参数，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return      URL 所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url,param,null);
    }

    public static String sendGet(String url, String param,Map<String, String> headers) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("Charsert", "UTF-8");
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            if(headers!=null){
                for(Map.Entry<String, String> entry : headers.entrySet()){
                    connection.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            /*for (String key : map.keySet()) {
                System.out.println(key + "--->" + map.get(key));
            }*/
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 向指定 URL 发送POST方法的请求
     * @param url       发送请求的 URL
     * @param param     请求参数 name1=value1&name2=value2 的形式。
     * @param charset   字符集
     * @return          所代表远程资源的响应结果
     */
    public static String sendPost(String url, String param, String charset) {
        Map<String, Object> params = new HashMap<>(16);
        String[] paramArr = param.split("&");
        for (String str : paramArr) {
            params.put(str.split("=")[0], str.split("=")[1]);
        }
        return sendPost(url,params,charset);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     * @param url       发送请求的 URL
     * @param params    请求参数
     * @return          所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String, Object> params) {
        return sendPost(url,params,"utf-8");
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url       发送请求的 URL
     * @param params    请求参数
     * @param charset   字符集
     * @return          所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String, Object> params, String charset) {
        return sendPost(url,params,charset,null);
    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url       发送请求的 URL
     * @param params    请求参数
     * @param charset   字符集
     * @param headers   头部信息
     * @return          所代表远程资源的响应结果
     */
    public static String sendPost(String url, Map<String, Object> params, String charset, Map<String, String> headers) {
        StringBuffer resultBuffer ;
        HttpClient client;
        BufferedReader br = null;
        try {
            if (url.startsWith("https://")) {
                client = new SslClient();
            } else {
                client = new DefaultHttpClient();
            }

            HttpPost httpPost = new HttpPost(url);
            // 构建请求参数
            List<NameValuePair> list = new ArrayList<>();
            Iterator<Map.Entry<String, Object>> iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> elem = iterator.next();
                if (elem.getValue() != null) {
                    list.add(new BasicNameValuePair(elem.getKey(), String.valueOf(elem.getValue())));
                }
            }

            if (list.size() > 0) {
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, charset);
                httpPost.setEntity(entity);
            }

            if(headers != null){
                for(Map.Entry<String, String> entry : headers.entrySet()){
                    httpPost.addHeader(entry.getKey(),entry.getValue());
                }
            }

            HttpResponse response = client.execute(httpPost);
            // 读取服务器响应数据
            resultBuffer = new StringBuffer();
            br = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), charset));
            String temp;
            while ((temp = br.readLine()) != null) {
                resultBuffer.append(temp);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    br = null;
                    throw new RuntimeException(e);
                }
            }
        }
        return resultBuffer.toString();
    }

    /**
     * 发送https的post请求，参数是json格式
     * @param url           地址
     * @param jsonObject    参数
     * @param charset       编码，一般使用"utf-8"
     * @return              String
     */
    public static String httpsPostJsonParam(String url, JSONObject jsonObject, String charset) {
        String jsonstr = jsonObject.toJSONString();
        HttpClient httpClient ;
        HttpPost httpPost ;
        String result = null;
        try {
            httpClient = new SslClient();
            httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity se = new StringEntity(jsonstr);
            se.setContentType("text/json");
            se.setContentEncoding(new BasicHeader("Content-Type", "application/json"));
            httpPost.setEntity(se);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = EntityUtils.toString(resEntity, charset);
                }
            }
        } catch (Exception ex) {
            log.error("httpsPostJsonParam:url="+url+",jsonObject="+jsonObject.toJSONString(),ex);
        }
        return result;
    }

    public static String doPostJson(String url, String json) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            // 创建请求内容
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            // 执行http请求
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }

    public static void main(String[] args) {
        JSONObject params = new JSONObject();
//        params.put("s_no","f64c56b9-cf38-42f6-bcfb-fbf0cc45149c37");
//        params.put("sign",signId.split("#")[0].toLowerCase());
//        params.put("timestamp",signId.split("#")[1]);
        try {
            String data = SendHttpRequestHelper.httpsPostJsonParam("http://manage.sydw.com/api/signup_detail", params, "UTF-8");
            Map<String, Object> dataMap = JSON.parseObject(data, new TypeReference<HashMap<String, Object>>() {}.getType());
            System.out.println(JSON.toJSONString(dataMap));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



}