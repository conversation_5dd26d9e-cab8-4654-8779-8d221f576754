package com.haoys.user.common.ip;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.haoys.user.common.util.HttpUtils;
import com.haoys.user.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.dreamlu.mica.ip2region.core.Ip2regionSearcher;
import net.dreamlu.mica.ip2region.core.IpInfo;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.*;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description : 地址工具类
 **/
@Slf4j
public class AddressUtils {

    // IP地址查询
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
    
    @Autowired
    private static Ip2regionSearcher ip2regionSearcher;
    
    // 未知地址
    public static final String UNKNOWN = "XX XX";

    public static String getRealAddressByIP(String requestIp) {
        String address = getIntranetIp();
        if (StringUtils.isNotEmpty(address)) {
            requestIp = address;
        }
        
        IpInfo ipInfo = ip2regionSearcher.memorySearch(requestIp);
        if(ipInfo != null){
            return ipInfo.getAddress();
        }
        
        return null;
        
        /*IPInfo ipInfo = IPInfoUtils.getIpInfo(requestIp);
        log.info(ipInfo.getCountry(),ipInfo.getProvince(), ipInfo.getAddress(), ipInfo.getIsp(), ipInfo.isOverseas(), ipInfo.getLat(), ipInfo.getLng());
        return ipInfo.getCountry().concat(" ") + ipInfo.getProvince().concat(" ") + ipInfo.getAddress().concat(" ") + ipInfo.getIsp();*/
    }

    public static String getRealAddressByIP_(String ipAddress) {
        String address = getIntranetIp();
        if(StringUtils.isNotEmpty(address)){
            ipAddress = address;
        }
        try {
            URL url = new URL("http://ip.taobao.com/service/getIpInfo.php?ip=" + ipAddress);
            URLConnection connection = url.openConnection();
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
            String line;
            StringBuilder builder = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }
            reader.close();
            String result = builder.toString();
            Pattern pattern = Pattern.compile("\"country\":\"(.*?)\",\"region\":\"(.*?)\",\"city\":\"(.*?)\",\"county\":\"(.*?)\",\"isp\":\"(.*?)\"");
            Matcher matcher = pattern.matcher(result);
            if (matcher.find()) {
                address = matcher.group(1) + " " + matcher.group(2) + " " + matcher.group(3) + " " + matcher.group(4) + " " + matcher.group(5);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return address;
    }

    public static String getIntranetIp() {
        String intranetIp = null;
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                Enumeration<InetAddress> inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    InetAddress inetAddress = inetAddresses.nextElement();
                    if (!inetAddress.isLoopbackAddress() && !inetAddress.isLinkLocalAddress() && inetAddress.isSiteLocalAddress()) {
                        intranetIp = inetAddress.getHostAddress();
                        break;
                    }
                }
                if (intranetIp != null) {
                    break;
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return intranetIp;
    }

    /**
     * 检查是否为内部IP地址
     *
     * @param ip IP地址
     * @return 结果
     */
    public static boolean internalIp(String ip)
    {
        byte[] addr = RequestIpUtils.textToNumericFormatV4(ip);
        return RequestIpUtils.internalIp(addr) || "127.0.0.1".equals(ip);
    }

    public static String getRealAddressByIP2(String ip) {
        // 内网不查询
        if (internalIp(ip)) {
            return "内网IP";
        }
        try {
            String rspStr = HttpUtils.sendGet(IP_URL, "ip=" + ip + "&json=true", "GBK");
            if (StringUtils.isEmpty(rspStr)) {
                log.error("获取地理位置异常 {}", ip);
                return UNKNOWN;
            }
            JSONObject obj = JSON.parseObject(rspStr);
            String region = obj.getString("pro");
            String city = obj.getString("city");
            return String.format("%s %s", region, city);
        } catch (Exception e) {
            log.error("获取地理位置异常 {}", ip);
        }
        return UNKNOWN;
    }
}
