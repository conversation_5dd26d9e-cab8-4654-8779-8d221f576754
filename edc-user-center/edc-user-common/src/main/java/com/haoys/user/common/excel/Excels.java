package com.haoys.user.common.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.event.SyncReadListener;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.listener.ModelBuildEventListener;
import com.haoys.user.common.annotation.ExcelDynamic;
import org.apache.commons.collections4.CollectionUtils;

import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/* easyExcel工具类 */
public class Excels extends EasyExcel {

    private Excels() {}

    /**
     * 导入
     * @param fileStream 文件流
     * @param clazz 导入类型class
     * @param <T> 导入类型
     * @return List<T>
     */
    public static <T> List<T> imports(InputStream fileStream, Class<T> clazz){
        return read(fileStream).head(clazz).sheet().doReadSync();
    }

    /**
     * 导入带有动态列的数据
     *
     * @param fileStream 文件流
     * @param clazz 导入类型class
     * @param <T> 导入类型
     * @return List<T>
     */
    public static <T> List<T> importsDynamic(InputStream fileStream, Class<T> clazz) throws IllegalAccessException, InstantiationException {
        //初始化 处理动态列 readListener
        DynamicReadListener dynamicReadListener = DynamicReadListener.init();
        //初始化 同步读取数据 readListener
        SyncReadListener syncReadListener = new SyncReadListener();
        /*
         * useDefaultListener = false
         * 默认的 readListener 即 ModelBuildEventListener 会第一个去处理数据，导致ReadHolder中的 currentRowAnalysisResult 已转为 Model 类型，
         * dynamicReadListener 调用到 invoke 时会报错, 因此需要 dynamicReadListener 排在 readListenerList 的第一位，保证能够接收到 map 类型，处理动态列
         * 但我们仍需要 ModelBuildEventListener 所以我们手动注册
         */
        ExcelReaderSheetBuilder sheet = read(fileStream).useDefaultListener(false).head(clazz).sheet();
        sheet.registerReadListener(dynamicReadListener);
        //注册 map转 model readListener
        sheet.registerReadListener(new ModelBuildEventListener());
        sheet.registerReadListener(syncReadListener);
        sheet.doRead();
        return build((List<T>) syncReadListener.getList(), dynamicReadListener);
    }

    /**
     * 处理源数据 为其实例化其中的动态列属性
     *
     * @param targets 处理目标List<T> 中的动态列 为其实例化动态列属性
     * @param <T> 处理数据类型
     * @return 源数据
     * @throws IllegalAccessException IllegalAccessException
     */
    private static <T> List<T> build(List<T> targets, DynamicReadListener listener) throws IllegalAccessException, InstantiationException {
        if (CollectionUtils.isNotEmpty(targets)){
            for (int i = 0; i < targets.size(); i++) {
                T target = targets.get(i);
                // 初始化带有 @ExcelDynamic 标志的属性
                for (Field targetField : target.getClass().getDeclaredFields()) {
                    if (Objects.nonNull(targetField.getAnnotation(ExcelDynamic.class))){
                        targetField.setAccessible(true);
                        targetField.set(target, build(listener.getDynamicColumns().get(i), targetField.getType()));
                    }
                }
            }
        }
        return targets;
    }

    /**
     * 根据class、当前行动态列数据 构建动态列属性
     * @param dynamicRow 当前行动态列数据
     * @param clazz 处理类型
     * @param <T> 处理类型
     * @return 多列转List
     */
    private static <T> T build(Map<String, List<Object>> dynamicRow, Class<T> clazz) throws InstantiationException, IllegalAccessException {
        if (Objects.isNull(clazz)){
            throw new NullPointerException("class not support null value");
        }
        //处理类型属性list
        Field[] fields = clazz.getDeclaredFields();
        //待处理命中动态列数据
        Map<Field, List<Object>> hitColumn = new HashMap<>(fields.length);
        //开始筛选命中动态列
        dynamicRow.forEach((head, columns)->{
            //遍历 <T> 属性
            for (Field field : fields) {
                //获取属性注解
                ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
                if (Objects.nonNull(annotation)){
                    //根据注解值匹配
                    Object[] annotationKeys = annotation.value();
                    for (Object annotationKey : annotationKeys) {
                        if (!Objects.equals("", annotationKey) && Objects.equals(head, annotationKey)){
                            if (CollectionUtils.isNotEmpty(columns)){
                                hitColumn.put(field, columns);
                                break;
                            }
                        }
                    }
                }
            }
        });
        return handle(hitColumn, clazz);
    }

    /**
     * 处理命中的动态列
     * @param hitColumn 待处理数据
     * @param clazz 类型
     * @param <T> T
     * @return T
     */
    private static <T> T handle(Map<Field, List<Object>> hitColumn, Class<T> clazz) throws InstantiationException, IllegalAccessException {
        //处理返回结果
        if(CollectionUtils.isNotEmpty(hitColumn.keySet())){
            T target = clazz.newInstance();
            hitColumn.forEach((field, columns) -> {
                try {
                    List<Object> targetFieldList = new ArrayList<>();
                    for (Object column : columns) {
                        try {
                            Object targetValue = Objects.requireNonNull(getGenericClass(field)).getConstructor(String.class).newInstance((String)column);
                            targetFieldList.add(targetValue);
                        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
                            e.printStackTrace();
                        }
                    }
                    field.setAccessible(true);
                    field.set(target, targetFieldList);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            });
            return target;
        }
        return null;
    }

    /**
     * 通过 Field 获取其泛型
     * @param field 类属性
     * @return 泛型类
     */
    private static Class<?> getGenericClass(Field field){
        Type genericType = field.getGenericType();
        if (genericType instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericType;
            // 获取成员变量的泛型类型信息
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            for (Type actualTypeArgument : actualTypeArguments) {
                return (Class<?>) actualTypeArgument;
            }
        }
        return null;
    }
}


