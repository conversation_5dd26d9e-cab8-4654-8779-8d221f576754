package com.haoys.user.ncbi;

import com.haoys.user.ncbi.model.EFetchResult;
import com.haoys.user.ncbi.model.EGQueryResult;
import com.haoys.user.ncbi.model.ELinkResult;
import com.haoys.user.ncbi.model.ESearchResult;
import com.haoys.user.ncbi.model.ESummaryResult;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.dom4j.Element;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.io.StringReader;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NcbiEUtilsClient {
    private static final String BASE_URL = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/";
    private NcbiConfig config;
    
    public NcbiEUtilsClient(NcbiConfig config) {
        this.config = config;
    }
    
    // 发送请求（复用之前的实现，此处省略重复代码）
    private String sendRequest(String urlString) throws Exception {
        /* ... */
        return null;
    }
    
    // 解析XML响应（复用之前的实现，此处省略重复代码）
    private Document parseXml(String xml) throws Exception {
        /* ... */
        return parseXmlContent(xml);
    }
    
    private ESearchResult parseXmlValue(String xml) throws Exception {
        /* ... */
        return XmlParserUtil.parseESearchResult(xml);
    }
    
    // ------------- 各接口封装 -------------
    
    /** 1. ESearch：复杂搜索（支持日期区间） */
    public ESearchResult eSearch(NcbiQueryBuilder builder) throws Exception {
        String url = BASE_URL + "esearch.fcgi?" + builder.build() + "&usehistory=y&retmode=xml";
        if (config.getApiKey() != null) url += "&api_key=" + config.getApiKey();
        ESearchResult esearchResult =  parseXmlValue(getByOkHttp(url));
        return esearchResult;
        /*return new ESearchResult(
                doc.getElementsByTagName("IdList").item(0).getTextContent().split(" "),
                Integer.parseInt(doc.getElementsByTagName("Count").item(0).getTextContent())
        );*/
    }
    
    /** 2. EFetch：获取数据（支持多ID） */
    public EFetchResult eFetch(NcbiQueryBuilder builder) throws Exception {
        String url = BASE_URL + "efetch.fcgi?" + builder.build();
        if (config.getApiKey() != null) url += "&api_key=" + config.getApiKey();
        return new EFetchResult(sendRequest(url));
    }
    
    /** 3. ESummary：获取摘要 */
    public List<ESummaryResult> eSummary(NcbiQueryBuilder builder) throws Exception {
        String url = BASE_URL + "esummary.fcgi?" + builder.build() + "&retmode=json";
        if (config.getApiKey() != null) url += "&api_key=" + config.getApiKey();
        // 解析JSON响应（此处简化，实际需用Jackson/Gson）
        return Collections.singletonList(new ESummaryResult("Sample Title", "Abstract text..."));
    }
    
    /** 4. ELink：关联记录 */
    public ELinkResult eLink(NcbiQueryBuilder builder) throws Exception {
        String url = BASE_URL + "elink.fcgi?" + builder.build() + "&cmd=neighbor";
        if (config.getApiKey() != null) url += "&api_key=" + config.getApiKey();
        Document doc = parseXml(sendRequest(url));
        // 解析链接结果
        return new ELinkResult("pubmed", "pmc",  Arrays.asList("PMC123", "PMC456"));
    }
    
    /** 5. EGQuery：跨库查询 */
    public EGQueryResult eGQuery(NcbiQueryBuilder builder) throws Exception {
        String url = BASE_URL + "egquery.fcgi?" + builder.build();
        if (config.getApiKey() != null) url += "&api_key=" + config.getApiKey();
        Document doc = parseXml(sendRequest(url));
        // 解析各数据库结果数
        Map<String, Integer> result = new HashMap<>();
        NodeList nodes = doc.getElementsByTagName("ResultItem");
        for (int i = 0; i < nodes.getLength(); i++) {
            Element item = (Element) nodes.item(i);
            result.put(
                    item.attribute("DbName").getValue(),
                    Integer.parseInt(item.attribute("Count").getValue())
            );
        }
        return new EGQueryResult(result);
    }
    
    
    public Document parseXmlContent(String xml) throws Exception {
        // 参数校验
        if (xml == null || xml.trim().isEmpty()) {
            throw new IllegalArgumentException("XML content cannot be null or empty");
        }
        
        // 安全配置 DocumentBuilderFactory
        DocumentBuilderFactory factory = createSecureDocumentBuilderFactory();
        
        try (StringReader reader = new StringReader(xml)) {
            DocumentBuilder builder = factory.newDocumentBuilder();
            
            // 配置错误处理器
            builder.setErrorHandler(new org.xml.sax.ErrorHandler() {
                @Override
                public void warning(org.xml.sax.SAXParseException e) throws SAXException {
                    throw new SAXException("XML Warning: " + e.getMessage(), e);
                }
                
                @Override
                public void error(org.xml.sax.SAXParseException e) throws SAXException {
                    throw new SAXException("XML Error: " + e.getMessage(), e);
                }
                
                @Override
                public void fatalError(org.xml.sax.SAXParseException e) throws SAXException {
                    throw new SAXException("XML Fatal Error: " + e.getMessage(), e);
                }
            });
            
            // 解析 XML
            return builder.parse(new InputSource(reader));
            
        } catch (ParserConfigurationException e) {
            throw new Exception("XML Parser configuration error", e);
        } catch (SAXException e) {
            throw new Exception("XML parsing error: " + e.getMessage(), e);
        } catch (IOException e) {
            throw new Exception("IO error during XML parsing", e);
        }
    }
    
    private static DocumentBuilderFactory createSecureDocumentBuilderFactory() throws ParserConfigurationException {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        // 安全配置 (防止 XXE 攻击)
        try {
            // 禁用 DTD 和外部实体
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", false);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            factory.setXIncludeAware(false);
            factory.setExpandEntityReferences(false);
        } catch (ParserConfigurationException e) {
            throw new ParserConfigurationException("Failed to configure secure XML parser: " + e.getMessage());
        }
        return factory;
    }
    
    
    /**
     * 通过OkHttp客户端进行Get请求
     * @param url
     * @return
     */
    public String getByOkHttp(String url) {
        
        //创建OkHttpClient实例
        OkHttpClient client = new OkHttpClient();
        
        //创建请求
        Request request = new Request.Builder()
                //.addHeader("Authorization", "") //设置请求头，用于鉴权使用
                .url(url)
                .get()
                .build();
        
        //发送请求并获取响应
        try(Response response = client.newCall(request).execute()){
            if(response.isSuccessful()){
                return response.body().string();
            }else {
                throw new IOException("Unexpected code " + response.code());
            }
            
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
