package com.haoys.user.enums;

import net.logstash.logback.encoder.org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public enum ProjectTesteeEnum {

    /** 项目参与者研究状态 */
    PROJECT_TESTEE_STATUS_01("10000000501","筛选中"),
    PROJECT_TESTEE_STATUS_02("10000000502","筛选未通过"),
    PROJECT_TESTEE_STATUS_03("10000000503","研究中"),
    PROJECT_TESTEE_STATUS_04("10000000504","研究结束"),
    PROJECT_TESTEE_STATUS_05("10000000505","中止脱落");

    private String name;
    private String value;

    ProjectTesteeEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static final Map<String, String> dataMap = new HashMap();
    static {
        for (ProjectTesteeEnum tEnum : ProjectTesteeEnum.values()) {
            dataMap.put(tEnum.getName(), tEnum.getValue());
        }
    }

    public static ProjectTesteeEnum getProjectTesteeEnumByKey(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (ProjectTesteeEnum temp : ProjectTesteeEnum.values()) {
            if (temp.getName() == code) {
                return temp;
            }
        }
        return null;
    }
}
