package com.haoys.user.domain.param.system;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;


@Data
@EqualsAndHashCode(callSuper = false)
public class UserPatientVerificationCodeParam {


    @NotEmpty
    @ApiModelProperty(value = "手机号",required = true)
    private String mobile;

    @NotEmpty
    @ApiModelProperty(value = "验证码",required = true)
    private String code;

    @NotEmpty
    @ApiModelProperty(value = "密码",required = true)
    private String password;

    @NotEmpty
    @ApiModelProperty(value = "确认密码",required = true)
    private String confirmPassword;

}
