package com.haoys.user.service;

import com.haoys.user.domain.vo.auth.SystemRoleVo;
import com.haoys.user.model.SystemRole;
import com.haoys.user.model.SystemUserRole;

import java.util.List;

/**
 * 后台角色管理Service
 */
public interface SystemRoleService {
    /**
     * 添加角色
     */
    int saveSystemRole(SystemRole role);

    /**
     * 修改角色信息
     */
    int updateSystemRole(Long id, SystemRole role);

    /**
     * 批量删除角色
     */
    int deleteSystemRole(List<Long> ids);

    /**
     * 分页获取角色列表
     */
    List<SystemRoleVo> getSystemRoleListForPage(String keyword, Integer status, Long id, String englishName, Integer pageSize, Integer pageNum);

    /**
     * 根据用户ID查询角色权限
     */
    List<String> selectRolePermissionByUserId(Long userId);

    /**
     * 根据roleId查询角色菜单
     * @param roleId
     * @return
     */
    List<Long> getSystemRoleMenuListByRoleId(Long roleId);


    /**
     * 根据用户ID查询角色权限
     */
    List<SystemRole> selectRoleByUserId(Long userId);


    int batchSaveUserRole(List<SystemUserRole> userRoleList);


    void deleteUserRole(Long userId);

    /**
     * 获取角色下拉列表
     */
    List<SystemRole> getSystemRoleList();

    /**
     * 编辑获取回显数据
     */
    SystemRoleVo getSystemRoleAndMenuIdsByRoleId(Long id);

    SystemRoleVo getSystemRoleName(String systemRoleName, String tenantId);

    void modifyRoleStatus(List<Long> ids, Integer status);
    
    SystemRole selectByPrimaryKey(Long aLong);
    
    void deleteSystemUserRoleByUserId(Long userId, String tenantId, String platformId);
    
    void insertSystemUserRole(SystemUserRole record);
}
