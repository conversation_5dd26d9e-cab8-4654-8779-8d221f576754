<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemAuthorizationInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemAuthorizationInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="request_url" jdbcType="VARCHAR" property="requestUrl" />
    <result column="redirect_url" jdbcType="VARCHAR" property="redirectUrl" />
    <result column="validate_time" jdbcType="TIMESTAMP" property="validateTime" />
    <result column="enabled" jdbcType="BIT" property="enabled" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, app_id, app_secret, access_token, request_url, redirect_url, validate_time, 
    enabled, status, create_time, create_user_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemAuthorizationInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_authorization_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_authorization_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemAuthorizationInfoExample">
    delete from system_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemAuthorizationInfo">
    insert into system_authorization_info (id, name, app_id, 
      app_secret, access_token, request_url, 
      redirect_url, validate_time, enabled, 
      status, create_time, create_user_id
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{appSecret,jdbcType=VARCHAR}, #{accessToken,jdbcType=VARCHAR}, #{requestUrl,jdbcType=VARCHAR}, 
      #{redirectUrl,jdbcType=VARCHAR}, #{validateTime,jdbcType=TIMESTAMP}, #{enabled,jdbcType=BIT}, 
      #{status,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemAuthorizationInfo">
    insert into system_authorization_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appSecret != null">
        app_secret,
      </if>
      <if test="accessToken != null">
        access_token,
      </if>
      <if test="requestUrl != null">
        request_url,
      </if>
      <if test="redirectUrl != null">
        redirect_url,
      </if>
      <if test="validateTime != null">
        validate_time,
      </if>
      <if test="enabled != null">
        enabled,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="validateTime != null">
        #{validateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enabled != null">
        #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemAuthorizationInfoExample" resultType="java.lang.Long">
    select count(*) from system_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_authorization_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.appSecret != null">
        app_secret = #{record.appSecret,jdbcType=VARCHAR},
      </if>
      <if test="record.accessToken != null">
        access_token = #{record.accessToken,jdbcType=VARCHAR},
      </if>
      <if test="record.requestUrl != null">
        request_url = #{record.requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.redirectUrl != null">
        redirect_url = #{record.redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.validateTime != null">
        validate_time = #{record.validateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enabled != null">
        enabled = #{record.enabled,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createUserId != null">
        create_user_id = #{record.createUserId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_authorization_info
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      app_id = #{record.appId,jdbcType=VARCHAR},
      app_secret = #{record.appSecret,jdbcType=VARCHAR},
      access_token = #{record.accessToken,jdbcType=VARCHAR},
      request_url = #{record.requestUrl,jdbcType=VARCHAR},
      redirect_url = #{record.redirectUrl,jdbcType=VARCHAR},
      validate_time = #{record.validateTime,jdbcType=TIMESTAMP},
      enabled = #{record.enabled,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_user_id = #{record.createUserId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemAuthorizationInfo">
    update system_authorization_info
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        app_secret = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="accessToken != null">
        access_token = #{accessToken,jdbcType=VARCHAR},
      </if>
      <if test="requestUrl != null">
        request_url = #{requestUrl,jdbcType=VARCHAR},
      </if>
      <if test="redirectUrl != null">
        redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      </if>
      <if test="validateTime != null">
        validate_time = #{validateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enabled != null">
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemAuthorizationInfo">
    update system_authorization_info
    set name = #{name,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      app_secret = #{appSecret,jdbcType=VARCHAR},
      access_token = #{accessToken,jdbcType=VARCHAR},
      request_url = #{requestUrl,jdbcType=VARCHAR},
      redirect_url = #{redirectUrl,jdbcType=VARCHAR},
      validate_time = #{validateTime,jdbcType=TIMESTAMP},
      enabled = #{enabled,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>