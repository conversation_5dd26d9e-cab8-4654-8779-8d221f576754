<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemOrgInfoMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemOrgInfo">
    <id column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="a_name1" jdbcType="VARCHAR" property="aName1" />
    <result column="a_name2" jdbcType="VARCHAR" property="aName2" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="zip" jdbcType="VARCHAR" property="zip" />
    <result column="passwd" jdbcType="VARCHAR" property="passwd" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="org_type" jdbcType="VARCHAR" property="orgType" />
    <result column="org_code" jdbcType="VARCHAR" property="orgCode" />
    <result column="province_code" jdbcType="BIGINT" property="provinceCode" />
    <result column="city_code" jdbcType="BIGINT" property="cityCode" />
    <result column="county_code" jdbcType="BIGINT" property="countyCode" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="is_auth" jdbcType="INTEGER" property="isAuth" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    org_id, org_name, a_name1, a_name2, contact, tel, address, zip, passwd, operator, 
    status, org_type, org_code, province_code, city_code, county_code, region_code, is_auth, 
    create_time, update_time, tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemOrgInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_org_info
    where org_id = #{orgId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_org_info
    where org_id = #{orgId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemOrgInfoExample">
    delete from system_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemOrgInfo">
    insert into system_org_info (org_id, org_name, a_name1, 
      a_name2, contact, tel, 
      address, zip, passwd, 
      operator, status, org_type, 
      org_code, province_code, city_code, 
      county_code, region_code, is_auth, 
      create_time, update_time, tenant_id, 
      platform_id)
    values (#{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{aName1,jdbcType=VARCHAR}, 
      #{aName2,jdbcType=VARCHAR}, #{contact,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{zip,jdbcType=VARCHAR}, #{passwd,jdbcType=VARCHAR}, 
      #{operator,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{orgType,jdbcType=VARCHAR}, 
      #{orgCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=BIGINT}, #{cityCode,jdbcType=BIGINT}, 
      #{countyCode,jdbcType=BIGINT}, #{regionCode,jdbcType=VARCHAR}, #{isAuth,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemOrgInfo">
    insert into system_org_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="aName1 != null">
        a_name1,
      </if>
      <if test="aName2 != null">
        a_name2,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="zip != null">
        zip,
      </if>
      <if test="passwd != null">
        passwd,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orgType != null">
        org_type,
      </if>
      <if test="orgCode != null">
        org_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="countyCode != null">
        county_code,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="isAuth != null">
        is_auth,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="aName1 != null">
        #{aName1,jdbcType=VARCHAR},
      </if>
      <if test="aName2 != null">
        #{aName2,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="zip != null">
        #{zip,jdbcType=VARCHAR},
      </if>
      <if test="passwd != null">
        #{passwd,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=BIGINT},
      </if>
      <if test="countyCode != null">
        #{countyCode,jdbcType=BIGINT},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="isAuth != null">
        #{isAuth,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemOrgInfoExample" resultType="java.lang.Long">
    select count(*) from system_org_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_org_info
    <set>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.aName1 != null">
        a_name1 = #{record.aName1,jdbcType=VARCHAR},
      </if>
      <if test="record.aName2 != null">
        a_name2 = #{record.aName2,jdbcType=VARCHAR},
      </if>
      <if test="record.contact != null">
        contact = #{record.contact,jdbcType=VARCHAR},
      </if>
      <if test="record.tel != null">
        tel = #{record.tel,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.zip != null">
        zip = #{record.zip,jdbcType=VARCHAR},
      </if>
      <if test="record.passwd != null">
        passwd = #{record.passwd,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.orgType != null">
        org_type = #{record.orgType,jdbcType=VARCHAR},
      </if>
      <if test="record.orgCode != null">
        org_code = #{record.orgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=BIGINT},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=BIGINT},
      </if>
      <if test="record.countyCode != null">
        county_code = #{record.countyCode,jdbcType=BIGINT},
      </if>
      <if test="record.regionCode != null">
        region_code = #{record.regionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isAuth != null">
        is_auth = #{record.isAuth,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_org_info
    set org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      a_name1 = #{record.aName1,jdbcType=VARCHAR},
      a_name2 = #{record.aName2,jdbcType=VARCHAR},
      contact = #{record.contact,jdbcType=VARCHAR},
      tel = #{record.tel,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      zip = #{record.zip,jdbcType=VARCHAR},
      passwd = #{record.passwd,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      org_type = #{record.orgType,jdbcType=VARCHAR},
      org_code = #{record.orgCode,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=BIGINT},
      city_code = #{record.cityCode,jdbcType=BIGINT},
      county_code = #{record.countyCode,jdbcType=BIGINT},
      region_code = #{record.regionCode,jdbcType=VARCHAR},
      is_auth = #{record.isAuth,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemOrgInfo">
    update system_org_info
    <set>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="aName1 != null">
        a_name1 = #{aName1,jdbcType=VARCHAR},
      </if>
      <if test="aName2 != null">
        a_name2 = #{aName2,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        tel = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="zip != null">
        zip = #{zip,jdbcType=VARCHAR},
      </if>
      <if test="passwd != null">
        passwd = #{passwd,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="orgType != null">
        org_type = #{orgType,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        org_code = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=BIGINT},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=BIGINT},
      </if>
      <if test="countyCode != null">
        county_code = #{countyCode,jdbcType=BIGINT},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="isAuth != null">
        is_auth = #{isAuth,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where org_id = #{orgId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemOrgInfo">
    update system_org_info
    set org_name = #{orgName,jdbcType=VARCHAR},
      a_name1 = #{aName1,jdbcType=VARCHAR},
      a_name2 = #{aName2,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      zip = #{zip,jdbcType=VARCHAR},
      passwd = #{passwd,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      org_type = #{orgType,jdbcType=VARCHAR},
      org_code = #{orgCode,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=BIGINT},
      city_code = #{cityCode,jdbcType=BIGINT},
      county_code = #{countyCode,jdbcType=BIGINT},
      region_code = #{regionCode,jdbcType=VARCHAR},
      is_auth = #{isAuth,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where org_id = #{orgId,jdbcType=BIGINT}
  </update>


  <!--分页查询系统中心列表-->
  <select id="selectSystemOrgListForPage" parameterType="com.haoys.user.domain.entity.SystemOrgInfoQuery" resultType="com.haoys.user.domain.vo.system.SystemOrgInfoVo">
    select soi.*,CONCAT(t1.city_name,t2.city_name,t3.city_name) city,dict.name org_level,t1.city_name provinceName,t2.city_name cityName,t3.city_name countyName,soi.org_name value
    from system_org_info soi
    left join system_area t1 on soi.province_code = t1.id
    left join system_area t2 on soi.city_code = t2.id
    left join system_area t3 on soi.county_code = t3.id
    left join dictionary dict on soi.org_type = dict.code
    <where>
      <if test="orgName !=null and orgName !='' ">
        and (org_name like concat('%', #{orgName}, '%') or a_name1 like concat('%', #{orgName}, '%') )
      </if>
      <if test="provinceCode != null and provinceCode != ''">
        and soi.province_code = #{provinceCode}
      </if>
      <if test="cityCode != null and cityCode != ''">
        and soi.city_code = #{cityCode}
      </if>
      <if test="countyCode != null and countyCode != ''">
        and soi.county_code = #{countyCode}
      </if>
      <if test="status != null and status != ''">
        and soi.status = #{status}
      </if>
    </where>
    order by soi.create_time desc
  </select>

  <select id="getSysOrgInfoByOrgName" resultType="com.haoys.user.domain.vo.system.SystemOrgInfoVo">
    select
    <include refid="Base_Column_List" />
    from system_org_info
    <where>
      <if test="orgName !=null and orgName !='' ">
        and org_name = #{orgName} LIMIT 1
      </if>
    </where>
  </select>

  <insert id="insertBatchSysOrgInfo">
    insert into system_org_info(org_id,org_name,org_code,province_code,city_code,county_code,org_type,create_time) values
    <foreach item="item" index="index" collection="list" separator=",">
      (#{item.orgId},#{item.orgName},#{item.orgCode},#{item.provinceCode},#{item.cityCode},#{item.countyCode},#{item.orgType},#{item.createTime})
    </foreach>
  </insert>


    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from system_org_info
        where org_id = #{orgId,jdbcType=BIGINT}
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
    delete from system_org_info
    where org_id = #{orgId,jdbcType=BIGINT}
  </delete>

    <select id="getMaxOrgCode" resultType="java.lang.String">
    SELECT MAX(CONVERT(org_code,SIGNED)) orgCode FROM system_org_info
  </select>

    <select id="getSysOrgInfoByAreaOrgName" resultMap="BaseResultMap">
        select * from system_org_info where province_code =#{provinceCode} and city_code =#{cityCode} and county_code =#{countyCode} and org_name =#{orgName} limit 1
  </select>


</mapper>