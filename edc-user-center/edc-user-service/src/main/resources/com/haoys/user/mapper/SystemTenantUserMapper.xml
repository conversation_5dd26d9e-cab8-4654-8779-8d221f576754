<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemTenantUserMapper">
  <resultMap id="BaseResultMap" type="com.haoys.user.model.SystemTenantUser">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="system_account" jdbcType="BIT" property="systemAccount" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="lock_status" jdbcType="BIT" property="lockStatus" />
    <result column="lock_time" jdbcType="BIGINT" property="lockTime" />
    <result column="active_status" jdbcType="BIT" property="activeStatus" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="enterprise" jdbcType="VARCHAR" property="enterprise" />
    <result column="positional" jdbcType="VARCHAR" property="positional" />
    <result column="company_owner_user" jdbcType="BIT" property="companyOwnerUser" />
    <result column="owner_total_auth" jdbcType="BIT" property="ownerTotalAuth" />
    <result column="data_from" jdbcType="VARCHAR" property="dataFrom" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="platform_id" jdbcType="VARCHAR" property="platformId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, system_account, status, lock_status, lock_time, active_status, department, 
    enterprise, positional, company_owner_user, owner_total_auth, data_from, create_time, 
    tenant_id, platform_id
  </sql>
  <select id="selectByExample" parameterType="com.haoys.user.model.SystemTenantUserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from system_tenant_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_tenant_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from system_tenant_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.haoys.user.model.SystemTenantUserExample">
    delete from system_tenant_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.haoys.user.model.SystemTenantUser">
    insert into system_tenant_user (id, user_id, system_account, 
      status, lock_status, lock_time, 
      active_status, department, enterprise, 
      positional, company_owner_user, owner_total_auth, 
      data_from, create_time, tenant_id, 
      platform_id)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{systemAccount,jdbcType=BIT}, 
      #{status,jdbcType=VARCHAR}, #{lockStatus,jdbcType=BIT}, #{lockTime,jdbcType=BIGINT}, 
      #{activeStatus,jdbcType=BIT}, #{department,jdbcType=VARCHAR}, #{enterprise,jdbcType=VARCHAR}, 
      #{positional,jdbcType=VARCHAR}, #{companyOwnerUser,jdbcType=BIT}, #{ownerTotalAuth,jdbcType=BIT}, 
      #{dataFrom,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.haoys.user.model.SystemTenantUser">
    insert into system_tenant_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="systemAccount != null">
        system_account,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lockStatus != null">
        lock_status,
      </if>
      <if test="lockTime != null">
        lock_time,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="enterprise != null">
        enterprise,
      </if>
      <if test="positional != null">
        positional,
      </if>
      <if test="companyOwnerUser != null">
        company_owner_user,
      </if>
      <if test="ownerTotalAuth != null">
        owner_total_auth,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="systemAccount != null">
        #{systemAccount,jdbcType=BIT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStatus != null">
        #{lockStatus,jdbcType=BIT},
      </if>
      <if test="lockTime != null">
        #{lockTime,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=BIT},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="enterprise != null">
        #{enterprise,jdbcType=VARCHAR},
      </if>
      <if test="positional != null">
        #{positional,jdbcType=VARCHAR},
      </if>
      <if test="companyOwnerUser != null">
        #{companyOwnerUser,jdbcType=BIT},
      </if>
      <if test="ownerTotalAuth != null">
        #{ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.haoys.user.model.SystemTenantUserExample" resultType="java.lang.Long">
    select count(*) from system_tenant_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update system_tenant_user
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.systemAccount != null">
        system_account = #{record.systemAccount,jdbcType=BIT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.lockStatus != null">
        lock_status = #{record.lockStatus,jdbcType=BIT},
      </if>
      <if test="record.lockTime != null">
        lock_time = #{record.lockTime,jdbcType=BIGINT},
      </if>
      <if test="record.activeStatus != null">
        active_status = #{record.activeStatus,jdbcType=BIT},
      </if>
      <if test="record.department != null">
        department = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.enterprise != null">
        enterprise = #{record.enterprise,jdbcType=VARCHAR},
      </if>
      <if test="record.positional != null">
        positional = #{record.positional,jdbcType=VARCHAR},
      </if>
      <if test="record.companyOwnerUser != null">
        company_owner_user = #{record.companyOwnerUser,jdbcType=BIT},
      </if>
      <if test="record.ownerTotalAuth != null">
        owner_total_auth = #{record.ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update system_tenant_user
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=BIGINT},
      system_account = #{record.systemAccount,jdbcType=BIT},
      status = #{record.status,jdbcType=VARCHAR},
      lock_status = #{record.lockStatus,jdbcType=BIT},
      lock_time = #{record.lockTime,jdbcType=BIGINT},
      active_status = #{record.activeStatus,jdbcType=BIT},
      department = #{record.department,jdbcType=VARCHAR},
      enterprise = #{record.enterprise,jdbcType=VARCHAR},
      positional = #{record.positional,jdbcType=VARCHAR},
      company_owner_user = #{record.companyOwnerUser,jdbcType=BIT},
      owner_total_auth = #{record.ownerTotalAuth,jdbcType=BIT},
      data_from = #{record.dataFrom,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.haoys.user.model.SystemTenantUser">
    update system_tenant_user
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="systemAccount != null">
        system_account = #{systemAccount,jdbcType=BIT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="lockStatus != null">
        lock_status = #{lockStatus,jdbcType=BIT},
      </if>
      <if test="lockTime != null">
        lock_time = #{lockTime,jdbcType=BIGINT},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=BIT},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="enterprise != null">
        enterprise = #{enterprise,jdbcType=VARCHAR},
      </if>
      <if test="positional != null">
        positional = #{positional,jdbcType=VARCHAR},
      </if>
      <if test="companyOwnerUser != null">
        company_owner_user = #{companyOwnerUser,jdbcType=BIT},
      </if>
      <if test="ownerTotalAuth != null">
        owner_total_auth = #{ownerTotalAuth,jdbcType=BIT},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.haoys.user.model.SystemTenantUser">
    update system_tenant_user
    set user_id = #{userId,jdbcType=BIGINT},
      system_account = #{systemAccount,jdbcType=BIT},
      status = #{status,jdbcType=VARCHAR},
      lock_status = #{lockStatus,jdbcType=BIT},
      lock_time = #{lockTime,jdbcType=BIGINT},
      active_status = #{activeStatus,jdbcType=BIT},
      department = #{department,jdbcType=VARCHAR},
      enterprise = #{enterprise,jdbcType=VARCHAR},
      positional = #{positional,jdbcType=VARCHAR},
      company_owner_user = #{companyOwnerUser,jdbcType=BIT},
      owner_total_auth = #{ownerTotalAuth,jdbcType=BIT},
      data_from = #{dataFrom,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getSystemTenantUserByUserId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from system_tenant_user where user_id = #{userId} and tenant_id = #{tenantId} and platform_id = #{platformId}
  </select>

</mapper>