<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.haoys.user.mapper.SystemRequestRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="SystemRequestRecordResult" type="com.haoys.user.model.SystemRequestRecord">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="realName" column="real_name"/>
        <result property="sessionId" column="session_id"/>
        <result property="traceId" column="trace_id"/>
        <result property="requestName" column="request_name"/>
        <result property="requestMethod" column="request_method"/>
        <result property="requestUrl" column="request_url"/>
        <result property="methodName" column="method_name"/>
        <result property="requestParam" column="request_param"/>
        <result property="requestStartTime" column="request_start_time"/>
        <result property="requestEndTime" column="request_end_time"/>
        <result property="responseResult" column="response_result"/>
        <result property="responseTime" column="response_time"/>
        <result property="responseSize" column="response_size"/>
        <result property="httpStatus" column="http_status"/>
        <result property="isSuccess" column="is_success"/>
        <result property="requestIp" column="request_ip"/>
        <result property="location" column="location"/>
        <result property="userAgent" column="user_agent"/>
        <result property="referer" column="referer"/>
        <result property="browser" column="browser"/>
        <result property="operatingSystem" column="operating_system"/>
        <result property="deviceType" column="device_type"/>
        <result property="businessType" column="business_type"/>
        <result property="operatorType" column="operator_type"/>
        <result property="dataFrom" column="data_from"/>
        <result property="accessType" column="access_type"/>
        <result property="projectRecordLog" column="project_record_log"/>
        <result property="errorMessage" column="error_message"/>
        <result property="exceptionType" column="exception_type"/>
        <result property="exceptionStack" column="exception_stack"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, real_name, session_id, trace_id,
        request_name, request_method, request_url, method_name,
        request_param, request_start_time, request_end_time,
        response_result, response_time, response_size, http_status, is_success,
        request_ip, location, user_agent, referer,
        browser, operating_system, device_type,
        business_type, operator_type, data_from, access_type, project_record_log,
        error_message, exception_type, exception_stack,
        status, create_time, update_time
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.haoys.user.model.SystemRequestRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO system_request_record (
            user_id, user_name, real_name, session_id, trace_id,
            request_name, request_method, request_url, method_name,
            request_param, request_start_time, request_end_time,
            response_result, response_time, response_size, http_status, is_success,
            request_ip, location, user_agent, referer,
            browser, operating_system, device_type,
            business_type, operator_type, data_from, access_type, project_record_log,
            error_message, exception_type, exception_stack,
            status, create_time, update_time
        ) VALUES (
            #{userId}, #{userName}, #{realName}, #{sessionId}, #{traceId},
            #{requestName}, #{requestMethod}, #{requestUrl}, #{methodName},
            #{requestParam}, #{requestStartTime}, #{requestEndTime},
            #{responseResult}, #{responseTime}, #{responseSize}, #{httpStatus}, #{isSuccess},
            #{requestIp}, #{location}, #{userAgent}, #{referer},
            #{browser}, #{operatingSystem}, #{deviceType},
            #{businessType}, #{operatorType}, #{dataFrom}, #{accessType}, #{projectRecordLog},
            #{errorMessage}, #{exceptionType}, #{exceptionStack},
            #{status}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO system_request_record (
            user_id, user_name, real_name, session_id, trace_id,
            request_name, request_method, request_url, method_name,
            request_param, request_start_time, request_end_time,
            response_result, response_time, response_size, http_status, is_success,
            request_ip, location, user_agent, referer,
            browser, operating_system, device_type,
            business_type, operator_type, data_from, access_type, project_record_log,
            error_message, exception_type, exception_stack,
            status, create_time, update_time
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.userId}, #{record.userName}, #{record.realName}, #{record.sessionId}, #{record.traceId},
                #{record.requestName}, #{record.requestMethod}, #{record.requestUrl}, #{record.methodName},
                #{record.requestParam}, #{record.requestStartTime}, #{record.requestEndTime},
                #{record.responseResult}, #{record.responseTime}, #{record.responseSize}, #{record.httpStatus}, #{record.isSuccess},
                #{record.requestIp}, #{record.location}, #{record.userAgent}, #{record.referer},
                #{record.browser}, #{record.operatingSystem}, #{record.deviceType},
                #{record.businessType}, #{record.operatorType}, #{record.dataFrom}, #{record.accessType}, #{record.projectRecordLog},
                #{record.errorMessage}, #{record.exceptionType}, #{record.exceptionStack},
                #{record.status}, #{record.createTime}, #{record.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE id = #{id}
    </select>

    <!-- 根据链路追踪ID查询 -->
    <select id="selectByTraceId" parameterType="java.lang.String" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE trace_id = #{traceId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据会话ID查询 -->
    <select id="selectBySessionId" parameterType="java.lang.String" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE session_id = #{sessionId}
        ORDER BY create_time DESC
        LIMIT 100
    </select>

    <!-- 分页查询条件 -->
    <sql id="selectCondition">
        <where>
            <if test="userName != null and userName != ''">
                AND user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="requestUrl != null and requestUrl != ''">
                AND request_url LIKE CONCAT('%', #{requestUrl}, '%')
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
            <if test="isSuccess != null">
                AND is_success = #{isSuccess}
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectByPage" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        <include refid="selectCondition"/>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM system_request_record
        <include refid="selectCondition"/>
    </select>

    <!-- 根据用户查询 -->
    <select id="selectByUser" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        <where>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND user_name = #{userName}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据IP查询 -->
    <select id="selectByIp" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE request_ip = #{requestIp}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询异常日志 -->
    <select id="selectErrorLogs" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE is_success = 0
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询慢请求日志 -->
    <select id="selectSlowLogs" resultMap="SystemRequestRecordResult">
        SELECT <include refid="Base_Column_List"/>
        FROM system_request_record
        WHERE response_time >= #{minResponseTime}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY response_time DESC
        LIMIT #{limit}
    </select>

    <!-- 访问量统计 -->
    <select id="selectAccessStatistics" resultType="java.util.Map">
        SELECT 
            <choose>
                <when test="groupBy == 'hour'">
                    DATE_FORMAT(create_time, '%Y-%m-%d %H:00:00') as time_period
                </when>
                <when test="groupBy == 'day'">
                    DATE_FORMAT(create_time, '%Y-%m-%d') as time_period
                </when>
                <when test="groupBy == 'month'">
                    DATE_FORMAT(create_time, '%Y-%m') as time_period
                </when>
                <otherwise>
                    DATE_FORMAT(create_time, '%Y-%m-%d') as time_period
                </otherwise>
            </choose>,
            COUNT(*) as total_count,
            COUNT(CASE WHEN is_success = 1 THEN 1 END) as success_count,
            COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
            AVG(response_time) as avg_response_time,
            COUNT(DISTINCT user_name) as unique_users
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY time_period
        ORDER BY time_period
    </select>

    <!-- 热点URL统计 -->
    <select id="selectHotUrls" resultType="java.util.Map">
        SELECT 
            request_url,
            request_method,
            COUNT(*) as request_count,
            AVG(response_time) as avg_response_time,
            COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
            ROUND(COUNT(CASE WHEN is_success = 0 THEN 1 END) * 100.0 / COUNT(*), 2) as error_rate
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY request_url, request_method
        HAVING request_count > 1
        ORDER BY request_count DESC
        LIMIT #{limit}
    </select>

    <!-- 用户访问统计 -->
    <select id="selectUserStatistics" resultType="java.util.Map">
        SELECT 
            user_name,
            real_name,
            COUNT(*) as request_count,
            AVG(response_time) as avg_response_time,
            COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
            COUNT(DISTINCT request_ip) as ip_count,
            MAX(create_time) as last_access_time
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND user_name IS NOT NULL
        GROUP BY user_name, real_name
        ORDER BY request_count DESC
        LIMIT #{limit}
    </select>

    <!-- IP访问统计 -->
    <select id="selectIpStatistics" resultType="java.util.Map">
        SELECT 
            request_ip,
            location,
            COUNT(*) as request_count,
            COUNT(DISTINCT user_name) as user_count,
            COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_count,
            MAX(create_time) as last_access_time
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY request_ip, location
        ORDER BY request_count DESC
        LIMIT #{limit}
    </select>

    <!-- 响应时间分布统计 -->
    <select id="selectResponseTimeDistribution" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN response_time &lt; 100 THEN '&lt;100ms'
                WHEN response_time &lt; 500 THEN '100-500ms'
                WHEN response_time &lt; 1000 THEN '500ms-1s'
                WHEN response_time &lt; 3000 THEN '1-3s'
                WHEN response_time &lt; 5000 THEN '3-5s'
                ELSE '&gt;5s'
            END as time_range,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM system_request_record WHERE create_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND response_time IS NOT NULL
        GROUP BY time_range
        ORDER BY 
            CASE 
                WHEN time_range = '&lt;100ms' THEN 1
                WHEN time_range = '100-500ms' THEN 2
                WHEN time_range = '500ms-1s' THEN 3
                WHEN time_range = '1-3s' THEN 4
                WHEN time_range = '3-5s' THEN 5
                ELSE 6
            END
    </select>

    <!-- 设备类型分布统计 -->
    <select id="selectDeviceTypeDistribution" resultType="java.util.Map">
        SELECT 
            device_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM system_request_record WHERE create_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY device_type
        ORDER BY count DESC
    </select>

    <!-- 浏览器分布统计 -->
    <select id="selectBrowserDistribution" resultType="java.util.Map">
        SELECT 
            browser,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM system_request_record WHERE create_time BETWEEN #{startTime} AND #{endTime}), 2) as percentage
        FROM system_request_record
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND browser IS NOT NULL
        GROUP BY browser
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 删除过期记录 -->
    <delete id="deleteByCreateTimeBefore" parameterType="java.util.Date">
        DELETE FROM system_request_record
        WHERE create_time &lt; #{beforeDate}
    </delete>

    <!-- 根据ID列表删除 -->
    <delete id="deleteByIds" parameterType="java.util.List">
        DELETE FROM system_request_record
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 清空所有记录 -->
    <delete id="deleteAll">
        DELETE FROM system_request_record
    </delete>

    <!-- 获取表统计信息 -->
    <select id="getTableStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as total_records,
            COUNT(CASE WHEN is_success = 1 THEN 1 END) as success_records,
            COUNT(CASE WHEN is_success = 0 THEN 1 END) as error_records,
            AVG(response_time) as avg_response_time,
            MAX(response_time) as max_response_time,
            MIN(create_time) as earliest_record,
            MAX(create_time) as latest_record,
            COUNT(DISTINCT user_name) as unique_users,
            COUNT(DISTINCT request_ip) as unique_ips
        FROM system_request_record
    </select>

    <!-- 优化表 -->
    <update id="optimizeTable">
        OPTIMIZE TABLE system_request_record
    </update>

</mapper>
